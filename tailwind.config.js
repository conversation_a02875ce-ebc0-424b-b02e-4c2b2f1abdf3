const { heroui } = require("@heroui/theme");

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx,mdx}",
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/framesound-ui/**/*.{js,ts,jsx,tsx}",
    // HeroUI components
    "./node_modules/@heroui/theme/dist/components/toast.js",
    "./node_modules/@heroui/theme/dist/components/button.js",
    "./node_modules/@heroui/theme/dist/components/popover.js",
    "./node_modules/@heroui/theme/dist/components/tooltip.js",
    // or you can use a glob pattern (multiple component styles)
    "./node_modules/@heroui/theme/dist/components/(toast|button|snippet|code|input|popover|tooltip).js",
  ],
  theme: {
    screens: {
      xxs: "375px", // 22.5rem
      xs: "430px", // 33.75rem
      sm: "560px", // 40rem
      md: "768px", // 48rem
      lg: "1024px", // 64rem
      xl: "1280px", // 80rem
      "1.5xl": "1440px",
      "2xl": "1920px", // 114rem
      "2.5xl": "2280px",
      "3xl": "2560px",
    },
    extend: {
      fontFamily: {
        sans: ["Noto Sans SC", "sans-serif"],
        serif: ["Noto Serif SC", "serif"],
      },
      borderRadius: {
        xs: "4px",
        sm: "6px",
        md: "8px",
        lg: "10px",
        xl: "12px",
      },
      animation: {
        "spin-slow": "spin 3s linear infinite",
        "pulse-slow": "pulse 6s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        ripple: "ripple 0.6s linear",
        "toast-in": "toast-in 0.3s ease-out forwards",
        "toast-out": "toast-out 0.2s ease-in forwards",
      },
      keyframes: {
        "toast-in": {
          "0%": {
            opacity: "0",
            transform: "translateY(-8px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
        "toast-out": {
          "0%": {
            opacity: "1",
            transform: "translateY(0)",
          },
          "100%": {
            opacity: "0",
            transform: "translateY(-8px)",
          },
        },
      },
      typography: {
        DEFAULT: {
          css: {
            maxWidth: "100%",
            color: "inherit",
            a: {
              color: "inherit",
              textDecoration: "underline",
              fontWeight: "500",
            },
            strong: {
              color: "inherit",
              fontWeight: "600",
            },
            h1: {
              color: "inherit",
            },
            h2: {
              color: "inherit",
            },
            h3: {
              color: "inherit",
            },
            h4: {
              color: "inherit",
            },
            code: {
              color: "inherit",
              fontWeight: "400",
            },
            "code::before": {
              content: '""',
            },
            "code::after": {
              content: '""',
            },
            "blockquote p:first-of-type::before": {
              content: '""',
            },
            "blockquote p:last-of-type::after": {
              content: '""',
            },
            blockquote: {
              color: "inherit",
            },
          },
        },
      },
    },
  },
  darkMode: "class",
  plugins: [
    require("@tailwindcss/typography"),
    heroui({
      // 配置 HeroUI 的主题和颜色
      themes: {
        light: {
          colors: {
            branding: "#4034BA", // 品牌色
            background: "#F9F8F7",
            backgroundDeep: "#FFFFFF",
            foreground: "#2F2F2F",
            divider: "#E8E8E8",
            focus: "#4F46E5",
            content1: "#FFFFFF",
            content2: "#F8F8F8",
            content3: "#F0F0F0",
            content4: "#E8E8E8",
            card: {
              DEFAULT: "#EBE9E7", // 卡片背景色
              card1: "#F6F4FF",
              card2: "#EBFFFC",
              card3: "#EDF8FF",
              hover: "#DFDDDA", // 卡片悬停背景色
              gradientFrom: "#EEEDFF", // 功能亮点卡片渐变起始色
              gradientTo: "#F6F6FF", // 功能亮点卡片渐变结束色
              securityGradientFrom: "#E5F0FF", // 数据安全区块渐变起始色
              securityGradientTo: "#F7FAFF", // 数据安全区块渐变结束色
            },
            floatingCard: {
              DEFAULT: "#EEEDFF", // 浮窗卡片背景色
              text: "#443AA7", // 浮窗卡片文本颜色
            },
            CardTags: {
              DEFAULT: "#FBDDC1",
              text: "#936125",
            },
            cardButton: {
              DEFAULT: "#D3D0CC",
            },
            consultButton: {
              gradientStart: "#7668FC",
              gradientEnd: "#4034ba",
            },
            primary: {
              50: "#EEF2FF",
              100: "#E0E7FF",
              200: "#C7D2FE",
              300: "#A5B4FC",
              400: "#818CF8",
              500: "#6366F1",
              600: "#4F46E5",
              700: "#4338CA",
              800: "#3730A3",
              900: "#312E81",
              DEFAULT: "#4034ba",
              foreground: "#FFFFFF",
            },
            secondary: {
              50: "#F5F3FF",
              100: "#EDE9FE",
              200: "#DDD6FE",
              300: "#C4B5FD",
              400: "#A78BFA",
              500: "#8B5CF6",
              600: "#7C3AED",
              700: "#6D28D9",
              800: "#5B21B6",
              900: "#4C1D95",
              DEFAULT: "#8B5CF6",
              foreground: "#FFFFFF",
            },
            success: {
              50: "#ECFDF5",
              100: "#D1FAE5",
              200: "#A7F3D0",
              300: "#6EE7B7",
              400: "#34D399",
              500: "#10B981",
              600: "#059669",
              700: "#047857",
              800: "#065F46",
              900: "#064E3B",
              DEFAULT: "#10B981",
              foreground: "#FFFFFF",
            },
            warning: {
              50: "#FFFBEB",
              100: "#FEF3C7",
              200: "#FDE68A",
              300: "#FCD34D",
              400: "#FBBF24",
              500: "#F59E0B",
              600: "#D97706",
              700: "#B45309",
              800: "#92400E",
              900: "#78350F",
              DEFAULT: "#F59E0B",
              foreground: "#FFFFFF",
            },
            danger: {
              50: "#FEF2F2",
              100: "#FEE2E2",
              200: "#FECACA",
              300: "#FCA5A5",
              400: "#F87171",
              500: "#EF4444",
              600: "#DC2626",
              700: "#B91C1C",
              800: "#991B1B",
              900: "#7F1D1D",
              DEFAULT: "#EF4444",
              foreground: "#FFFFFF",
            },
            default: {
              50: "#F9FAFB",
              100: "#F3F4F6",
              200: "#E5E7EB",
              300: "#D1D5DB",
              400: "#9CA3AF",
              500: "#6B7280",
              600: "#4B5563",
              700: "#374151",
              800: "#1F2937",
              900: "#111827",
              DEFAULT: "#6B7280",
              foreground: "#FFFFFF",
            },
            pricing: {
              gradientStart: "#F0F0FF",
              gradientEnd: "#FFFFFF",
            },
            pageHeader: {
              gradientStart: "#EEEDFF", // 页面顶部模块渐变起始色
              gradientEnd: "#FFFFFF", // 页面顶部模块渐变结束色
            },
            enterprise: {
              button: "#1D1814",
              buttonText: "#F0E8DD",
            },
          },
        },
        dark: {
          colors: {
            branding: "#4034BA",
            background: "#020612",
            backgroundDeep: "#171614",
            foreground: "#FFFFFF",
            divider: "#2D2D2D",
            focus: "#6366F1",
            content1: "#161616",
            content2: "#1C1C1C",
            content3: "#232323",
            content4: "#2D2D2D",
            card: {
              DEFAULT: "#1C1F26", // 卡片背景色
              hover: "#252A34", // 卡片悬停背景色
              gradientFrom: "#2D2D45", // 功能亮点卡片渐变起始色（深色模式）
              gradientTo: "#333345", // 功能亮点卡片渐变结束色（深色模式）
              securityGradientFrom: "#1A2234", // 数据安全区块渐变起始色（深色模式）
              securityGradientTo: "#1E2532", // 数据安全区块渐变结束色（深色模式）
            },
            floatingCard: {
              DEFAULT: "#2D2D45", // 浮窗卡片背景色（深色模式）
              text: "#A5B4FC", // 浮窗卡片文本颜色（深色模式）
            },
            cardButton: {
              DEFAULT: "#3D424C",
            },
            consultButton: {
              gradientStart: "#7668FC",
              gradientEnd: "#443AA7",
            },
            primary: {
              50: "#EEF2FF",
              100: "#E0E7FF",
              200: "#C7D2FE",
              300: "#A5B4FC",
              400: "#818CF8",
              500: "#6366F1",
              600: "#4F46E5",
              700: "#4338CA",
              800: "#3730A3",
              900: "#312E81",
              DEFAULT: "#443AA7",
              foreground: "#FFFFFF",
            },
            secondary: {
              50: "#F5F3FF",
              100: "#EDE9FE",
              200: "#DDD6FE",
              300: "#C4B5FD",
              400: "#A78BFA",
              500: "#8B5CF6",
              600: "#7C3AED",
              700: "#6D28D9",
              800: "#5B21B6",
              900: "#4C1D95",
              DEFAULT: "#8B5CF6",
              foreground: "#FFFFFF",
            },
            success: {
              50: "#ECFDF5",
              100: "#D1FAE5",
              200: "#A7F3D0",
              300: "#6EE7B7",
              400: "#34D399",
              500: "#10B981",
              600: "#059669",
              700: "#047857",
              800: "#065F46",
              900: "#064E3B",
              DEFAULT: "#10B981",
              foreground: "#FFFFFF",
            },
            warning: {
              50: "#FFFBEB",
              100: "#FEF3C7",
              200: "#FDE68A",
              300: "#FCD34D",
              400: "#FBBF24",
              500: "#F59E0B",
              600: "#D97706",
              700: "#B45309",
              800: "#92400E",
              900: "#78350F",
              DEFAULT: "#F59E0B",
              foreground: "#FFFFFF",
            },
            danger: {
              50: "#FEF2F2",
              100: "#FEE2E2",
              200: "#FECACA",
              300: "#FCA5A5",
              400: "#F87171",
              500: "#EF4444",
              600: "#DC2626",
              700: "#B91C1C",
              800: "#991B1B",
              900: "#7F1D1D",
              DEFAULT: "#EF4444",
              foreground: "#FFFFFF",
            },
            default: {
              50: "#F9FAFB",
              100: "#F3F4F6",
              200: "#E5E7EB",
              300: "#D1D5DB",
              400: "#9CA3AF",
              500: "#6B7280",
              600: "#4B5563",
              700: "#374151",
              800: "#1F2937",
              900: "#111827",
              DEFAULT: "#6B7280",
              foreground: "#FFFFFF",
            },
            pricing: {
              gradientStart: "#F0F0FF",
              gradientEnd: "#101010", // 深色模式下使用深色结束色
            },
            pageHeader: {
              gradientStart: "#2D2D45", // 深色模式下页面顶部模块渐变起始色
              gradientEnd: "#161616", // 深色模式下页面顶部模块渐变结束色
            },
            enterprise: {
              button: "#1D1814",
              buttonText: "#F0E8DD",
            },
          },
        },
      },
      // 添加通用颜色
      addCommonColors: true,
    }),
    function ({ addUtilities }) {
      addUtilities({
        ".scrollbar-none": {
          "-ms-overflow-style": "none",
          "scrollbar-width": "none",
          "&::-webkit-scrollbar": {
            display: "none",
          },
        },
      });
    },
  ],
};
