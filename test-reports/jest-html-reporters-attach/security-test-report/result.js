window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":1,"numPassedTests":17,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":17,"startTime":1753174520772,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":17,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1753174521259,"loadTestEnvironmentEnd":1753174521049,"loadTestEnvironmentStart":1753174520780,"runtime":210,"setupAfterEnvEnd":1753174521199,"setupAfterEnvStart":1753174521109,"setupFilesEnd":1753174521049,"setupFilesStart":1753174521049,"slow":false,"start":1753174521049},"testFilePath":"/Users/<USER>/GitHub/quote-official/src/utils/__tests__/auth.security.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["重定向安全验证测试","✅ 合法重定向URL测试"],"duration":1,"failureMessages":[],"fullName":"重定向安全验证测试 ✅ 合法重定向URL测试 应该允许相对路径","status":"passed","title":"应该允许相对路径"},{"ancestorTitles":["重定向安全验证测试","✅ 合法重定向URL测试"],"duration":1,"failureMessages":[],"fullName":"重定向安全验证测试 ✅ 合法重定向URL测试 应该允许当前域名的绝对URL","status":"passed","title":"应该允许当前域名的绝对URL"},{"ancestorTitles":["重定向安全验证测试","✅ 合法重定向URL测试"],"duration":1,"failureMessages":[],"fullName":"重定向安全验证测试 ✅ 合法重定向URL测试 应该允许配置的允许域名","status":"passed","title":"应该允许配置的允许域名"},{"ancestorTitles":["重定向安全验证测试","✅ 合法重定向URL测试"],"duration":1,"failureMessages":[],"fullName":"重定向安全验证测试 ✅ 合法重定向URL测试 应该支持子域名匹配（以.开头的域名）","status":"passed","title":"应该支持子域名匹配（以.开头的域名）"},{"ancestorTitles":["重定向安全验证测试","🚨 恶意重定向URL测试"],"duration":15,"failureMessages":[],"fullName":"重定向安全验证测试 🚨 恶意重定向URL测试 应该拒绝路径遍历攻击","status":"passed","title":"应该拒绝路径遍历攻击"},{"ancestorTitles":["重定向安全验证测试","🚨 恶意重定向URL测试"],"duration":8,"failureMessages":[],"fullName":"重定向安全验证测试 🚨 恶意重定向URL测试 应该拒绝双斜杠绕过","status":"passed","title":"应该拒绝双斜杠绕过"},{"ancestorTitles":["重定向安全验证测试","🚨 恶意重定向URL测试"],"duration":1,"failureMessages":[],"fullName":"重定向安全验证测试 🚨 恶意重定向URL测试 应该拒绝未授权的外部域名","status":"passed","title":"应该拒绝未授权的外部域名"},{"ancestorTitles":["重定向安全验证测试","🚨 恶意重定向URL测试"],"duration":1,"failureMessages":[],"fullName":"重定向安全验证测试 🚨 恶意重定向URL测试 应该拒绝危险协议","status":"passed","title":"应该拒绝危险协议"},{"ancestorTitles":["重定向安全验证测试","🚨 恶意重定向URL测试"],"duration":2,"failureMessages":[],"fullName":"重定向安全验证测试 🚨 恶意重定向URL测试 应该拒绝URL编码绕过尝试","status":"passed","title":"应该拒绝URL编码绕过尝试"},{"ancestorTitles":["重定向安全验证测试","🚨 恶意重定向URL测试"],"duration":1,"failureMessages":[],"fullName":"重定向安全验证测试 🚨 恶意重定向URL测试 应该拒绝子域名欺骗","status":"passed","title":"应该拒绝子域名欺骗"},{"ancestorTitles":["重定向安全验证测试","🔍 边界情况测试"],"duration":5,"failureMessages":[],"fullName":"重定向安全验证测试 🔍 边界情况测试 应该处理空字符串和null","status":"passed","title":"应该处理空字符串和null"},{"ancestorTitles":["重定向安全验证测试","🔍 边界情况测试"],"duration":8,"failureMessages":[],"fullName":"重定向安全验证测试 🔍 边界情况测试 应该处理格式错误的URL","status":"passed","title":"应该处理格式错误的URL"},{"ancestorTitles":["重定向安全验证测试","🔍 边界情况测试"],"duration":7,"failureMessages":[],"fullName":"重定向安全验证测试 🔍 边界情况测试 应该处理极长的URL","status":"passed","title":"应该处理极长的URL"},{"ancestorTitles":["重定向安全验证测试","🔍 边界情况测试"],"duration":2,"failureMessages":[],"fullName":"重定向安全验证测试 🔍 边界情况测试 应该处理Unicode和国际化域名","status":"passed","title":"应该处理Unicode和国际化域名"},{"ancestorTitles":["重定向安全验证测试","⚙️ 配置测试"],"duration":0,"failureMessages":[],"fullName":"重定向安全验证测试 ⚙️ 配置测试 getAllowedRedirectDomains 应该正确解析环境变量","status":"passed","title":"getAllowedRedirectDomains 应该正确解析环境变量"},{"ancestorTitles":["重定向安全验证测试","⚙️ 配置测试"],"duration":1,"failureMessages":[],"fullName":"重定向安全验证测试 ⚙️ 配置测试 应该处理空的环境变量","status":"passed","title":"应该处理空的环境变量"},{"ancestorTitles":["重定向安全验证测试","⚙️ 配置测试"],"duration":0,"failureMessages":[],"fullName":"重定向安全验证测试 ⚙️ 配置测试 应该过滤空白域名","status":"passed","title":"应该过滤空白域名"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":["src/**/*.{js,jsx,ts,tsx}","!src/**/*.d.ts","!src/app/**","!src/components/ui/**"],"coverageDirectory":"/Users/<USER>/GitHub/quote-official/coverage","coverageProvider":"babel","coverageReporters":["json","text","lcov","clover"],"coverageThreshold":{"global":{"branches":80,"functions":80,"lines":80,"statements":80},"src/utils/auth.ts":{"branches":95,"functions":95,"lines":95,"statements":95}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":11,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["/Users/<USER>/GitHub/quote-official/node_modules/jest-html-reporters/index.js",{"publicPath":"./test-reports","filename":"security-test-report.html","expand":true}]],"rootDir":"/Users/<USER>/GitHub/quote-official","runInBand":false,"runTestsByPath":false,"seed":**********,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":["auth.security.test.ts"],"type":"TestPathPatterns"},"testSequencer":"/Users/<USER>/GitHub/quote-official/node_modules/@jest/test-sequencer/build/index.js","testTimeout":10000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1753174521264,"_reporterOptions":{"publicPath":"./test-reports","filename":"security-test-report.html","expand":true,"pageTitle":"","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})