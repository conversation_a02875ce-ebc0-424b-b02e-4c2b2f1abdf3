// Jest 全局设置文件

// 扩展 Jest 匹配器
import "@testing-library/jest-dom";

// 模拟 Next.js 环境
global.fetch = require("jest-fetch-mock");

// 模拟 window 对象 - 不设置 location，让测试自己处理

// 模拟 localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// 模拟 console 方法以减少测试输出噪音
global.console = {
  ...console,
  // 保留 error 和 warn 用于调试
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
};

// 设置测试环境变量
process.env.NODE_ENV = "test";
process.env.NEXT_PUBLIC_API_BASE_URL = "http://localhost:3000";

// 全局测试工具函数
global.mockEnvironmentVariable = (key, value) => {
  const originalValue = process.env[key];
  process.env[key] = value;

  return () => {
    if (originalValue === undefined) {
      delete process.env[key];
    } else {
      process.env[key] = originalValue;
    }
  };
};

// 安全测试专用工具
global.securityTestUtils = {
  // 生成恶意 URL 列表
  generateMaliciousUrls: (baseUrl = "evil.com") => [
    `https://${baseUrl}/`,
    `http://${baseUrl}/malware`,
    `javascript:alert("xss")`,
    `data:text/html,<script>alert("xss")</script>`,
    `//evil.com`,
    `/${baseUrl}`,
  ],

  // 生成路径遍历攻击 URL
  generatePathTraversalUrls: () => [
    "/../../../etc/passwd",
    "/path/../../../admin",
    "\\..\\..\\windows\\system32",
    "/path\\..\\admin",
    "/.././.././etc/passwd",
  ],

  // 生成 URL 编码绕过尝试
  generateEncodedBypassUrls: () => [
    "https://<EMAIL>/",
    "https://trusted.com%2eevil.com/",
    "https://trusted.com%252eevil.com/",
    encodeURIComponent("https://evil.com/@trusted.com/"),
  ],
};

// 测试前后清理
beforeEach(() => {
  // 清理模拟函数
  jest.clearAllMocks();

  // 重置环境变量
  delete process.env.NEXT_PUBLIC_ALLOWED_REDIRECT_DOMAINS;
  delete process.env.NEXT_PUBLIC_SENSITIVE_HOSTNAMES;

  // 重置 localStorage 模拟
  localStorageMock.getItem.mockClear();
  localStorageMock.setItem.mockClear();
  localStorageMock.removeItem.mockClear();
  localStorageMock.clear.mockClear();
});

afterEach(() => {
  // 清理定时器
  jest.clearAllTimers();
});
