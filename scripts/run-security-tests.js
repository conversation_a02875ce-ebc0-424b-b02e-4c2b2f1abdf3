#!/usr/bin/env node

/**
 * 安全测试运行脚本
 * 自动化运行所有安全相关测试并生成报告
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔒 开始运行安全测试套件...\n');

// 测试配置
const testConfigs = [
  {
    name: '重定向安全测试',
    pattern: 'auth.security.test.ts',
    description: '测试重定向URL验证函数的安全性',
  },
  {
    name: '渗透测试模拟',
    pattern: 'auth.penetration.test.ts',
    description: '模拟真实攻击场景的渗透测试',
  },
];

// 运行测试的函数
function runTest(config) {
  console.log(`📋 运行 ${config.name}...`);
  console.log(`   ${config.description}`);
  
  try {
    const command = `npx jest --testPathPattern=${config.pattern} --verbose --coverage`;
    const output = execSync(command, { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    console.log(`✅ ${config.name} 通过\n`);
    return { success: true, output };
  } catch (error) {
    console.log(`❌ ${config.name} 失败`);
    console.log(`错误信息: ${error.message}\n`);
    return { success: false, error: error.message, output: error.stdout };
  }
}

// 生成安全测试报告
function generateSecurityReport(results) {
  const timestamp = new Date().toISOString();
  const reportDir = path.join(process.cwd(), 'security-reports');
  
  // 确保报告目录存在
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  const report = {
    timestamp,
    summary: {
      total: results.length,
      passed: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
    },
    tests: results.map((result, index) => ({
      name: testConfigs[index].name,
      description: testConfigs[index].description,
      status: result.success ? 'PASSED' : 'FAILED',
      error: result.error || null,
    })),
    recommendations: generateRecommendations(results),
  };
  
  // 保存 JSON 报告
  const jsonReportPath = path.join(reportDir, `security-report-${Date.now()}.json`);
  fs.writeFileSync(jsonReportPath, JSON.stringify(report, null, 2));
  
  // 生成 Markdown 报告
  const markdownReport = generateMarkdownReport(report);
  const mdReportPath = path.join(reportDir, `security-report-${Date.now()}.md`);
  fs.writeFileSync(mdReportPath, markdownReport);
  
  console.log(`📊 安全测试报告已生成:`);
  console.log(`   JSON: ${jsonReportPath}`);
  console.log(`   Markdown: ${mdReportPath}`);
  
  return report;
}

// 生成 Markdown 格式的报告
function generateMarkdownReport(report) {
  const { summary, tests, recommendations } = report;
  
  return `# 安全测试报告

## 📊 测试摘要
- **测试时间**: ${report.timestamp}
- **总测试数**: ${summary.total}
- **通过**: ${summary.passed}
- **失败**: ${summary.failed}
- **成功率**: ${((summary.passed / summary.total) * 100).toFixed(1)}%

## 📋 测试详情

${tests.map(test => `
### ${test.status === 'PASSED' ? '✅' : '❌'} ${test.name}
**描述**: ${test.description}
**状态**: ${test.status}
${test.error ? `**错误**: \`${test.error}\`` : ''}
`).join('\n')}

## 🔧 安全建议

${recommendations.map(rec => `- ${rec}`).join('\n')}

## 📈 后续行动

${summary.failed > 0 ? `
⚠️ **发现 ${summary.failed} 个失败的测试，需要立即处理：**

1. 检查失败的测试用例
2. 修复发现的安全问题
3. 重新运行测试验证修复效果
4. 更新安全文档和最佳实践
` : `
✅ **所有安全测试通过！**

建议定期运行安全测试以确保持续的安全性。
`}

---
*报告生成时间: ${new Date().toLocaleString()}*
`;
}

// 生成安全建议
function generateRecommendations(results) {
  const recommendations = [
    '定期运行安全测试，建议在每次代码提交前执行',
    '监控第三方依赖的安全漏洞，及时更新',
    '实施代码审查，特别关注安全相关的代码变更',
    '配置 CI/CD 流水线自动运行安全测试',
  ];
  
  const failedTests = results.filter(r => !r.success);
  if (failedTests.length > 0) {
    recommendations.unshift(
      '立即修复失败的安全测试，这些可能表示存在安全漏洞',
      '对失败的测试进行根因分析，确保彻底解决问题'
    );
  }
  
  return recommendations;
}

// 主执行函数
async function main() {
  const startTime = Date.now();
  const results = [];
  
  // 运行所有测试
  for (const config of testConfigs) {
    const result = runTest(config);
    results.push(result);
  }
  
  // 生成报告
  const report = generateSecurityReport(results);
  
  // 输出摘要
  const duration = ((Date.now() - startTime) / 1000).toFixed(2);
  console.log(`\n🏁 安全测试完成 (耗时 ${duration}s)`);
  console.log(`📊 结果: ${report.summary.passed}/${report.summary.total} 通过`);
  
  if (report.summary.failed > 0) {
    console.log(`\n⚠️  发现 ${report.summary.failed} 个安全问题，请立即处理！`);
    process.exit(1);
  } else {
    console.log(`\n✅ 所有安全测试通过！`);
    process.exit(0);
  }
}

// 错误处理
process.on('unhandledRejection', (error) => {
  console.error('❌ 未处理的错误:', error);
  process.exit(1);
});

// 运行主函数
main().catch(console.error);
