/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  env: {
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
    NEXT_PUBLIC_API_VERSION_PREFIX: process.env.NEXT_PUBLIC_API_VERSION_PREFIX,
  },

  // 开发环境配置
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      // 在开发环境中客户端配置 webpack-dev-server
      config.devServer = {
        ...(config.devServer || {}),
        host: "0.0.0.0",
        hot: true,
      };
    }
    return config;
  },

  // CORS 配置
  async headers() {
    // 从环境变量获取允许的源
    const allowedOrigins = process.env.ALLOWED_ORIGINS
      ? process.env.ALLOWED_ORIGINS.split(",")
      : ["*"];

    return [
      {
        source: "/api/:path*",
        headers: [
          {
            key: "Access-Control-Allow-Credentials",
            value: "true",
          },
          {
            key: "Access-Control-Allow-Origin",
            // 如果只有一个源且为*，直接使用*
            // 否则使用环境变量配置的值
            value:
              allowedOrigins.length === 1 && allowedOrigins[0] === "*"
                ? "*"
                : process.env.ALLOWED_ORIGINS || "*",
          },
          {
            key: "Access-Control-Allow-Methods",
            value: "GET,OPTIONS,PATCH,DELETE,POST,PUT",
          },
          {
            key: "Access-Control-Allow-Headers",
            value:
              "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version",
          },
        ],
      },
    ];
  },

  // 处理预检请求
  async rewrites() {
    return [
      {
        source: "/api/:path*",
        destination: "/api/:path*",
      },
    ];
  },
};

module.exports = nextConfig;
