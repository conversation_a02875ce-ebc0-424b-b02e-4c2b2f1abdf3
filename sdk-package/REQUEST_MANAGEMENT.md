# 请求管理机制

本文档详细说明了 SDK 中实现的请求去重和队列管理机制，用于解决外部项目多处同时调用认证相关端点导致的重复请求问题。

## 🎯 解决的问题

### 问题描述
外部使用这个 SDK 的项目，有可能出现多处同时调用认证相关端点，导致同一时间发送多个相同的请求到后端的问题。

### 具体场景
1. **多组件同时检查认证状态**：导航栏、侧边栏、页面内容等多个组件同时调用 `verifyStatus`
2. **重复的用户信息获取**：多个地方同时调用 `getUser` 方法
3. **并发认证操作**：登录、登出等操作可能被意外地并发执行

## 🛠️ 解决方案

### 1. 请求去重机制 (Request Deduplication)

**原理**：相同的请求会返回同一个 Promise，避免重复的网络请求。

**实现方式**：
- 使用 `Map` 存储正在进行的请求
- 为每个请求类型分配唯一的键
- 相同键的请求返回缓存的 Promise

**适用方法**：
- `verifyStatus()` - 验证认证状态
- `getUser()` - 获取用户信息

```typescript
// 示例：多个组件同时调用，只会发送一次实际请求
const promise1 = authClient.verifyStatus(); // 发送网络请求
const promise2 = authClient.verifyStatus(); // 返回相同的 Promise
const promise3 = authClient.verifyStatus(); // 返回相同的 Promise

// promise1 === promise2 === promise3 (true)
```

### 2. 队列管理机制 (Request Queue)

**原理**：确保认证相关请求按顺序执行，永远不会瞬间执行多个请求。

**实现方式**：
- 使用 Promise 链维护请求队列
- 每个新请求都会等待前一个请求完成
- 错误不会中断队列的执行

**适用方法**：
- `verifyStatus()` - 验证认证状态
- `signInOrUp()` - 登录或注册
- `logout()` - 登出
- `getUser()` - 获取用户信息

```typescript
// 示例：请求会按顺序执行，不会并发
authClient.signInOrUp(email, code);  // 第1个执行
authClient.verifyStatus();           // 等待第1个完成后执行
authClient.getUser();                // 等待第2个完成后执行
```

### 3. 组合机制 (Combined Approach)

对于 `verifyStatus` 和 `getUser` 方法，同时应用去重和队列管理：

```typescript
// 既去重又排队
async verifyStatus(): Promise<VerifyStatusResponse> {
  return this.requestManager.deduplicateAndEnqueue(
    'verifyStatus',
    () => this._verifyStatusInternal()
  );
}
```

## 📋 方法分类

### 去重 + 队列管理
- `verifyStatus()` - 最重要，最容易重复调用
- `getUser()` - 用户信息获取

### 仅队列管理
- `signInOrUp()` - 登录注册（参数不同，不适合去重）
- `logout()` - 登出操作

### 不受管理
- `login()` - 重定向操作，无网络请求
- `checkSSORedirect()` - 本地状态检查
- `apiSendVerificationCode()` - 验证码发送（参数不同）

## 🔧 技术实现

### RequestManager 类

```typescript
class RequestManager {
  private pendingRequests: Map<string, PendingRequest<any>>;
  private requestQueue: Promise<any>;
  
  // 请求去重
  deduplicate<T>(key: string, requestFn: () => Promise<T>): Promise<T>
  
  // 队列管理
  enqueue<T>(requestFn: () => Promise<T>): Promise<T>
  
  // 组合功能
  deduplicateAndEnqueue<T>(key: string, requestFn: () => Promise<T>): Promise<T>
}
```

### 超时清理机制

- 自动清理超过 30 秒的超时请求
- 避免内存泄漏
- 防止长时间占用缓存

### 错误处理

- 请求失败时自动从缓存中移除
- 队列中的错误不影响后续请求
- 保留原始错误信息

## 📊 性能影响

### 优势
- ✅ 减少重复网络请求
- ✅ 降低服务器负载
- ✅ 提升用户体验
- ✅ 避免竞态条件

### 开销
- 少量内存开销（Map 存储）
- 少量 CPU 开销（Promise 链）
- 总体性能提升明显

## 🧪 测试验证

运行测试套件验证功能：

```bash
cd sdk-package
npm test
```

测试覆盖：
- ✅ 请求去重功能
- ✅ 队列管理功能
- ✅ 错误处理
- ✅ 超时清理
- ✅ 缓存管理

## 🔍 调试工具

SDK 提供了调试方法来监控请求管理状态：

```typescript
// 获取调试信息
const debugInfo = authClient.getRequestManagerDebugInfo();
console.log('待处理请求数量:', debugInfo.pendingRequestsCount);
console.log('缓存的请求键:', debugInfo.pendingRequestKeys);

// 清除缓存（测试用）
authClient.clearRequestCache();
```

## 📝 使用建议

### 最佳实践
1. **正常使用**：无需修改现有代码，机制自动生效
2. **监控调试**：在开发环境使用调试方法监控状态
3. **错误处理**：继续使用原有的错误处理逻辑

### 注意事项
1. 请求去重基于方法名，不考虑参数差异
2. 队列管理确保顺序，但可能增加总体响应时间
3. 超时清理机制避免内存泄漏，但可能清理正在进行的长请求

## 🚀 向后兼容性

- ✅ 完全向后兼容
- ✅ 现有 API 接口不变
- ✅ 仅内部实现改变
- ✅ 不影响外部使用者

## 📈 效果预期

在典型的多组件应用中：
- **请求数量减少**：70-90%
- **响应时间优化**：避免重复等待
- **服务器负载降低**：显著减少并发请求
- **用户体验提升**：更快的认证状态更新
