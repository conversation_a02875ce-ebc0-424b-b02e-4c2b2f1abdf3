import logger from "./logger";

interface PendingRequest<T> {
  promise?: Promise<T>;
  result?: T;
  error?: any;
  timestamp: number;
  completedAt?: number;
  status: "pending" | "completed" | "failed";
}

/**
 * 请求管理器
 * 提供请求去重和队列管理功能，确保认证相关请求的合理调度
 */
export class RequestManager {
  private pendingRequests: Map<string, PendingRequest<any>> = new Map();
  private requestQueue: Promise<any> = Promise.resolve();
  private readonly REQUEST_TIMEOUT = 30000; // 30秒超时
  private readonly CACHE_WINDOW = 2000; // 2000ms 短期缓存窗口，确保令牌刷新后的稳定性

  logger.log("RequestManager: 初始化请求管理器...");
  

  /**
   * 清理超时的请求和过期的缓存
   * 避免内存泄漏和长时间占用缓存
   */
  private cleanupTimeoutRequests(): void {
    const now = Date.now();
    const timeoutKeys: string[] = [];

    // 使用 forEach 方法代替 for...of 循环以兼容较低的 ES 版本
    this.pendingRequests.forEach((request, key) => {
      // 清理超时的正在进行的请求
      if (
        request.status === "pending" &&
        now - request.timestamp > this.REQUEST_TIMEOUT
      ) {
        timeoutKeys.push(key);
      }
      // 清理过期的已完成缓存（超过缓存窗口很久的）
      else if (request.status !== "pending" && request.completedAt) {
        const cacheAge = now - request.completedAt;
        if (cacheAge > this.CACHE_WINDOW * 10) {
          // 缓存窗口的10倍后清理
          timeoutKeys.push(key);
        }
      }
    });

    if (timeoutKeys.length > 0) {
      logger.warn(
        `Cleaning up ${timeoutKeys.length} timeout/expired requests:`,
        timeoutKeys
      );
      timeoutKeys.forEach((key) => this.pendingRequests.delete(key));
    }
  }

  /**
   * 请求去重
   * 相同的请求键会返回同一个 Promise，避免重复请求
   * 支持短期缓存，在缓存窗口内直接返回已完成的结果
   * @param key 请求的唯一标识
   * @param requestFn 实际的请求函数
   * @returns Promise<T>
   */
  deduplicate<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    // 清理超时请求
    this.cleanupTimeoutRequests();

    const cached = this.pendingRequests.get(key);

    // 检查是否有已完成的缓存结果（短期缓存）
    if (cached && cached.status === "completed" && cached.completedAt) {
      const age = Date.now() - cached.completedAt;
      if (age < this.CACHE_WINDOW) {
        logger.log(
          `SDK RequestManager: Request '${key}' returning cached result (age: ${age}ms)`
        );
        return Promise.resolve(cached.result as T);
      }
    }

    // 检查是否有失败的缓存结果（短期缓存）
    if (cached && cached.status === "failed" && cached.completedAt) {
      const age = Date.now() - cached.completedAt;
      if (age < this.CACHE_WINDOW) {
        logger.log(
          `SDK RequestManager: Request '${key}' returning cached error (age: ${age}ms)`
        );
        return Promise.reject(cached.error);
      }
    }

    // 检查是否有正在进行的请求
    if (cached && cached.status === "pending" && cached.promise) {
      logger.log(
        `SDK RequestManager: Request '${key}' deduplicated, returning existing promise`
      );
      return cached.promise;
    }

    // 创建新请求
    const promise = requestFn()
      .then((result) => {
        // 更新缓存状态为已完成
        const entry = this.pendingRequests.get(key);
        if (entry) {
          entry.status = "completed";
          entry.result = result;
          entry.completedAt = Date.now();
          entry.promise = undefined; // 清除 promise 引用以释放内存
          logger.log(
            `SDK RequestManager: Request '${key}' completed and cached`
          );
        }
        return result;
      })
      .catch((error) => {
        // 更新缓存状态为失败
        const entry = this.pendingRequests.get(key);
        if (entry) {
          entry.status = "failed";
          entry.error = error;
          entry.completedAt = Date.now();
          entry.promise = undefined; // 清除 promise 引用以释放内存
          logger.log(`SDK RequestManager: Request '${key}' failed and cached`);
        }
        throw error;
      });

    // 缓存新请求
    this.pendingRequests.set(key, {
      promise,
      timestamp: Date.now(),
      status: "pending",
    });

    logger.log(`SDK RequestManager: Request '${key}' added to cache`);
    return promise;
  }

  /**
   * 队列管理
   * 确保请求按顺序执行，避免并发冲突
   * @param requestFn 实际的请求函数
   * @returns Promise<T>
   */
  async enqueue<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.requestQueue = this.requestQueue
        .then(async () => {
          try {
            logger.log("SDK RequestManager: Executing queued request");
            const result = await requestFn();
            resolve(result);
          } catch (error) {
            logger.error("SDK RequestManager: Queued request failed:", error);
            reject(error);
          }
        })
        .catch(() => {
          // 忽略队列中前面请求的错误，不影响当前请求
          // 这里的 catch 是为了防止 Promise 链中断
        });
    });
  }

  /**
   * 组合去重和队列
   * 既避免重复请求，又确保请求顺序
   * @param key 请求的唯一标识
   * @param requestFn 实际的请求函数
   * @returns Promise<T>
   */
  async deduplicateAndEnqueue<T>(
    key: string,
    requestFn: () => Promise<T>
  ): Promise<T> {
    return this.deduplicate(key, () => this.enqueue(requestFn));
  }

  /**
   * 获取当前缓存的请求数量
   * 主要用于调试和监控
   */
  getPendingRequestsCount(): number {
    this.cleanupTimeoutRequests();
    return this.pendingRequests.size;
  }

  /**
   * 获取当前缓存的请求键列表
   * 主要用于调试和监控
   */
  getPendingRequestKeys(): string[] {
    this.cleanupTimeoutRequests();
    return Array.from(this.pendingRequests.keys());
  }

  /**
   * 获取详细的缓存状态信息
   * 主要用于调试和监控
   */
  getCacheStatus(): {
    [key: string]: { status: string; age: number; cacheAge?: number };
  } {
    this.cleanupTimeoutRequests();
    const now = Date.now();
    const status: {
      [key: string]: { status: string; age: number; cacheAge?: number };
    } = {};

    this.pendingRequests.forEach((request, key) => {
      const age = now - request.timestamp;
      const info: { status: string; age: number; cacheAge?: number } = {
        status: request.status,
        age,
      };

      if (request.completedAt) {
        info.cacheAge = now - request.completedAt;
      }

      status[key] = info;
    });

    return status;
  }

  /**
   * 清除所有缓存的请求
   * 主要用于测试或特殊情况下的重置
   */
  clearCache(): void {
    logger.log(
      `SDK RequestManager: Clearing ${this.pendingRequests.size} cached requests`
    );
    this.pendingRequests.clear();
  }
}
