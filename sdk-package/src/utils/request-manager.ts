import logger from "./logger";

interface PendingRequest<T> {
  promise: Promise<T>;
  timestamp: number;
}

/**
 * 请求管理器
 * 提供请求去重和队列管理功能，确保认证相关请求的合理调度
 */
export class RequestManager {
  private pendingRequests: Map<string, PendingRequest<any>> = new Map();
  private requestQueue: Promise<any> = Promise.resolve();
  private readonly REQUEST_TIMEOUT = 30000; // 30秒超时

  /**
   * 清理超时的请求
   * 避免内存泄漏和长时间占用缓存
   */
  private cleanupTimeoutRequests(): void {
    const now = Date.now();
    const timeoutKeys: string[] = [];

    // 使用 forEach 方法代替 for...of 循环以兼容较低的 ES 版本
    this.pendingRequests.forEach((request, key) => {
      if (now - request.timestamp > this.REQUEST_TIMEOUT) {
        timeoutKeys.push(key);
      }
    });

    if (timeoutKeys.length > 0) {
      logger.warn(
        `Cleaning up ${timeoutKeys.length} timeout requests:`,
        timeoutKeys
      );
      timeoutKeys.forEach((key) => this.pendingRequests.delete(key));
    }
  }

  /**
   * 请求去重
   * 相同的请求键会返回同一个 Promise，避免重复请求
   * @param key 请求的唯一标识
   * @param requestFn 实际的请求函数
   * @returns Promise<T>
   */
  deduplicate<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    // 清理超时请求
    this.cleanupTimeoutRequests();

    // 检查是否已有相同请求在进行中
    if (this.pendingRequests.has(key)) {
      logger.log(
        `SDK RequestManager: Request '${key}' deduplicated, returning existing promise`
      );
      return this.pendingRequests.get(key)!.promise as Promise<T>;
    }

    // 创建新请求
    const promise = requestFn().finally(() => {
      this.pendingRequests.delete(key);
      logger.log(
        `SDK RequestManager: Request '${key}' completed and removed from cache`
      );
    });

    // 缓存请求
    this.pendingRequests.set(key, {
      promise,
      timestamp: Date.now(),
    });

    logger.log(`SDK RequestManager: Request '${key}' added to cache`);
    return promise;
  }

  /**
   * 队列管理
   * 确保请求按顺序执行，避免并发冲突
   * @param requestFn 实际的请求函数
   * @returns Promise<T>
   */
  async enqueue<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.requestQueue = this.requestQueue
        .then(async () => {
          try {
            logger.log("SDK RequestManager: Executing queued request");
            const result = await requestFn();
            resolve(result);
          } catch (error) {
            logger.error("SDK RequestManager: Queued request failed:", error);
            reject(error);
          }
        })
        .catch(() => {
          // 忽略队列中前面请求的错误，不影响当前请求
          // 这里的 catch 是为了防止 Promise 链中断
        });
    });
  }

  /**
   * 组合去重和队列
   * 既避免重复请求，又确保请求顺序
   * @param key 请求的唯一标识
   * @param requestFn 实际的请求函数
   * @returns Promise<T>
   */
  async deduplicateAndEnqueue<T>(
    key: string,
    requestFn: () => Promise<T>
  ): Promise<T> {
    return this.deduplicate(key, () => this.enqueue(requestFn));
  }

  /**
   * 获取当前缓存的请求数量
   * 主要用于调试和监控
   */
  getPendingRequestsCount(): number {
    this.cleanupTimeoutRequests();
    return this.pendingRequests.size;
  }

  /**
   * 获取当前缓存的请求键列表
   * 主要用于调试和监控
   */
  getPendingRequestKeys(): string[] {
    this.cleanupTimeoutRequests();
    return Array.from(this.pendingRequests.keys());
  }

  /**
   * 清除所有缓存的请求
   * 主要用于测试或特殊情况下的重置
   */
  clearCache(): void {
    logger.log(
      `SDK RequestManager: Clearing ${this.pendingRequests.size} cached requests`
    );
    this.pendingRequests.clear();
  }
}
