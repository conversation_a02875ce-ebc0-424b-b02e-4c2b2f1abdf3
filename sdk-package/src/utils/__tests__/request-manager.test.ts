import { RequestManager } from "../request-manager";

// Mock logger to avoid console output during tests
jest.mock("../logger", () => ({
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}));

describe("RequestManager", () => {
  let requestManager: RequestManager;

  beforeEach(() => {
    requestManager = new RequestManager();
  });

  afterEach(() => {
    requestManager.clearCache();
  });

  describe("deduplicate", () => {
    it("should return the same promise for identical keys", async () => {
      const mockFn = jest.fn().mockResolvedValue("result");

      // Start two identical requests
      const promise1 = requestManager.deduplicate("test-key", mockFn);
      const promise2 = requestManager.deduplicate("test-key", mockFn);

      // They should be the same promise
      expect(promise1).toBe(promise2);

      // Wait for completion
      const result1 = await promise1;
      const result2 = await promise2;

      // Both should have the same result
      expect(result1).toBe("result");
      expect(result2).toBe("result");

      // The function should only be called once
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it("should allow new requests after previous ones complete", async () => {
      const mockFn = jest
        .fn()
        .mockResolvedValueOnce("result1")
        .mockResolvedValueOnce("result2");

      // First request
      const result1 = await requestManager.deduplicate("test-key", mockFn);
      expect(result1).toBe("result1");

      // Second request with same key (should be allowed after first completes)
      const result2 = await requestManager.deduplicate("test-key", mockFn);
      expect(result2).toBe("result2");

      expect(mockFn).toHaveBeenCalledTimes(2);
    });

    it("should handle errors correctly", async () => {
      const error = new Error("Test error");
      const mockFn = jest.fn().mockRejectedValue(error);

      // Start two identical requests
      const promise1 = requestManager.deduplicate("test-key", mockFn);
      const promise2 = requestManager.deduplicate("test-key", mockFn);

      // Both should reject with the same error
      await expect(promise1).rejects.toThrow("Test error");
      await expect(promise2).rejects.toThrow("Test error");

      // The function should only be called once
      expect(mockFn).toHaveBeenCalledTimes(1);
    });
  });

  describe("enqueue", () => {
    it("should execute requests in order", async () => {
      const results: number[] = [];

      const createRequest = (value: number, delay: number) => async () => {
        await new Promise((resolve) => setTimeout(resolve, delay));
        results.push(value);
        return value;
      };

      // Start multiple requests with different delays
      const promises = [
        requestManager.enqueue(createRequest(1, 50)),
        requestManager.enqueue(createRequest(2, 10)),
        requestManager.enqueue(createRequest(3, 30)),
      ];

      await Promise.all(promises);

      // Results should be in order despite different delays
      expect(results).toEqual([1, 2, 3]);
    });

    it("should handle errors without affecting subsequent requests", async () => {
      const results: string[] = [];

      const successRequest = async (value: string) => {
        results.push(value);
        return value;
      };

      const errorRequest = async () => {
        throw new Error("Test error");
      };

      // Queue requests: success, error, success
      const promise1 = requestManager.enqueue(() => successRequest("first"));
      const promise2 = requestManager.enqueue(errorRequest);
      const promise3 = requestManager.enqueue(() => successRequest("third"));

      // First should succeed
      await expect(promise1).resolves.toBe("first");

      // Second should fail
      await expect(promise2).rejects.toThrow("Test error");

      // Third should still succeed
      await expect(promise3).resolves.toBe("third");

      expect(results).toEqual(["first", "third"]);
    });
  });

  describe("deduplicateAndEnqueue", () => {
    it("should combine deduplication and queuing", async () => {
      const mockFn = jest.fn().mockResolvedValue("result");

      // Start multiple identical requests
      const promises = [
        requestManager.deduplicateAndEnqueue("test-key", mockFn),
        requestManager.deduplicateAndEnqueue("test-key", mockFn),
        requestManager.deduplicateAndEnqueue("test-key", mockFn),
      ];

      const results = await Promise.all(promises);

      // All should have the same result
      expect(results).toEqual(["result", "result", "result"]);

      // Function should only be called once due to deduplication
      expect(mockFn).toHaveBeenCalledTimes(1);
    });
  });

  describe("cache management", () => {
    it("should track pending requests count", () => {
      expect(requestManager.getPendingRequestsCount()).toBe(0);

      // Start a request but don't await it
      const mockFn = jest.fn().mockImplementation(() => new Promise(() => {})); // Never resolves
      requestManager.deduplicate("test-key", mockFn);

      expect(requestManager.getPendingRequestsCount()).toBe(1);
      expect(requestManager.getPendingRequestKeys()).toEqual(["test-key"]);
    });

    it("should clear cache", async () => {
      const mockFn = jest.fn().mockResolvedValue("result");

      // Start a request
      requestManager.deduplicate("test-key", mockFn);
      expect(requestManager.getPendingRequestsCount()).toBe(1);

      // Clear cache
      requestManager.clearCache();
      expect(requestManager.getPendingRequestsCount()).toBe(0);
    });
  });
});
