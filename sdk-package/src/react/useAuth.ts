import { useEffect, useState } from "react";
import { AuthClient } from "../client";
import { AuthState, SSORedirectState, User } from "../types";
import logger from "../utils/logger";

// 全局单例实例
let authClientInstance: AuthClient | null = null;

/**
 * 初始化认证客户端
 * 在应用入口处调用此函数来配置认证客户端
 */
export function initAuthClient(
  authServiceApiUrl: string,
  authServiceRedirectUrl: string,
  options?: {
    sensitiveHostnames?: string[];
    timeout?: number; // 请求超时时间（毫秒）
  }
) {
  if (!authServiceApiUrl || !authServiceRedirectUrl) {
    logger.error("authServiceApiUrl和authServiceRedirectUrl必须提供");
    return;
  }

  try {
    authClientInstance = new AuthClient({
      authServiceApiUrl,
      authServiceRedirectUrl,
      sensitiveHostnames: options?.sensitiveHostnames, // 不使用默认值，让AuthClient内部处理
      timeout: options?.timeout, // 传递超时配置，可以有默认值
    });
  } catch (error) {
    logger.error("初始化认证客户端失败:", error);
  }
}

/**
 * 统一认证Hook
 * 提供认证状态和操作方法
 */
export function useAuth(): AuthState & {
  login: (redirectUrl?: string) => void;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  verifyStatus: () => Promise<import("../types").VerifyStatusResponse>;
  checkSSORedirect: () => SSORedirectState;
} {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    loading: true,
    error: null,
    statusCode: undefined,
  });

  // 检查认证状态的函数
  const checkAuthStatus = async () => {
    // 检查客户端实例是否已初始化
    if (!authClientInstance) {
      setAuthState({
        isAuthenticated: false,
        user: null,
        loading: false,
        error: "认证客户端未初始化，请调用initAuthClient",
        statusCode: undefined,
      });
      return;
    }

    try {
      // 直接使用 checkAuth，它内部会调用 verifyStatus 并处理所有逻辑
      const { isAuthenticated, user, statusCode } =
        await authClientInstance.checkAuth();

      setAuthState({
        isAuthenticated,
        user: user || null,
        loading: false,
        error: null,
        statusCode,
      });
    } catch (error) {
      setAuthState({
        isAuthenticated: false,
        user: null,
        loading: false,
        error:
          error instanceof Error
            ? error.message
            : "Authentication check failed",
        statusCode: undefined,
      });
    }
  };

  // 组件挂载时检查认证状态
  useEffect(() => {
    checkAuthStatus();
  }, []);

  // 监听路由变化，在路由变化时重新检查认证状态
  useEffect(() => {
    if (typeof window !== "undefined") {
      // 路由变化时的处理函数
      const handleRouteChange = () => {
        logger.log("SDK: 检测到路由变化，重新检查认证状态");
        checkAuthStatus();
      };

      // 监听popstate事件（浏览器前进/后退按钮）
      window.addEventListener("popstate", handleRouteChange);

      // 尝试拦截history.pushState和history.replaceState（客户端路由）
      const originalPushState = window.history.pushState;
      const originalReplaceState = window.history.replaceState;

      window.history.pushState = function () {
        originalPushState.apply(this, arguments as any);
        handleRouteChange();
      };

      window.history.replaceState = function () {
        originalReplaceState.apply(this, arguments as any);
        handleRouteChange();
      };

      return () => {
        // 清理事件监听
        window.removeEventListener("popstate", handleRouteChange);
        window.history.pushState = originalPushState;
        window.history.replaceState = originalReplaceState;
      };
    }
  }, []);

  // 登录方法
  const login = (redirectUrl?: string) => {
    if (!authClientInstance) {
      setAuthState((prev) => ({
        ...prev,
        error: "认证客户端未初始化，请调用initAuthClient",
      }));
      return;
    }

    authClientInstance.login(redirectUrl);
  };

  // 登出方法
  const logout = async () => {
    try {
      if (!authClientInstance) {
        throw new Error("认证客户端未初始化，请调用initAuthClient");
      }

      await authClientInstance.logout();

      // 清除本地存储的用户信息
      localStorage.removeItem("user");

      // 更新本地状态
      setAuthState({
        isAuthenticated: false,
        user: null,
        loading: false,
        error: null,
        statusCode: undefined,
      });
    } catch (error) {
      setAuthState((prev) => ({
        ...prev,
        error: error instanceof Error ? error.message : "Logout failed",
      }));
    }
  };

  // 刷新用户信息
  // 注意：目前只从本地存储获取用户信息，API 端点尚未实现
  const refreshUser = async () => {
    try {
      if (!authClientInstance) {
        throw new Error("认证客户端未初始化，请调用initAuthClient");
      }

      setAuthState((prev) => ({ ...prev, loading: true }));
      const user = await authClientInstance.getUser();

      setAuthState({
        isAuthenticated: !!user,
        user,
        loading: false,
        error: null,
        statusCode: undefined,
      });
    } catch (error) {
      setAuthState((prev) => ({
        ...prev,
        loading: false,
        error:
          error instanceof Error ? error.message : "Failed to refresh user",
      }));
    }
  };

  // 验证用户认证状态
  const verifyStatus = async () => {
    try {
      if (!authClientInstance) {
        throw new Error("认证客户端未初始化，请调用initAuthClient");
      }

      return await authClientInstance.verifyStatus();
    } catch (error) {
      setAuthState((prev) => ({
        ...prev,
        error: error instanceof Error ? error.message : "验证状态失败",
      }));
      return { success: false, message: "验证状态失败" };
    }
  };

  // 检查SSO重定向状态
  const checkSSORedirect = (): SSORedirectState => {
    if (!authClientInstance) {
      return {
        isSSORedirect: false,
        clear: () => {},
      };
    }

    return authClientInstance.checkSSORedirect();
  };

  return {
    ...authState,
    login,
    logout,
    refreshUser,
    verifyStatus,
    checkSSORedirect,
  };
}
