import axios, { AxiosInstance } from "axios";
import {
  AuthClientConfig,
  AuthResponse,
  SignInOrUpResponse,
  SSORedirectState,
  User,
  VerifyStatusResponse,
} from "./types";
import FingerprintJS from "@fingerprintjs/fingerprintjs";
import logger from "./utils/logger";
import { RequestManager } from "./utils/request-manager";

// 默认请求超时时间（毫秒）
const DEFAULT_TIMEOUT = 10000;

// SSO重定向状态标记的sessionStorage键名
const SSO_REDIRECT_KEY = "quote_auth_sso_redirect"; // 10秒

/**
 * 统一认证客户端SDK
 * 提供认证状态检查、登录、登出等功能
 */
export class AuthClient {
  private api: AxiosInstance;
  private appApi: AxiosInstance;
  private authServiceRedirectUrl: string;
  private deviceId: string = "";
  private fingerprint: string = "";
  private sensitiveHostnames: string[] = [];
  private requestManager: RequestManager;

  constructor(config: AuthClientConfig) {
    if (!config.authServiceApiUrl) {
      throw new Error("authServiceApiUrl is required");
    }

    if (!config.authServiceRedirectUrl) {
      throw new Error("authServiceRedirectUrl is required");
    }

    // 主要API实例，用于认证相关操作
    this.api = axios.create({
      baseURL: config.authServiceApiUrl,
      withCredentials: true, // 确保跨域请求携带Cookie
      timeout: config.timeout ?? DEFAULT_TIMEOUT, // timeout可以有默认值
    });

    // 额外的API实例，用于调用应用自身的API
    this.appApi = axios.create({
      baseURL: config.authServiceApiUrl,
      withCredentials: true,
      timeout: config.timeout ?? DEFAULT_TIMEOUT, // timeout可以有默认值
    });

    this.authServiceRedirectUrl = config.authServiceRedirectUrl;

    // 初始化敏感域名列表，sensitiveHostnames可以有默认值（空数组）
    this.sensitiveHostnames = config.sensitiveHostnames ?? [];

    // 初始化请求管理器
    this.requestManager = new RequestManager();

    // 初始化设备指纹
    if (typeof window !== "undefined") {
      this.initFingerprint();
    }
  }

  /**
   * 初始化浏览器指纹
   */
  private async initFingerprint() {
    try {
      // 仅在浏览器环境中执行
      if (typeof window !== "undefined") {
        // 初始化FingerprintJS
        const fpPromise = FingerprintJS.load();
        const fp = await fpPromise;
        const result = await fp.get();

        // 获取浏览器指纹
        this.fingerprint = result.visitorId;

        // 生成设备ID
        this.deviceId = localStorage.getItem("device_id") || "";
        if (!this.deviceId) {
          this.deviceId = `device_${Date.now()}_${Math.random()
            .toString(36)
            .substring(2, 9)}`;
          localStorage.setItem("device_id", this.deviceId);
        }
      }
    } catch (error) {
      logger.error("Failed to initialize fingerprint:", error);
    }
  }

  /**
   * 检查当前主机名是否在敏感域名列表中
   */
  public isCurrentHostnameSensitive(): boolean {
    if (typeof window === "undefined") return false;

    const currentHostname = window.location.hostname;
    const currentPath = window.location.pathname;

    // 检查完整路径或仅主机名是否匹配
    for (const sensitive of this.sensitiveHostnames) {
      // 检查是否包含路径
      if (sensitive.includes("/")) {
        const [hostPart, ...pathParts] = sensitive.split("/");
        const sensitivePathPattern = "/" + pathParts.join("/");

        if (
          currentHostname === hostPart &&
          currentPath.startsWith(sensitivePathPattern)
        ) {
          return true;
        }
      }
      // 检查主机名匹配
      else if (
        currentHostname === sensitive ||
        currentHostname.endsWith("." + sensitive)
      ) {
        return true;
      }
    }

    return false;
  }

  /**
   * 检查用户是否已认证
   */
  async checkAuth(): Promise<{
    isAuthenticated: boolean;
    user?: User;
    statusCode?: number;
  }> {
    try {
      // 使用verifyStatus方法检查认证状态
      const verifyResult = await this.verifyStatus();

      if (verifyResult.success) {
        // 如果认证成功，尝试获取用户信息
        const user = await this.getUser();
        return {
          isAuthenticated: true,
          user: user || undefined,
          statusCode: verifyResult.statusCode,
        };
      }

      // 如果是401且是敏感域名，verifyStatus已处理重定向

      return {
        isAuthenticated: false,
        statusCode: verifyResult.statusCode,
      };
    } catch (error) {
      logger.error("Auth check failed:", error);
      return { isAuthenticated: false };
    }
  }

  /**
   * 设置SSO重定向标记
   * @private
   */
  private setSSORedirectFlag(): void {
    if (typeof window !== "undefined" && window.sessionStorage) {
      sessionStorage.setItem(SSO_REDIRECT_KEY, "true");
    }
  }

  /**
   * 检查并获取SSO重定向状态
   * @returns SSO重定向状态对象，包含状态和清除方法
   */
  checkSSORedirect(): SSORedirectState {
    if (typeof window === "undefined" || !window.sessionStorage) {
      return {
        isSSORedirect: false,
        clear: () => {},
      };
    }

    const isSSORedirect = sessionStorage.getItem(SSO_REDIRECT_KEY) === "true";

    return {
      isSSORedirect,
      clear: () => {
        if (typeof window !== "undefined" && window.sessionStorage) {
          sessionStorage.removeItem(SSO_REDIRECT_KEY);
        }
      },
    };
  }

  /**
   * 重定向到登录页
   */
  login(redirectUrl?: string): void {
    if (typeof window === "undefined") {
      return;
    }

    // 设置SSO重定向标记，登录成功后目标页面可以检测到
    this.setSSORedirectFlag();

    const currentUrl = redirectUrl || window.location.href;
    const encodedRedirect = encodeURIComponent(currentUrl);
    window.location.href = `${this.authServiceRedirectUrl}/login?redirect=${encodedRedirect}`;
  }

  /**
   * 登出
   * 使用队列管理确保认证请求按顺序执行
   */
  async logout(): Promise<void> {
    return this.requestManager.enqueue(() => this._logoutInternal());
  }

  /**
   * 内部登出实现
   * 实际的API调用逻辑
   */
  private async _logoutInternal(): Promise<void> {
    try {
      // 调用登出API
      await this.api.post("/v1/auth/logout");

      if (typeof window !== "undefined") {
        // 清除本地存储中的用户信息
        localStorage.removeItem("user");

        // 根据敏感域名列表决定重定向行为
        if (this.isCurrentHostnameSensitive()) {
          // 敏感域名：直接重定向到登录页
          logger.log("SDK: 敏感域名退出登录，重定向到登录页");
          const loginUrl = `${this.authServiceRedirectUrl}/login`;
          window.location.href = loginUrl;
        } else {
          // 非敏感域名：仅清除本地认证状态，停留在当前页面
          logger.log("SDK: 非敏感域名退出登录，停留在当前页面");
          // 刷新当前页面，清除状态
          window.location.reload();
        }
      }
    } catch (error) {
      logger.error("Logout failed:", error);

      // 即使API调用失败，也执行同样的重定向逻辑
      if (typeof window !== "undefined") {
        localStorage.removeItem("user");

        if (this.isCurrentHostnameSensitive()) {
          const loginUrl = `${this.authServiceRedirectUrl}/login`;
          window.location.href = loginUrl;
        } else {
          window.location.reload();
        }
      }
    }
  }

  /**
   * 获取当前用户信息
   * 注意：此方法目前仅从本地存储获取用户信息，API 端点尚未实现
   * 使用请求去重和队列管理，避免重复请求
   */
  async getUser(): Promise<User | null> {
    return this.requestManager.deduplicateAndEnqueue("getUser", () =>
      this._getUserInternal()
    );
  }

  /**
   * 内部获取用户信息实现
   * 实际的获取逻辑
   */
  private async _getUserInternal(): Promise<User | null> {
    try {
      // 从本地存储获取用户信息
      if (typeof localStorage !== "undefined") {
        const userStr = localStorage.getItem("user");
        if (userStr) {
          return JSON.parse(userStr);
        }
      }

      // 暂时不从 API 获取，因为端点尚未实现
      return null;
    } catch (error) {
      logger.error("Get user failed:", error);
      return null;
    }
  }

  /**
   * 发送验证码
   */
  async apiSendVerificationCode(contact: string): Promise<AuthResponse> {
    // 等待指纹初始化完成
    if (!this.fingerprint && typeof window !== "undefined") {
      await this.initFingerprint();
    }

    const response = await this.api.post<AuthResponse>(
      "/v1/auth/send_verification",
      {
        contact,
        browser_fingerprinting: this.fingerprint,
        device_id: this.deviceId,
      }
    );

    return {
      success: response.data.success || true,
      message: response.data.message || "验证码发送成功",
    };
  }

  /**
   * 登录或注册
   * 使用队列管理确保认证请求按顺序执行
   */
  async signInOrUp(contact: string, code: string): Promise<SignInOrUpResponse> {
    return this.requestManager.enqueue(() =>
      this._signInOrUpInternal(contact, code)
    );
  }

  /**
   * 内部登录或注册实现
   * 实际的API调用逻辑
   */
  private async _signInOrUpInternal(
    contact: string,
    code: string
  ): Promise<SignInOrUpResponse> {
    // 等待指纹初始化完成
    if (!this.fingerprint && typeof window !== "undefined") {
      await this.initFingerprint();
    }

    const response = await this.api.post<SignInOrUpResponse>(
      "/v1/auth/sign_in_or_up",
      {
        contact,
        code,
        browser_fingerprinting: this.fingerprint,
        device_id: this.deviceId,
      }
    );

    // 如果登录成功，可以保存基本用户信息到本地存储
    if (response.data.success && typeof localStorage !== "undefined") {
      const user = {
        email: contact,
      };
      localStorage.setItem("user", JSON.stringify(user));
    }

    return {
      success: response.data.success,
      message: response.data.message,
      isNewUser: response.status === 201, // 201表示新用户注册成功
    };
  }

  /**
   * 验证当前用户认证状态
   * 通过调用verify_status端点验证JWT是否有效
   * 使用请求去重和队列管理，避免重复请求
   */
  async verifyStatus(): Promise<VerifyStatusResponse> {
    return this.requestManager.deduplicateAndEnqueue("verifyStatus", () =>
      this._verifyStatusInternal()
    );
  }

  /**
   * 内部验证状态实现
   * 实际的API调用逻辑
   */
  private async _verifyStatusInternal(): Promise<VerifyStatusResponse> {
    try {
      // logger.log("SDK: 开始调用验证状态API...");
      const response = await this.api.get<VerifyStatusResponse>(
        "/v1/auth/verify_status"
      );
      // logger.log("SDK: 验证状态API调用成功:", response.data);
      return {
        ...response.data,
        statusCode: response.status,
      };
    } catch (error: any) {
      // logger.error("SDK: 验证状态API调用失败:", error);

      // 打印详细错误信息
      // if (error.response) {
      //   logger.error("SDK: 错误响应状态码:", error.response.status);
      //   logger.error("SDK: 错误响应数据:", error.response.data);
      // }

      // 检查是否为超时错误
      if (error.code === "ECONNABORTED" || error.message?.includes("timeout")) {
        logger.error("SDK: 验证状态API调用超时");
        return {
          success: false,
          message: "请求超时，请检查网络连接",
          statusCode: 408, // 请求超时状态码
        };
      }

      const statusCode = error.response?.status || 500;

      // 如果是401未授权，且当前域名在敏感列表中，则重定向到登录页
      if (
        statusCode === 401 &&
        this.isCurrentHostnameSensitive() &&
        typeof window !== "undefined"
      ) {
        logger.log("SDK: 检测到敏感域名未授权访问，将重定向到登录页");
        // 使用完整的当前URL作为重定向URL，确保登录成功后能回到用户当前页面
        this.login(window.location.href);
        // 不立即返回，而是继续返回标准错误响应，以便调用者能处理
      }

      // 如果请求失败，返回错误状态码
      return {
        success: false,
        message: "认证无效或已过期",
        statusCode: statusCode, // 返回实际状态码或默认为500
      };
    }
  }

  /**
   * 调用应用API
   * 用于应用与自身API通信的便捷方法
   */
  async callAppApi<T = any>(
    path: string,
    method: string = "GET",
    data?: any
  ): Promise<T> {
    try {
      const config = {
        method,
        url: path,
        data: method !== "GET" ? data : undefined,
        params: method === "GET" ? data : undefined,
      };

      const response = await this.appApi(config);
      return response.data;
    } catch (error: any) {
      // 检查是否为超时错误
      if (error.code === "ECONNABORTED" || error.message?.includes("timeout")) {
        logger.error(`API call to ${path} timed out`);
        throw new Error(`请求超时: ${path}`);
      }

      logger.error(`API call to ${path} failed:`, error);
      throw error;
    }
  }

  /**
   * 获取请求管理器的调试信息
   * 主要用于开发和调试
   */
  getRequestManagerDebugInfo(): {
    pendingRequestsCount: number;
    pendingRequestKeys: string[];
    cacheStatus: {
      [key: string]: { status: string; age: number; cacheAge?: number };
    };
  } {
    return {
      pendingRequestsCount: this.requestManager.getPendingRequestsCount(),
      pendingRequestKeys: this.requestManager.getPendingRequestKeys(),
      cacheStatus: this.requestManager.getCacheStatus(),
    };
  }

  /**
   * 清除请求管理器缓存
   * 主要用于测试或特殊情况下的重置
   */
  clearRequestCache(): void {
    this.requestManager.clearCache();
  }
}
