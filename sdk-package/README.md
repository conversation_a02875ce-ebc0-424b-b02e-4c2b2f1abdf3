# Quote 统一身份认证客户端 SDK

这是 Quote 系列应用的统一身份认证客户端 SDK，用于与 Quote 统一身份认证服务集成。

## 安装

```bash
npm install @quote/auth-client
```

```bash
npm link @quote/auth-client  # 本地调试
```

## 功能特性

- 统一的认证状态管理
- 无密码登录/注册（基于验证码）
- 基于 HttpOnly Cookie 的安全认证
- 设备指纹和安全验证
- 支持 Next.js 中间件集成
- 敏感域名和路径保护
- 请求超时处理
- **直接与认证服务通信，无需实现本地 API 路由**

## 使用方法

### 1. 配置环境变量

在项目的`.env.local`文件中配置以下环境变量：

```
NEXT_PUBLIC_AUTH_SERVICE_API_URL=https://api.example.com  # 认证服务URL
NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL=https://auth.quote.framesound.tech:3010  # 登录、退出重定向目标 URL
NEXT_PUBLIC_AUTH_DOMAIN=quote.framesound.tech  # 认证服务域名
```

### 2. 基本使用

```typescript
import { AuthClient } from "@quote/auth-client";

// 创建认证客户端实例
const authClient = new AuthClient({
  authServiceApiUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL,
  authServiceRedirectUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL,
  // 可选配置
  sensitiveHostnames: ["app.example.com", "admin.example.com"],
  timeout: 15000, // 请求超时时间，默认为10000ms
});

// 检查认证状态
const checkAuth = async () => {
  const { isAuthenticated, user, statusCode } = await authClient.checkAuth();
  console.log("是否已认证:", isAuthenticated);
  console.log("用户信息:", user);
  console.log("状态码:", statusCode);
};

// 验证用户认证状态（直接调用后端验证端点）
const verifyUserStatus = async () => {
  const result = await authClient.verifyStatus();
  console.log("认证状态:", result.success);
  console.log("消息:", result.message);
  console.log("状态码:", result.statusCode);

  // 基于状态码进行不同处理
  if (result.statusCode === 200) {
    console.log("用户认证有效");
  } else if (result.statusCode === 401) {
    console.log("JWT令牌无效或已过期");
  } else if (result.statusCode === 408) {
    console.log("请求超时");
  } else if (result.statusCode === 500) {
    console.log("系统异常");
  }
};

// 重定向到登录页
const login = (redirectUrl) => {
  // 如果不提供redirectUrl，将使用当前页面URL
  authClient.login(redirectUrl);
};

// 登出
const logout = async () => {
  await authClient.logout();
  // 注意：对于敏感域名，登出后会自动重定向到登录页
  // 对于非敏感域名，登出后会刷新当前页面
};

// 发送验证码
const sendCode = async (email) => {
  const response = await authClient.apiSendVerificationCode(email);
  return response;
};

// 登录或注册
const signIn = async (email, code) => {
  const response = await authClient.signInOrUp(email, code);
  console.log("是否新用户:", response.isNewUser); // 201状态码表示新用户注册成功
  return response;
};

// 获取用户信息
const getUserInfo = async () => {
  const user = await authClient.getUser();
  return user;
};
```

### 3. React Hook 集成

在应用入口处初始化：

```typescript
// _app.tsx 或 layout.tsx
import { initAuthClient } from "@quote/auth-client/react";

// 初始化认证客户端
initAuthClient(
  process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL,
  process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL,
  {
    // 配置敏感域名列表，验证失败时将自动重定向到登录页
    sensitiveHostnames: [
      "example.com", // 整个域名敏感
      "admin.example.com", // 整个域名敏感
      "example.com/dashboard", // 只有dashboard路径敏感
    ],
    timeout: 15000, // 可选：请求超时时间，默认为10000ms
  }
);

function MyApp({ Component, pageProps }) {
  return <Component {...pageProps} />;
}

export default MyApp;
```

在组件中使用：

```typescript
import { useAuth } from "@quote/auth-client/react";

function MyComponent() {
  // 使用useAuth hook获取认证状态和方法
  const {
    isAuthenticated, // 是否已认证
    user, // 用户信息
    loading, // 加载状态
    error, // 错误信息
    statusCode, // HTTP状态码
    login, // 登录方法
    logout, // 登出方法
    refreshUser, // 刷新用户信息
    verifyStatus, // 验证认证状态
  } = useAuth();

  if (loading) return <div>加载中...</div>;

  // 可以根据statusCode做出不同的响应
  if (statusCode === 401) {
    return (
      <div>
        会话已过期，请重新登录 <button onClick={() => login()}>登录</button>
      </div>
    );
  }

  if (error) {
    return <div>发生错误: {error}</div>;
  }

  if (!isAuthenticated) {
    return <button onClick={() => login()}>登录</button>;
  }

  return (
    <div>
      <p>欢迎，{user?.name || user?.email}</p>
      <button onClick={logout}>登出</button>
      <button onClick={refreshUser}>刷新用户信息</button>
      <button
        onClick={async () => {
          const result = await verifyStatus();
          console.log("验证结果:", result);
        }}
      >
        验证状态
      </button>
    </div>
  );
}
```

### 4. Next.js 中间件集成

在项目根目录创建 `middleware.ts` 文件：

```typescript
// middleware.ts
import { authMiddleware } from "@quote/auth-client/middleware";

// 创建认证中间件
export default authMiddleware({
  // 不需要认证的公开路径
  publicPaths: [
    "/about",
    "/contact",
    "/pricing",
    "/api/public", // API公开路径
  ],
  // 可选：覆盖环境变量
  // authServiceRedirectUrl: "https://custom-auth.example.com",
  // authDomain: "example.com"
});

// 配置中间件匹配规则
export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico).*)"],
};
```

## 高级功能

### 敏感域名和路径保护

SDK 支持配置敏感域名和路径列表，用于控制验证失败时的行为：

- **敏感域名/路径**：验证失败（401 错误）会自动重定向到登录页，**登录成功后会重定向回用户的原始访问页面**
- **非敏感域名/路径**：验证失败时会停留在当前页面，不进行自动重定向

此外，敏感域名配置还会影响退出登录的行为：

- **敏感域名/路径**：退出登录后会直接重定向到登录页
- **非敏感域名/路径**：退出登录后会停留在当前页面（刷新页面以清除登录状态）

配置示例：

```typescript
// 使用AuthClient直接配置
const authClient = new AuthClient({
  authServiceApiUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL,
  authServiceRedirectUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL,
  sensitiveHostnames: [
    "app.example.com", // 整个域名敏感
    "admin.example.com", // 整个域名敏感
    "example.com/dashboard", // 只有dashboard路径敏感
    "example.com/admin/users", // 只有admin/users路径敏感
  ],
});
```

### 超时处理

SDK 支持配置请求超时时间，默认为 10 秒（10000ms）。当请求超时时，会返回 408 状态码。

```typescript
// 配置15秒超时
const authClient = new AuthClient({
  authServiceApiUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL,
  authServiceRedirectUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL,
  timeout: 15000, // 15秒
});
```

### 状态码说明

SDK 在验证状态时会返回具体的 HTTP 状态码，以便应用根据不同状态码做出不同响应：

- **200**: 用户认证有效
- **401**: JWT 令牌无效或已过期
- **408**: 请求超时
- **500**: 系统异常

示例：

```typescript
const { isAuthenticated, user, statusCode } = await authClient.checkAuth();

// 基于状态码进行不同处理
switch (statusCode) {
  case 200:
    // 用户认证有效
    showUserDashboard();
    break;
  case 401:
    // JWT令牌无效或已过期
    promptUserToLogin();
    break;
  case 408:
    // 请求超时
    showTimeoutMessage("请求超时，请检查网络连接");
    break;
  case 500:
    // 系统异常
    showErrorMessage("系统暂时不可用，请稍后再试");
    break;
  default:
    // 其他错误
    showGenericError();
}
```

## 重要说明

### 无需实现本地 API 路由

本 SDK 直接与认证服务通信，您**不需要**在自己的项目中实现以下 API 路由：

- `/api/auth/v1/send_verification`
- `/api/auth/v1/sign_in_or_up`
- `/api/auth/v1/logout`
- `/api/auth/v1/verify_status`

注意：`/api/user/me` 端点目前尚未实现，SDK 中的 getUser 方法目前仅从本地存储获取用户信息。

### 与应用自身 API 通信（可选功能）

SDK 提供了`callAppApi`方法，可以用来调用您自己应用的 API。**这是一个可选的便利功能**，您完全可以使用自己的 API 调用方法（如 axios、fetch 等）来代替。

```typescript
// 使用SDK提供的方法调用应用API（可选）
const data = await authClient.callAppApi("/api/products");
const result = await authClient.callAppApi("/api/orders", "POST", {
  productId: 123,
});

// 或者直接使用您自己的API调用方法（推荐）
import axios from "axios";
const data = await axios.get("/api/products");
const result = await axios.post("/api/orders", { productId: 123 });
```

**何时使用`callAppApi`：**

- 如果您希望利用 SDK 已配置的 withCredentials 设置
- 如果您想使用一致的 API 调用接口
- 如果您不想额外配置 axios 或其他 HTTP 客户端

**何时使用自己的 API 调用方法：**

- 如果您已有成熟的 API 调用方法
- 如果您需要更多自定义配置
- 如果您想保持认证逻辑和业务逻辑的清晰分离

## 最佳实践

### 1. 初始化时机

在应用入口处尽早初始化认证客户端，确保认证状态在整个应用中可用：

```typescript
// Next.js App Router (app/layout.tsx)
"use client";

import { initAuthClient } from "@quote/auth-client/react";

export default function RootLayout({ children }) {
  // 在客户端组件中初始化
  if (typeof window !== "undefined") {
    initAuthClient(
      process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL,
      process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL
    );
  }

  return (
    <html>
      <body>{children}</body>
    </html>
  );
}
```

### 2. 路由保护

结合 Next.js 中间件和 React 组件级别保护：

```typescript
// 组件级别保护
"use client";

import { useAuth } from "@quote/auth-client/react";
import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function ProtectedPage() {
  const { isAuthenticated, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      router.push("/login");
    }
  }, [isAuthenticated, loading, router]);

  if (loading) return <div>加载中...</div>;
  if (!isAuthenticated) return null;

  return <div>受保护的内容</div>;
}
```

### 3. 错误处理

妥善处理认证错误，提供友好的用户体验：

```typescript
function AuthErrorHandler() {
  const { error, statusCode } = useAuth();

  if (!error && (!statusCode || statusCode === 200)) return null;

  return (
    <div className="auth-error">
      {statusCode === 401 && <p>您的会话已过期，请重新登录</p>}
      {statusCode === 408 && <p>网络请求超时，请检查您的网络连接</p>}
      {statusCode === 500 && <p>系统暂时不可用，请稍后再试</p>}
      {error && <p>错误: {error}</p>}
    </div>
  );
}
```

### 4. 敏感域名配置

根据应用架构合理配置敏感域名：

- 管理后台和需要严格保护的应用应设为敏感域名
- 公开网站中的会员区域可设为敏感路径
- 完全公开的网站无需设为敏感域名

```typescript
// 推荐配置
initAuthClient(
  process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL,
  process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL,
  {
    sensitiveHostnames: [
      "admin.example.com", // 管理后台（整个域名敏感）
      "app.example.com", // 应用（整个域名敏感）
      "example.com/account", // 账户页面（路径敏感）
      "example.com/dashboard", // 仪表盘（路径敏感）
    ],
  }
);
```

## API 参考

### AuthClient

#### 构造函数

```typescript
new AuthClient(config: AuthClientConfig)
```

**参数：**

- `config.authServiceApiUrl`: 认证服务 API 基础 URL
- `config.authServiceRedirectUrl`: 认证服务重定向 URL
- `config.sensitiveHostnames`: 敏感域名列表（可选）
- `config.timeout`: 请求超时时间，单位毫秒（可选，默认 10000ms）

#### 方法

- `checkAuth()`: 检查用户是否已认证

  - 返回: `Promise<{ isAuthenticated: boolean; user?: User; statusCode?: number; }>`

- `login(redirectUrl?: string)`: 重定向到登录页

  - 参数: `redirectUrl` - 登录成功后重定向的 URL（可选，默认为当前 URL）
  - 返回: `void`

- `logout()`: 登出

  - 返回: `Promise<void>`

- `getUser()`: 获取当前用户信息

  - 返回: `Promise<User | null>`
  - 注意：目前仅从本地存储获取，API 端点尚未实现

- `verifyStatus()`: 验证当前用户认证状态

  - 返回: `Promise<VerifyStatusResponse>`

- `apiSendVerificationCode(contact: string)`: 发送验证码

  - 参数: `contact` - 联系方式（通常是邮箱）
  - 返回: `Promise<AuthResponse>`

- `signInOrUp(contact: string, code: string)`: 登录或注册

  - 参数:
    - `contact` - 联系方式（通常是邮箱）
    - `code` - 验证码
  - 返回: `Promise<SignInOrUpResponse>`

- `callAppApi<T = any>(path: string, method: string = "GET", data?: any)`: 调用应用自身的 API

  - 参数:
    - `path` - API 路径
    - `method` - HTTP 方法（默认为 GET）
    - `data` - 请求数据（可选）
  - 返回: `Promise<T>`

- `isCurrentHostnameSensitive()`: 检查当前主机名是否在敏感域名列表中
  - 返回: `boolean`

### React Hook

#### initAuthClient

```typescript
initAuthClient(
  authServiceApiUrl: string,
  authServiceRedirectUrl: string,
  options?: {
    sensitiveHostnames?: string[];
    timeout?: number;
  }
)
```

**参数：**

- `authServiceApiUrl`: 认证服务 API 基础 URL
- `authServiceRedirectUrl`: 认证服务重定向 URL
- `options`: 可选配置
  - `sensitiveHostnames`: 敏感域名列表
  - `timeout`: 请求超时时间（毫秒）

#### useAuth Hook

```typescript
const {
  isAuthenticated,
  user,
  loading,
  error,
  statusCode,
  login,
  logout,
  refreshUser,
  verifyStatus,
} = useAuth();
```

**返回值：**

- `isAuthenticated`: 是否已认证（boolean）
- `user`: 用户信息（User | null）
- `loading`: 加载状态（boolean）
- `error`: 错误信息（string | null）
- `statusCode`: HTTP 状态码（number | undefined）
- `login(redirectUrl?: string)`: 重定向到登录页
- `logout()`: 登出
- `refreshUser()`: 刷新用户信息
- `verifyStatus()`: 验证当前用户认证状态

### Next.js 中间件

```typescript
authMiddleware(options: AuthMiddlewareOptions)
```

**参数：**

- `options.publicPaths`: 不需要认证的公开路径数组（可选）
- `options.authServiceRedirectUrl`: 认证服务 URL（可选，默认使用环境变量）
- `options.authDomain`: 认证域名（可选，默认使用环境变量）

## 类型定义

### User

```typescript
interface User {
  email: string;
  name?: string;
  avatar?: string;
  role?: string;
  createdAt?: string;
  updatedAt?: string;
}
```

### AuthResponse

```typescript
interface AuthResponse {
  success: boolean;
  user?: User;
  message?: string;
  token?: string;
}
```

### SignInOrUpResponse

```typescript
interface SignInOrUpResponse {
  success: boolean;
  message?: string;
  isNewUser?: boolean;
}
```

### VerifyStatusResponse

```typescript
interface VerifyStatusResponse {
  success: boolean;
  message: string;
  statusCode?: number;
}
```

## 兼容性

- React 18+
- Next.js 13.4+

## 许可证

MIT
