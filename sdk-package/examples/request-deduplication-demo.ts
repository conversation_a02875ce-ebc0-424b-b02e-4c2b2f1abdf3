/**
 * 请求去重和队列管理演示
 * 
 * 这个示例展示了 SDK 如何处理：
 * 1. 相同的 verifyStatus 请求去重
 * 2. 认证相关请求的队列管理
 * 3. 避免瞬间执行多个请求
 */

import { AuthClient } from '../src/client';

// 模拟配置
const config = {
  authServiceApiUrl: 'https://auth.example.com/api',
  authServiceRedirectUrl: 'https://auth.example.com',
  sensitiveHostnames: ['app.example.com'],
  timeout: 10000
};

// 创建认证客户端实例
const authClient = new AuthClient(config);

/**
 * 演示请求去重功能
 */
async function demonstrateRequestDeduplication() {
  console.log('=== 请求去重演示 ===');
  
  // 同时发起多个相同的 verifyStatus 请求
  console.log('同时发起 5 个 verifyStatus 请求...');
  
  const startTime = Date.now();
  
  const promises = Array.from({ length: 5 }, (_, index) => {
    console.log(`启动请求 ${index + 1}`);
    return authClient.verifyStatus();
  });
  
  // 等待所有请求完成
  const results = await Promise.all(promises);
  
  const endTime = Date.now();
  const duration = endTime - startTime;
  
  console.log(`所有请求完成，耗时: ${duration}ms`);
  console.log('结果:', results.map((r, i) => `请求${i + 1}: ${r.success}`));
  
  // 检查调试信息
  const debugInfo = authClient.getRequestManagerDebugInfo();
  console.log('当前缓存的请求数量:', debugInfo.pendingRequestsCount);
  console.log('缓存的请求键:', debugInfo.pendingRequestKeys);
}

/**
 * 演示队列管理功能
 */
async function demonstrateRequestQueue() {
  console.log('\n=== 队列管理演示 ===');
  
  // 清除之前的缓存
  authClient.clearRequestCache();
  
  console.log('按顺序执行认证相关操作...');
  
  const operations = [
    () => {
      console.log('执行操作 1: verifyStatus');
      return authClient.verifyStatus();
    },
    () => {
      console.log('执行操作 2: getUser');
      return authClient.getUser();
    },
    () => {
      console.log('执行操作 3: verifyStatus (应该被去重)');
      return authClient.verifyStatus();
    }
  ];
  
  const startTime = Date.now();
  
  // 同时启动所有操作
  const promises = operations.map(op => op());
  
  // 等待所有操作完成
  const results = await Promise.all(promises);
  
  const endTime = Date.now();
  const duration = endTime - startTime;
  
  console.log(`所有操作完成，耗时: ${duration}ms`);
  console.log('结果数量:', results.length);
}

/**
 * 演示混合场景
 */
async function demonstrateMixedScenario() {
  console.log('\n=== 混合场景演示 ===');
  
  // 清除缓存
  authClient.clearRequestCache();
  
  console.log('模拟真实应用场景：多个组件同时检查认证状态...');
  
  // 模拟多个组件同时调用认证相关方法
  const componentCalls = [
    // 组件 1: 导航栏检查用户状态
    () => {
      console.log('导航栏: 检查认证状态');
      return authClient.checkAuth();
    },
    // 组件 2: 侧边栏获取用户信息
    () => {
      console.log('侧边栏: 获取用户信息');
      return authClient.getUser();
    },
    // 组件 3: 页面内容验证状态
    () => {
      console.log('页面内容: 验证状态');
      return authClient.verifyStatus();
    },
    // 组件 4: 用户头像组件检查认证
    () => {
      console.log('用户头像: 检查认证状态');
      return authClient.checkAuth();
    },
    // 组件 5: 权限检查组件
    () => {
      console.log('权限检查: 验证状态');
      return authClient.verifyStatus();
    }
  ];
  
  const startTime = Date.now();
  
  // 同时启动所有组件的调用
  const promises = componentCalls.map(call => call());
  
  // 等待所有调用完成
  const results = await Promise.all(promises);
  
  const endTime = Date.now();
  const duration = endTime - startTime;
  
  console.log(`所有组件调用完成，耗时: ${duration}ms`);
  console.log('成功的调用数量:', results.filter(r => r && (r.isAuthenticated !== undefined || r.success !== undefined)).length);
  
  // 显示最终的调试信息
  const debugInfo = authClient.getRequestManagerDebugInfo();
  console.log('最终缓存状态:');
  console.log('- 待处理请求数量:', debugInfo.pendingRequestsCount);
  console.log('- 缓存的请求键:', debugInfo.pendingRequestKeys);
}

/**
 * 运行所有演示
 */
async function runAllDemonstrations() {
  try {
    await demonstrateRequestDeduplication();
    await demonstrateRequestQueue();
    await demonstrateMixedScenario();
    
    console.log('\n=== 演示完成 ===');
    console.log('✅ 请求去重机制正常工作');
    console.log('✅ 队列管理机制正常工作');
    console.log('✅ 避免了重复和瞬间执行的问题');
    
  } catch (error) {
    console.error('演示过程中出现错误:', error);
  }
}

// 如果直接运行此文件，则执行演示
if (require.main === module) {
  runAllDemonstrations();
}

export {
  demonstrateRequestDeduplication,
  demonstrateRequestQueue,
  demonstrateMixedScenario,
  runAllDemonstrations
};
