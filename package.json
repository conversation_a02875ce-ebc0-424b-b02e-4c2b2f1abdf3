{"name": "framesound-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3010", "dev:https": "node server.js", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:security": "jest --testPathPattern=.*\\.security\\.test\\.(ts|tsx)$", "test:penetration": "jest --testPathPattern=.*\\.penetration\\.test\\.(ts|tsx)$", "cert": "mkdir -p certificates && mkcert -key-file ./certificates/quote.framesound.tech+3-key.pem -cert-file ./certificates/quote.framesound.tech+3.pem quote.framesound.tech localhost 127.0.0.1 ::1"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.6.2", "@heroui/react": "^2.7.8", "@heroui/theme": "^2.4.15", "@heroui/toast": "^2.0.11", "@heroui/tooltip": "^2.2.19", "clsx": "^2.1.1", "email-validator": "^2.0.4", "framer-motion": "^12.23.0", "gray-matter": "^4.0.3", "immutable": "^5.1.3", "libphonenumber-js": "^1.12.9", "lucide": "^0.518.0", "lucide-react": "^0.511.0", "next": "14.1.4", "next-mdx-remote": "^5.0.0", "next-themes": "^0.4.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-phone-number-input": "^3.4.12", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "rehype-slug": "^6.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "~18.3.1", "@types/react-dom": "~18.3.1", "autoprefixer": "^10", "eslint": "~8.57.1", "eslint-config-next": "~14.1.4", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "jest-fetch-mock": "^3.0.3", "jest-html-reporters": "^3.1.7", "postcss": "^8", "tailwindcss": "~3.4.17", "typescript": "^5"}}