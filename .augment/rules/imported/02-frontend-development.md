---
type: "always_apply"
---

# quote-official 前端开发规范

## 组件开发规范
- 优先使用函数式组件和React Hooks
- 优先使用 TypeScript 类型定义而不是PropTypes
- 组件props类型应明确定义，使用interface而非type
- 尽可能使用memo、useCallback和useMemo优化性能
- 大型组件应拆分成多个小组件

## 样式开发规范
- 原子类优先：全部采用Tailwind工具类，尽可能不要使用CSS/SCSS文件
- 复杂样式可使用Tailwind的@apply指令创建可复用类
- 自定义颜色使用tailwind.config.js中定义的颜色变量
- 遵循桌面端优先、兼容移动端的响应式设计原则

## UI组件开发规范
- 组件分类和存放规范：
  - `/components/ui`：仅存放全局通用的基础UI组件（如按钮、输入框、模态框等）
  - `/components/[feature-name]`：特定功能相关的组件应创建独立的目录（如 editor、sidebar 等）
    - `/components/[feature-name]/components`：用于存放构成该功能大组件的子组件
    - 示例结构：
      ```
      /components
        /editor
          Editor.tsx          # 主组件
          /components         # 编辑器的子组件
            ToolBar.tsx
            Canvas.tsx
            PropertyPanel.tsx
      ```
  - 组件目录命名应清晰表达其功能用途
  - 如果某个功能只需要一个简单组件，可直接在 components 目录下创建单文件组件

## 状态管理
- 简单状态使用useState和useReducer
- 复杂状态或跨组件共享状态使用React Context API
- 避免不必要的全局状态

## 注释规范
- 复杂逻辑需添加注释说明
- 公共API和关键函数需添加JSDoc注释
- TODO和FIXME使用标准格式：// TODO: 内容 或 // FIXME: 内容
