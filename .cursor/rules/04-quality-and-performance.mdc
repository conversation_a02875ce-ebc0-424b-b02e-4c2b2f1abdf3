---
description: 
globs: 
alwaysApply: true
---
# quote-official 质量和性能规范

## 性能优化
- 使用React.memo()、useMemo()和useCallback()避免不必要的重渲染
- 图片应进行适当优化，使用Next.js的Image组件
- 大型组件应使用动态导入和代码分割
- 避免不必要的状态更新和副作用
- 使用Chrome DevTools和React DevTools进行性能分析
- 优先考虑用户体验的关键指标（FCP、LCP、CLS等）

## 代码质量
- 使用ESLint和Prettier保持代码风格一致
- 遵循DRY（Don't Repeat Yourself）原则
- 组件和函数应遵循单一职责原则
- 避免过深的组件嵌套
- 定期进行代码审查和重构
- 使用TypeScript的严格模式，避免any类型

## 错误处理
- 遵循 fail-fast 原则，尽早将错误暴露出来
- 不对错误进行不必要的二次包装
- 仅在必要的地方使用 try/catch 结构
- API 请求错误处理应直接抛出详细的错误信息
- 使用工具函数显示错误提示（如src/utils/toast.ts中的showErrorToast）
- 在组件中捕获和处理错误时，保留原始错误信息
- 对于需要全局处理的错误，使用专门的错误边界组件
- 避免静默失败，确保用户能够看到错误消息

## 测试
- 编写单元测试验证关键功能
- 使用Jest和React Testing Library进行组件测试
- 对复杂逻辑编写详细的测试用例
- 保持测试覆盖率在合理水平
- 测试应独立且可重复运行

## 安全性
- 防止XSS攻击，不直接渲染不受信任的内容
- 使用HTTPS进行API通信
- 敏感数据不应存储在本地存储或URL中
- 实施适当的CORS策略
- 使用安全的依赖包，定期更新

## 可访问性（A11y）
- 遵循WCAG 2.1标准
- 所有交互元素应支持键盘导航
- 使用适当的ARIA属性
- 确保颜色对比度符合标准
- 提供替代文本和标签

## 国际化和本地化
- 早期开发阶段：完全使用纯英文实现界面文本语言
- 后期完善阶段：文本内容应使用i18n解决方案
- 日期、时间和数字格式应考虑本地化
- 考虑不同语言文本长度的变化

## 兼容性
- 支持主流现代浏览器（Chrome、Firefox、Safari、Edge）
- 响应式设计适应不同屏幕尺寸
- 优雅降级处理不支持的功能
- 测试关键功能在不同环境下的表现
