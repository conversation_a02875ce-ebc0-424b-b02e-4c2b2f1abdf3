---
description: 
globs: 
alwaysApply: true
---
# quote-official 项目基础规范

## 技术栈
- React 18.3.1
- Next.js 14.1.4 (使用 App Router)
- TypeScript 5+
- Tailwind CSS 3.4.17（使用内联样式工具类）
- HeroUI 组件库 (@heroui/react, @heroui/theme, @heroui/toast, @heroui/tooltip)
- Framer Motion 12.23.0 (动画库)
- Lucide React 0.511.0 (图标库)
- React Markdown 10.1.0 (Markdown 渲染)
- Next Themes 0.4.6 配合@heroui/theme (主题切换)
- FingerprintJS 4.6.2 (设备指纹识别)
- Tailwind Merge 3.3.1 (样式合并工具)

## 项目结构
```
/src
  /app                    # Next.js 应用目录（使用 App Router）
    /api                  # API 路由处理
      /docs               # 文档相关 API
    /case-study           # 案例研究页面
    /dashboard            # 仪表板页面
    /document             # 文档页面
      /[slug]             # 动态文档路由
    /help-center          # 帮助中心页面
    /login                # 登录页面
    /pricing              # 定价页面
    /settings             # 设置页面
    /use-cases            # 用例页面
    layout.tsx            # 根布局
    page.tsx              # 首页
    providers.tsx         # 全局 Provider 配置
    globals.css           # 全局样式
  /components             # 组件目录
    /page-components      # 页面级组件
      /blog               # 博客相关组件
      /case-study         # 案例研究组件
      /document           # 文档相关组件
      /help-center        # 帮助中心组件
      /overview           # 概览组件
      /pricing            # 定价组件
      /updates            # 更新组件
      /use-cases          # 用例组件
    /ui                   # 通用UI组件（全局共用的基础组件）
      /Footer             # 页脚组件
      /FunctionTabs       # 功能标签组件
      /Navbar             # 导航栏组件
      /loading            # 加载组件
      /welcome            # 欢迎组件
    LoginForm.tsx         # 登录表单
    VerificationForm.tsx  # 验证表单
  /contexts               # React Context
    DocumentContext.tsx   # 文档上下文
  /hooks                  # 自定义React Hooks
    useAuthActions.ts     # 认证操作钩子
    useAuthState.ts       # 认证状态钩子
    useFingerprint.ts     # 设备指纹钩子
    useUser.ts            # 用户钩子
  /lib                    # 库文件
    utils.ts              # 通用工具函数
  /utils                  # 工具函数
    auth.ts               # 认证工具
    docs.ts               # 文档工具
    toast.ts              # 消息提示工具
```

## 命名规范
- 组件使用 PascalCase (例如 `Button.tsx`, `Sidebar.tsx`)
- 工具函数、钩子等使用 camelCase (例如 `useAuth.ts`, `formatDate.ts`)
- 文件名尽量使用有意义的名称，避免缩写
- CSS类名使用 kebab-case 或 Tailwind 类名

## 导入顺序
1. React相关导入 (`import React from "react"`)
2. Next.js相关导入 (`next/link`, `next/navigation` 等)
3. 第三方库导入 (Lucide, HeroUI, Framer Motion 等)
4. 项目内组件导入 (`@/components/*`)
5. 工具函数、钩子等导入 (`@/utils/*`, `@/hooks/*`)
6. 类型导入 (`@/types/*`)
7. 样式导入

## 提交规范
- 使用语义化提交信息（英文）
- 确保代码通过ESLint检查无警告和错误
- 提交前运行 `npm run lint` 检查代码质量

## 项目特性
- **多页面应用**：包含文档系统、案例研究、定价、帮助中心等完整页面
- **文档系统**：支持 Markdown 渲染，动态路由，面包屑导航
- **认证系统**：基于设备指纹的用户认证，支持手机号验证
- **主题切换**：支持亮色/暗色模式（通过 next-themes 实现）
- **响应式设计**：全面支持移动端和桌面端
- **消息提示**：使用 @heroui/toast 实现统一的消息提示
- **图标系统**：使用 Lucide React 图标库
- **性能优化**：遵循 Next.js 最佳实践，支持 SSR/SSG

## 开发规范
- **端口配置**：开发服务器运行在 3010 端口
- **HTTPS 支持**：支持本地 HTTPS 开发环境
- **环境变量**：所有配置通过环境变量管理
- **类型安全**：严格的 TypeScript 配置，确保类型安全
- **代码分割**：按页面和功能模块进行代码分割

## 技术限制
- 确保兼容主流现代浏览器（Chrome 90+, Firefox 88+, Safari 14+）
- 符合Web无障碍标准(WCAG 2.1 AA)
- 确保代码性能和加载速度最优化
- 支持移动端触摸操作和手势
