/**
 * 认证相关工具函数
 */

/**
 * 从环境变量获取敏感域名列表
 * @returns 敏感域名数组
 */
export function getSensitiveHostnames(): string[] {
  const sensitiveHostnamesStr = process.env.NEXT_PUBLIC_SENSITIVE_HOSTNAMES;
  let sensitiveHostnames: string[] = [];

  if (sensitiveHostnamesStr) {
    // 将逗号分隔的字符串转换为数组，并去除空白
    sensitiveHostnames = sensitiveHostnamesStr
      .split(",")
      .map((host) => host.trim());
  } else {
    console.warn("未配置敏感域名列表，使用空列表");
  }

  return sensitiveHostnames;
}

/**
 * 检查当前域名是否为敏感域名
 * @returns 是否为敏感域名
 */
export function isCurrentHostnameSensitive(): boolean {
  // 获取敏感域名列表
  const sensitiveHostnames = getSensitiveHostnames();

  // 如果没有配置敏感域名，则返回false
  if (sensitiveHostnames.length === 0) {
    return false;
  }

  // 获取当前域名
  const currentHostname =
    typeof window !== "undefined" ? window.location.hostname : "";
  const currentPath =
    typeof window !== "undefined" ? window.location.pathname : "";
  const currentHostWithPath = `${currentHostname}${currentPath}`;

  // 检查当前域名是否在敏感域名列表中
  return sensitiveHostnames.some((host) => {
    // 完全匹配
    if (currentHostname === host) return true;
    // 部分路径匹配 (如 example.com/projects)
    if (currentHostWithPath.includes(host)) return true;
    return false;
  });
}

/**
 * 获取登录重定向URL
 */
export const getLoginRedirectUrl = (): string => {
  const redirectUrl = process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL;
  if (!redirectUrl) {
    throw new Error("环境变量 NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL 未设置");
  }
  return redirectUrl;
};

/**
 * 从环境变量获取允许的重定向域名列表
 * @returns 允许的重定向域名数组
 */
export function getAllowedRedirectDomains(): string[] {
  const allowedDomainsStr = process.env.NEXT_PUBLIC_ALLOWED_REDIRECT_DOMAINS;
  let allowedDomains: string[] = [];

  if (allowedDomainsStr) {
    // 将逗号分隔的字符串转换为数组，并去除空白
    allowedDomains = allowedDomainsStr
      .split(",")
      .map((domain) => domain.trim())
      .filter((domain) => domain.length > 0);
  }

  return allowedDomains;
}

/**
 * 验证重定向URL是否安全
 * @param redirectUrl 要验证的重定向URL
 * @param testHostname 测试时使用的当前域名（仅在测试环境中有效）
 * @returns 是否为安全的重定向URL
 */
export function isValidRedirectUrl(
  redirectUrl: string,
  testHostname?: string
): boolean {
  try {
    // 如果是相对路径，检查是否为安全的相对路径
    if (redirectUrl.startsWith("/") && !redirectUrl.startsWith("//")) {
      // 防止路径遍历攻击
      if (redirectUrl.includes("..") || redirectUrl.includes("\\")) {
        return false;
      }
      return true;
    }

    // 如果是绝对URL，进行域名验证
    const url = new URL(redirectUrl);

    // 只允许 http 和 https 协议
    if (url.protocol !== "http:" && url.protocol !== "https:") {
      return false;
    }

    // 获取当前域名
    // 安全检查：testHostname 参数仅在测试环境中有效
    const isTestEnvironment =
      process.env.NODE_ENV === "test" || process.env.NODE_ENV === "development";

    const currentHostname =
      (isTestEnvironment && testHostname) ||
      (typeof window !== "undefined" ? window.location.hostname : "");

    // 构建允许的域名列表
    const allowedDomains = [
      currentHostname,
      ...getAllowedRedirectDomains(),
    ].filter((domain) => domain.length > 0);

    // 检查目标域名是否在允许列表中
    return allowedDomains.some((allowedDomain) => {
      // 完全匹配
      if (url.hostname === allowedDomain) return true;
      // 子域名匹配（如果允许的域名以 . 开头）
      if (
        allowedDomain.startsWith(".") &&
        url.hostname.endsWith(allowedDomain)
      ) {
        return true;
      }
      return false;
    });
  } catch (error) {
    // URL 解析失败，认为不安全
    console.warn("重定向URL解析失败:", redirectUrl, error);
    return false;
  }
}

/**
 * 获取安全的重定向URL
 * @param redirectParam URL参数中的redirect值
 * @returns 安全的重定向URL，如果不安全则返回默认路径
 */
export function getSafeRedirectUrl(redirectParam: string | null): string {
  // 如果没有重定向参数，返回默认路径
  if (!redirectParam) {
    return "/";
  }

  try {
    const decodedUrl = decodeURIComponent(redirectParam);

    // 验证重定向URL是否安全
    if (isValidRedirectUrl(decodedUrl)) {
      return decodedUrl;
    }

    console.warn("不安全的重定向URL被拒绝:", decodedUrl);
    return "/";
  } catch (error) {
    console.error("解析重定向URL失败:", error);
    return "/";
  }
}
