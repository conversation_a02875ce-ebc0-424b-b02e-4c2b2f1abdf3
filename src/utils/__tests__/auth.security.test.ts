/**
 * 重定向安全验证测试
 * 测试 isValidRedirectUrl 函数的安全性，防止开放重定向攻击
 */

import { isValidRedirectUrl, getAllowedRedirectDomains } from "../auth";

// Mock 环境变量
const mockEnv = (allowedDomains: string) => {
  process.env.NEXT_PUBLIC_ALLOWED_REDIRECT_DOMAINS = allowedDomains;
};

// 用于测试的当前域名
let currentTestHostname = "example.com";

describe("重定向安全验证测试", () => {
  beforeEach(() => {
    // 重置环境变量
    delete process.env.NEXT_PUBLIC_ALLOWED_REDIRECT_DOMAINS;
    // 设置默认的当前域名
    currentTestHostname = "example.com";
  });

  describe("✅ 合法重定向URL测试", () => {
    test("应该允许相对路径", () => {
      const validPaths = [
        "/",
        "/dashboard",
        "/user/profile",
        "/api/data",
        "/path/with/multiple/segments",
      ];

      validPaths.forEach((path) => {
        expect(isValidRedirectUrl(path, currentTestHostname)).toBe(true);
      });
    });

    test("应该允许当前域名的绝对URL", () => {
      currentTestHostname = "example.com";
      const validUrls = [
        "https://example.com/",
        "https://example.com/dashboard",
        "http://example.com/api",
      ];

      validUrls.forEach((url) => {
        expect(isValidRedirectUrl(url, currentTestHostname)).toBe(true);
      });
    });

    test("应该允许配置的允许域名", () => {
      mockEnv("trusted.com,api.example.com");
      const validUrls = [
        "https://trusted.com/",
        "https://api.example.com/callback",
        "http://trusted.com/auth",
      ];

      validUrls.forEach((url) => {
        expect(isValidRedirectUrl(url, currentTestHostname)).toBe(true);
      });
    });

    test("应该支持子域名匹配（以.开头的域名）", () => {
      mockEnv(".example.com");
      const validUrls = [
        "https://api.example.com/",
        "https://admin.example.com/dashboard",
        "https://sub.api.example.com/callback",
      ];

      validUrls.forEach((url) => {
        expect(isValidRedirectUrl(url, currentTestHostname)).toBe(true);
      });
    });
  });

  describe("🚨 恶意重定向URL测试", () => {
    test("应该拒绝路径遍历攻击", () => {
      const maliciousUrls = [
        "/../../../etc/passwd",
        "/path/../../../admin",
        "/user/../../config",
        "\\..\\..\\windows\\system32",
        "/path\\..\\admin",
      ];

      maliciousUrls.forEach((url) => {
        expect(isValidRedirectUrl(url, currentTestHostname)).toBe(false);
      });
    });

    test("应该拒绝双斜杠绕过", () => {
      const maliciousUrls = [
        "//evil.com",
        "//evil.com/path",
        "///evil.com",
        "////evil.com",
      ];

      maliciousUrls.forEach((url) => {
        expect(isValidRedirectUrl(url, currentTestHostname)).toBe(false);
      });
    });

    test("应该拒绝未授权的外部域名", () => {
      mockEnv("trusted.com");
      const maliciousUrls = [
        "https://evil.com/",
        "https://malicious.org/steal-data",
        "http://phishing.site/fake-login",
        "https://evil.com.trusted.com/", // 域名欺骗
      ];

      maliciousUrls.forEach((url) => {
        expect(isValidRedirectUrl(url, currentTestHostname)).toBe(false);
      });
    });

    test("应该拒绝危险协议", () => {
      const maliciousUrls = [
        'javascript:alert("xss")',
        'data:text/html,<script>alert("xss")</script>',
        "file:///etc/passwd",
        "ftp://evil.com/malware",
        "mailto:<EMAIL>",
        "tel:+1234567890",
      ];

      maliciousUrls.forEach((url) => {
        expect(isValidRedirectUrl(url, currentTestHostname)).toBe(false);
      });
    });

    test("应该拒绝URL编码绕过尝试", () => {
      const maliciousUrls = [
        "https://<EMAIL>/",
        "https://trusted.com%2eevil.com/",
        "https://trusted.com%252eevil.com/",
        decodeURIComponent("https://<EMAIL>/"),
      ];

      maliciousUrls.forEach((url) => {
        expect(isValidRedirectUrl(url, currentTestHostname)).toBe(false);
      });
    });

    test("应该拒绝子域名欺骗", () => {
      mockEnv("trusted.com");
      const maliciousUrls = [
        "https://trusted.com.evil.com/",
        "https://evil-trusted.com/",
        "https://trustedcom.evil.com/",
        "https://sub.trusted.com.evil.com/",
      ];

      maliciousUrls.forEach((url) => {
        expect(isValidRedirectUrl(url, currentTestHostname)).toBe(false);
      });
    });
  });

  describe("🔍 边界情况测试", () => {
    test("应该处理空字符串和null", () => {
      expect(isValidRedirectUrl("", currentTestHostname)).toBe(false);
      expect(isValidRedirectUrl(" ", currentTestHostname)).toBe(false);
      expect(isValidRedirectUrl("\t", currentTestHostname)).toBe(false);
      expect(isValidRedirectUrl("\n", currentTestHostname)).toBe(false);
    });

    test("应该处理格式错误的URL", () => {
      const invalidUrls = [
        "not-a-url",
        "https://",
        "https:///",
        "https:///path",
        "http://[invalid-ipv6",
      ];

      invalidUrls.forEach((url) => {
        expect(isValidRedirectUrl(url, currentTestHostname)).toBe(false);
      });
    });

    test("应该处理极长的URL", () => {
      const longPath = "/path/" + "a".repeat(10000);
      const longUrl = "https://evil.com/" + "a".repeat(10000);

      expect(isValidRedirectUrl(longPath, currentTestHostname)).toBe(true); // 相对路径应该允许
      expect(isValidRedirectUrl(longUrl, currentTestHostname)).toBe(false); // 外部域名应该拒绝
    });

    test("应该处理Unicode和国际化域名", () => {
      mockEnv("测试.com,xn--0zwm56d.com");
      const unicodeUrls = [
        "https://测试.com/",
        "https://xn--0zwm56d.com/", // 测试.com 的 punycode
        "https://例え.テスト/",
      ];

      // 这些测试可能需要根据实际需求调整
      unicodeUrls.forEach((url) => {
        // 测试函数是否能正确处理，不会崩溃
        expect(() => isValidRedirectUrl(url, currentTestHostname)).not.toThrow();
      });
    });
  });

  describe("⚙️ 配置测试", () => {
    test("getAllowedRedirectDomains 应该正确解析环境变量", () => {
      mockEnv("domain1.com, domain2.com , domain3.com");
      const domains = getAllowedRedirectDomains();
      expect(domains).toEqual(["domain1.com", "domain2.com", "domain3.com"]);
    });

    test("应该处理空的环境变量", () => {
      mockEnv("");
      const domains = getAllowedRedirectDomains();
      expect(domains).toEqual([]);
    });

    test("应该过滤空白域名", () => {
      mockEnv("domain1.com, , domain2.com,  ,domain3.com");
      const domains = getAllowedRedirectDomains();
      expect(domains).toEqual(["domain1.com", "domain2.com", "domain3.com"]);
    });
  });
});
