/**
 * 生产环境安全测试
 * 验证在生产环境中测试参数不会被恶意利用
 */

import { isValidRedirectUrl } from "../auth";

describe("🔒 生产环境安全测试", () => {
  const originalNodeEnv = process.env.NODE_ENV;

  afterEach(() => {
    // 恢复原始环境变量
    process.env.NODE_ENV = originalNodeEnv;
  });

  test("在生产环境中应该忽略 testHostname 参数", () => {
    // 模拟生产环境
    process.env.NODE_ENV = "production";

    // 尝试使用 testHostname 参数绕过安全检查
    const maliciousUrl = "https://evil.com/steal-data";
    const result = isValidRedirectUrl(maliciousUrl, "evil.com");

    // 在生产环境中，testHostname 应该被忽略，返回 false
    expect(result).toBe(false);
  });

  test("在测试环境中 testHostname 参数应该有效", () => {
    // 确保在测试环境中
    process.env.NODE_ENV = "test";

    const testUrl = "https://test.com/";
    const result = isValidRedirectUrl(testUrl, "test.com");

    // 在测试环境中，testHostname 应该有效
    expect(result).toBe(true);
  });

  test("在开发环境中 testHostname 参数应该有效", () => {
    // 模拟开发环境
    process.env.NODE_ENV = "development";

    const testUrl = "https://dev.com/";
    const result = isValidRedirectUrl(testUrl, "dev.com");

    // 在开发环境中，testHostname 应该有效
    expect(result).toBe(true);
  });

  test("在未知环境中应该忽略 testHostname 参数", () => {
    // 模拟未知环境
    process.env.NODE_ENV = "unknown";

    const maliciousUrl = "https://evil.com/";
    const result = isValidRedirectUrl(maliciousUrl, "evil.com");

    // 在未知环境中，testHostname 应该被忽略
    expect(result).toBe(false);
  });

  test("验证生产环境中的正常功能不受影响", () => {
    // 模拟生产环境
    process.env.NODE_ENV = "production";

    // 测试相对路径（应该允许）
    expect(isValidRedirectUrl("/dashboard")).toBe(true);

    // 测试恶意URL（应该拒绝）
    expect(isValidRedirectUrl("https://evil.com/")).toBe(false);
    expect(isValidRedirectUrl('javascript:alert("xss")')).toBe(false);
    expect(isValidRedirectUrl("//evil.com")).toBe(false);
  });
});
