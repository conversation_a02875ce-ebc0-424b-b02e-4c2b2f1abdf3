/**
 * 重定向安全渗透测试
 * 模拟真实攻击场景，测试开放重定向漏洞
 */

import { isValidRedirectUrl } from '../auth';

// Mock 环境变量和 window.location
const setupTestEnvironment = (currentDomain: string, allowedDomains: string = '') => {
  process.env.NEXT_PUBLIC_ALLOWED_REDIRECT_DOMAINS = allowedDomains;
  Object.defineProperty(window, 'location', {
    value: { hostname: currentDomain },
    writable: true,
  });
};

describe('🔴 重定向安全渗透测试', () => {
  beforeEach(() => {
    delete process.env.NEXT_PUBLIC_ALLOWED_REDIRECT_DOMAINS;
  });

  describe('🎯 真实攻击场景模拟', () => {
    test('钓鱼攻击场景', () => {
      setupTestEnvironment('bank.com', 'api.bank.com');
      
      // 攻击者尝试重定向到钓鱼网站
      const phishingUrls = [
        'https://bank-security-update.evil.com/',
        'https://secure-bank.com/', // 相似域名
        'https://bank.com.evil.com/', // 子域名欺骗
        'https://bank.co.evil.com/', // 域名变体
        'https://bаnk.com/', // 使用西里尔字母 'а' 替代 'a'
      ];

      phishingUrls.forEach(url => {
        expect(isValidRedirectUrl(url)).toBe(false);
      });
    });

    test('OAuth 重定向攻击', () => {
      setupTestEnvironment('oauth.service.com', 'app1.com,app2.com');
      
      // 攻击者尝试劫持 OAuth 回调
      const maliciousCallbacks = [
        'https://evil.com/oauth/callback',
        'https://app1.com.evil.com/callback',
        'https://fake-app1.com/callback',
        'https://app1-com.evil.com/callback',
      ];

      maliciousCallbacks.forEach(url => {
        expect(isValidRedirectUrl(url)).toBe(false);
      });

      // 合法的回调应该被允许
      const legitimateCallbacks = [
        'https://app1.com/oauth/callback',
        'https://app2.com/auth/success',
      ];

      legitimateCallbacks.forEach(url => {
        expect(isValidRedirectUrl(url)).toBe(true);
      });
    });

    test('SSO 重定向劫持', () => {
      setupTestEnvironment('sso.company.com', '.company.com');
      
      // 攻击者尝试劫持 SSO 重定向
      const ssoAttacks = [
        'https://company.com.evil.com/',
        'https://evil-company.com/',
        'https://company.evil.com/',
        'https://sso.company.com.attacker.com/',
      ];

      ssoAttacks.forEach(url => {
        expect(isValidRedirectUrl(url)).toBe(false);
      });

      // 合法的公司子域名应该被允许
      const legitimateSubdomains = [
        'https://app.company.com/',
        'https://admin.company.com/dashboard',
        'https://api.company.com/callback',
      ];

      legitimateSubdomains.forEach(url => {
        expect(isValidRedirectUrl(url)).toBe(true);
      });
    });
  });

  describe('🛠️ 高级绕过技术测试', () => {
    test('URL 解析混淆攻击', () => {
      setupTestEnvironment('trusted.com');
      
      const confusionAttacks = [
        'https://<EMAIL>/',
        'https://trusted.com:evil.com/',
        'https://trusted.com#@evil.com/',
        'https://trusted.com?redirect=evil.com',
        'https://trusted.com/<EMAIL>',
        'https://trusted.com\\@evil.com',
      ];

      confusionAttacks.forEach(url => {
        expect(isValidRedirectUrl(url)).toBe(false);
      });
    });

    test('协议混淆攻击', () => {
      const protocolAttacks = [
        'HTTPS://evil.com/', // 大写协议
        'hTTps://evil.com/', // 混合大小写
        'https\u0000://evil.com/', // 空字节注入
        'https\u0009://evil.com/', // Tab 字符
        'https\u000A://evil.com/', // 换行符
        'https\u000D://evil.com/', // 回车符
      ];

      protocolAttacks.forEach(url => {
        expect(isValidRedirectUrl(url)).toBe(false);
      });
    });

    test('IP 地址绕过尝试', () => {
      setupTestEnvironment('***********', '********');
      
      const ipAttacks = [
        'https://***********/', // 不同 IP
        'https://127.0.0.1/', // localhost
        'https://0.0.0.0/', // 任意地址
        'https://[::1]/', // IPv6 localhost
        'https://[2001:db8::1]/', // IPv6 地址
      ];

      ipAttacks.forEach(url => {
        expect(isValidRedirectUrl(url)).toBe(false);
      });

      // 允许的 IP 应该通过
      expect(isValidRedirectUrl('https://********/')).toBe(true);
    });

    test('端口号绕过尝试', () => {
      setupTestEnvironment('app.com');
      
      const portAttacks = [
        'https://app.com:<EMAIL>/',
        'https://app.com:443:evil.com/',
        'https://app.com:80/redirect?url=evil.com',
      ];

      portAttacks.forEach(url => {
        expect(isValidRedirectUrl(url)).toBe(false);
      });
    });
  });

  describe('🔬 边界条件压力测试', () => {
    test('极限长度 URL 测试', () => {
      const maxLength = 2048; // 常见浏览器 URL 长度限制
      const longDomain = 'a'.repeat(maxLength - 20) + '.evil.com';
      const longUrl = `https://${longDomain}/`;
      
      // 应该能处理而不崩溃
      expect(() => isValidRedirectUrl(longUrl)).not.toThrow();
      expect(isValidRedirectUrl(longUrl)).toBe(false);
    });

    test('特殊字符注入测试', () => {
      const specialChars = [
        'https://evil.com/\x00', // 空字节
        'https://evil.com/\x0A', // 换行
        'https://evil.com/\x0D', // 回车
        'https://evil.com/\x09', // Tab
        'https://evil.com/\x20', // 空格
        'https://evil.com/\xFF', // 高位字符
      ];

      specialChars.forEach(url => {
        expect(() => isValidRedirectUrl(url)).not.toThrow();
        expect(isValidRedirectUrl(url)).toBe(false);
      });
    });

    test('递归重定向检测', () => {
      setupTestEnvironment('app.com');
      
      // 模拟可能导致重定向循环的 URL
      const recursiveUrls = [
        'https://app.com/redirect?url=https://app.com/redirect',
        'https://app.com/auth?redirect=https://app.com/auth',
      ];

      recursiveUrls.forEach(url => {
        // 函数本身不检测递归，但应该允许合法的同域 URL
        expect(isValidRedirectUrl(url)).toBe(true);
      });
    });
  });

  describe('📊 性能和稳定性测试', () => {
    test('大量并发测试', () => {
      setupTestEnvironment('test.com');
      
      const testUrls = [
        'https://evil.com/',
        '/safe/path',
        'https://test.com/safe',
        'javascript:alert(1)',
        '//evil.com',
      ];

      // 模拟大量并发请求
      const promises = Array.from({ length: 1000 }, (_, i) => {
        const url = testUrls[i % testUrls.length];
        return Promise.resolve(isValidRedirectUrl(url));
      });

      return Promise.all(promises).then(results => {
        expect(results).toHaveLength(1000);
        // 验证结果的一致性
        results.forEach((result, i) => {
          const url = testUrls[i % testUrls.length];
          const expected = isValidRedirectUrl(url);
          expect(result).toBe(expected);
        });
      });
    });

    test('内存泄漏检测', () => {
      setupTestEnvironment('test.com');
      
      // 创建大量字符串，测试是否有内存泄漏
      for (let i = 0; i < 10000; i++) {
        const testUrl = `https://test${i}.evil.com/path${i}`;
        isValidRedirectUrl(testUrl);
      }
      
      // 如果有内存泄漏，这个测试可能会超时或崩溃
      expect(true).toBe(true); // 能执行到这里说明没有严重的内存问题
    });
  });
});
