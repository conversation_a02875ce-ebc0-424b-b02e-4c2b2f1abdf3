import fs from "fs";
import path from "path";
import matter from "gray-matter";

const DOCS_DIRECTORY = path.join(process.cwd(), "public/docs");

/**
 * 文档元数据类型
 */
export interface DocMetadata {
  id: string;
  title: string;
  description?: string;
  lastUpdated?: string;
  path?: string; // 文档路径，用于分类
  order?: number; // 排序字段
  [key: string]: any;
}

/**
 * 文档内容类型
 */
export interface DocContent {
  metadata: DocMetadata;
  content: string;
}

/**
 * 文件夹内容项类型
 */
export interface FolderItem {
  id: string;
  title: string;
  type: "folder" | "file";
  description?: string;
}

/**
 * 文件夹内容类型
 */
export interface FolderContent {
  id: string;
  title: string;
  children: FolderItem[];
}

// 预定义的文档分类
export const DOCUMENT_CATEGORIES = [
  {
    id: "get-started",
    title: "Get Started",
    order: 1,
  },
  {
    id: "tutorial",
    title: "Tutorial",
    order: 2,
  },
  {
    id: "advanced",
    title: "Advanced",
    order: 3,
  },
  {
    id: "faq",
    title: "FAQ",
    order: 4,
  },
];

/**
 * 获取文档路径
 */
function getDocPath(docId: string): string {
  // 首先尝试直接匹配文件
  let docPath = path.join(DOCS_DIRECTORY, `${docId}.md`);

  // 如果文件不存在，尝试在子目录中查找
  if (!fs.existsSync(docPath)) {
    // 遍历所有可能的父目录
    const dirs = fs
      .readdirSync(DOCS_DIRECTORY, { withFileTypes: true })
      .filter((dirent) => dirent.isDirectory())
      .map((dirent) => dirent.name);

    for (const dir of dirs) {
      const potentialPath = path.join(DOCS_DIRECTORY, dir, `${docId}.md`);
      if (fs.existsSync(potentialPath)) {
        docPath = potentialPath;
        break;
      }
    }
  }

  return docPath;
}

/**
 * 获取文档内容
 */
export async function getDocContent(docId: string): Promise<DocContent | null> {
  try {
    const docPath = getDocPath(docId);

    if (!fs.existsSync(docPath)) {
      return null;
    }

    const fileContent = fs.readFileSync(docPath, "utf8");
    const { data, content } = matter(fileContent);

    return {
      metadata: {
        id: docId,
        title: data.title || docId,
        description: data.description,
        lastUpdated: data.lastUpdated,
        path: data.path,
        order: data.order,
        ...data,
      },
      content,
    };
  } catch (error) {
    console.error(`Error reading document ${docId}:`, error);
    return null;
  }
}

/**
 * 获取文件夹内容
 */
export async function getFolderContent(
  folderId: string
): Promise<FolderContent | null> {
  try {
    const folderPath = path.join(DOCS_DIRECTORY, folderId);

    // 检查文件夹是否存在
    if (!fs.existsSync(folderPath) || !fs.statSync(folderPath).isDirectory()) {
      return null;
    }

    // 读取目录下的所有Markdown文件
    const mdFiles = fs
      .readdirSync(folderPath)
      .filter((file) => file.endsWith(".md"));

    // 如果没有Markdown文件，返回null
    if (mdFiles.length === 0) {
      return null;
    }

    // 解析每个Markdown文件，提取标题和描述
    const children: FolderItem[] = await Promise.all(
      mdFiles.map(async (file) => {
        const fileId = path.basename(file, ".md");
        const filePath = path.join(folderPath, file);
        const fileContent = fs.readFileSync(filePath, "utf8");
        const { data } = matter(fileContent);

        return {
          id: fileId,
          title: data.title || fileId,
          type: "file",
          description: data.description,
        };
      })
    );

    // 查找预定义分类
    const category = DOCUMENT_CATEGORIES.find((cat) => cat.id === folderId);
    const folderTitle = category
      ? category.title
      : folderId.charAt(0).toUpperCase() + folderId.slice(1).replace(/-/g, " ");

    return {
      id: folderId,
      title: folderTitle,
      children,
    };
  } catch (error) {
    console.error(`Error reading folder ${folderId}:`, error);
    return null;
  }
}

/**
 * 获取所有文档目录结构
 */
export async function getDocumentTree() {
  try {
    // 创建基于预定义分类的树结构
    const tree: TreeNode[] = DOCUMENT_CATEGORIES.map((category) => ({
      id: category.id,
      title: category.title,
      type: "folder" as const,
      expanded: true, // 默认展开
      children: [], // 初始化为空数组
      order: category.order,
    }));

    // 获取所有文档文件
    const allDocs: { id: string; path: string; metadata: any }[] = [];

    // 递归获取所有文档
    const readDocsRecursively = (dir: string, basePath: string = "") => {
      const items = fs.readdirSync(dir, { withFileTypes: true });

      for (const item of items) {
        const fullPath = path.join(dir, item.name);

        if (item.isDirectory()) {
          readDocsRecursively(fullPath, path.join(basePath, item.name));
        } else if (item.name.endsWith(".md")) {
          try {
            const fileContent = fs.readFileSync(fullPath, "utf8");
            const { data } = matter(fileContent);
            const id = path.basename(item.name, ".md");

            // 提取相对路径作为文档路径
            const relativePath = path.relative(DOCS_DIRECTORY, fullPath);
            const docPath = path.dirname(relativePath);

            allDocs.push({
              id,
              path: docPath !== "." ? docPath : "",
              metadata: {
                ...data,
                id,
                title: data.title || id,
                path: data.path || docPath,
                order: data.order || 999, // 默认排序值
              },
            });
          } catch (err) {
            console.error(`Error reading file ${fullPath}:`, err);
          }
        }
      }
    };

    readDocsRecursively(DOCS_DIRECTORY);

    // 将文档分配到相应的分类中
    for (const doc of allDocs) {
      // 确定文档应该归属的分类
      let categoryId = "";

      // 优先使用metadata中的path字段
      if (doc.metadata.path) {
        categoryId = doc.metadata.path.split("/")[0];
      }
      // 否则使用文件系统路径
      else if (doc.path) {
        categoryId = doc.path.split("/")[0];
      }

      // 查找对应的分类
      const categoryIndex = tree.findIndex((cat) => cat.id === categoryId);

      if (categoryIndex !== -1) {
        // 将文档添加到对应分类
        tree[categoryIndex].children!.push({
          id: doc.id,
          title: doc.metadata.title,
          type: "file" as const,
          description: doc.metadata.description,
          order: doc.metadata.order || 999,
        });
      } else {
        // 如果找不到对应分类，创建"其他"分类
        let otherCategory = tree.find((cat) => cat.id === "other");
        if (!otherCategory) {
          otherCategory = {
            id: "other",
            title: "Other",
            type: "folder" as const,
            expanded: true,
            children: [],
            order: 999, // 放在最后
          };
          tree.push(otherCategory);
        }

        otherCategory.children!.push({
          id: doc.id,
          title: doc.metadata.title,
          type: "file" as const,
          description: doc.metadata.description,
          order: doc.metadata.order || 999,
        });
      }
    }

    // 对每个分类中的文档按order排序
    for (const category of tree) {
      if (category.children && category.children.length > 0) {
        category.children.sort((a, b) => {
          const orderA = (a as any).order || 999;
          const orderB = (b as any).order || 999;
          return orderA - orderB;
        });
      }
    }

    // 对分类进行排序
    tree.sort((a, b) => {
      const orderA = a.order || 999;
      const orderB = b.order || 999;
      return orderA - orderB;
    });

    return tree;
  } catch (error) {
    console.error("Error reading document tree:", error);
    return [];
  }
}

// 为TypeScript类型定义添加TreeNode接口
interface TreeNode {
  id: string;
  title: string;
  type: "folder" | "file";
  expanded?: boolean;
  children?: TreeNode[];
  order?: number;
  description?: string;
}
