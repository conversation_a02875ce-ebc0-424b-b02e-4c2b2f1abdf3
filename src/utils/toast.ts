import { addToast } from "@heroui/toast";

export const showToast = (
  message: string,
  type:
    | "default"
    | "primary"
    | "secondary"
    | "success"
    | "warning"
    | "danger" = "default"
) => {
  addToast({
    title: message,
    color: type,
  });
};

export const showSuccessToast = (message: string) => {
  showToast(message, "success");
};

export const showErrorToast = (message: string) => {
  showToast(message, "danger");
};

export const showWarningToast = (message: string) => {
  showToast(message, "warning");
};

export const showInfoToast = (message: string) => {
  showToast(message, "primary");
};
