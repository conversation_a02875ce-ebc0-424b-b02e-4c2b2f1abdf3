"use client";

import { <PERSON><PERSON><PERSON>rovider } from "@heroui/react";
import { ToastProvider } from "@heroui/toast";
import { ThemeProvider } from "next-themes";
import React, { useEffect, useState, useRef } from "react";
import { DocumentProvider } from "@/contexts/DocumentContext";
import { initAuthClient } from "@quote/auth-client/react";
import { showSuccessToast } from "@/utils/toast";
import FullScreenLoading from "@/components/ui/loading/FullScreenLoading";
import {
  getSensitiveHostnames,
  isCurrentHostnameSensitive,
} from "@/utils/auth";
import { useAuthActions } from "@/hooks/useAuthActions";
import { usePathname } from "next/navigation";
import logger from "@/utils/logger";

// 立即执行初始化认证客户端
if (typeof window !== "undefined") {
  try {
    // 使用环境变量
    const authServiceApiUrl = process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL;
    const authServiceRedirectUrl =
      process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL;

    if (!authServiceApiUrl || !authServiceRedirectUrl) {
      logger.error("Auth Service API URL 或 Auth Service Redirect URL 未设置");
    } else {
      // 使用公共函数获取敏感域名列表
      const sensitiveHostnames = getSensitiveHostnames();

      // 添加敏感域名列表配置
      initAuthClient(authServiceApiUrl, authServiceRedirectUrl, {
        sensitiveHostnames,
        timeout: 10000, // 请求超时时间
      });

      logger.log("认证客户端初始化成功，已配置敏感域名");
    }
  } catch (error) {
    logger.error("认证客户端初始化失败:", error);
  }
}

// 应用提供者
export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
      <HeroUIProvider>
        <ToastProvider
          placement="top-center"
          maxVisibleToasts={3}
          toastProps={{
            color: "primary",
            variant: "flat",
            radius: "md",
            timeout: 4000,
          }}
        />
        <AuthStateProvider>
          <DocumentProvider>{children}</DocumentProvider>
        </AuthStateProvider>
      </HeroUIProvider>
    </ThemeProvider>
  );
}

// 添加一个认证状态包装组件
function AuthStateProvider({ children }: { children: React.ReactNode }) {
  // 使用统一的认证hook，现在包含 SSO 重定向检查功能
  const {
    isAuthenticated,
    loading,
    statusCode,
    error,
    checkSSORedirect,
    verifyStatus,
  } = useAuthActions();
  // 检查当前是否为敏感域名
  const [isSensitiveHost, setIsSensitiveHost] = useState(false);
  // 是否已完成敏感域名检查
  const [hostCheckComplete, setHostCheckComplete] = useState(false);
  // 获取当前路径，用于监听路由变化
  const pathname = usePathname();
  // 使用 useRef 跟踪是否已经处理过 SSO 重定向，避免重复执行
  const ssoRedirectHandled = useRef(false);

  // 检查认证客户端状态
  useEffect(() => {
    if (error) {
      logger.warn("认证客户端可能未正确初始化，请检查环境变量配置");
    }
  }, [error]);

  // 检查当前是否为敏感域名的函数
  const checkSensitiveHost = () => {
    if (typeof window !== "undefined") {
      try {
        const sensitive = isCurrentHostnameSensitive();
        setIsSensitiveHost(sensitive);
        logger.log("当前域名是否为敏感域名:", sensitive, "路径:", pathname);
      } catch (error) {
        logger.error("检查敏感域名时出错:", error);
      } finally {
        setHostCheckComplete(true);
      }
    }
  };

  // 初始检查
  useEffect(() => {
    checkSensitiveHost();
  }, []);

  // 当路径变化时重新检查
  useEffect(() => {
    checkSensitiveHost();
  }, [pathname]);

  // SSO 重定向检查和处理
  useEffect(() => {
    const handleSSORedirect = async () => {
      // 只在客户端执行，且尚未处理过 SSO 重定向
      if (typeof window === "undefined" || ssoRedirectHandled.current) return;

      try {
        // 检查是否是 SSO 重定向
        if (checkSSORedirect) {
          const ssoState = checkSSORedirect();

          if (ssoState.isSSORedirect) {
            logger.log("检测到 SSO 重定向，验证登录状态");
            ssoRedirectHandled.current = true;

            // 验证登录状态
            const result = await verifyStatus();
            if (result.success) {
              logger.log("SSO 重定向验证成功");
              showSuccessToast("Welcome back! Login successful.");
            } else {
              logger.warn("SSO 重定向验证失败:", result);
            }

            // 清除 SSO 标记
            ssoState.clear();
          }
        }
      } catch (error) {
        logger.error("处理 SSO 重定向时出错:", error);
      }
    };

    handleSSORedirect();
  }, []); // 只在组件挂载时执行一次

  // 检测认证状态变化
  useEffect(() => {
    // 如果认证已完成(不在加载中)
    if (!loading) {
      if (isAuthenticated) {
        logger.log("认证通过，状态码:", statusCode);
      } else {
        logger.log("认证未通过，状态码:", statusCode);

        // 用户退出登录时，重置 SSO 重定向处理状态
        if (ssoRedirectHandled.current) {
          ssoRedirectHandled.current = false;
          logger.log("重置 SSO 重定向处理状态");
        }
      }
    }
  }, [loading, isAuthenticated, statusCode]);

  // 如果敏感域名检查尚未完成，显示加载状态
  if (!hostCheckComplete) {
    return <FullScreenLoading spinnerSize={24} />;
  }

  // 敏感域名检查已完成
  if (isSensitiveHost) {
    // 如果是敏感域名
    if (loading) {
      // 认证状态正在加载中，显示加载状态
      return <FullScreenLoading spinnerSize={24} text="正在验证身份" />;
    }

    if (!isAuthenticated) {
      // 认证未通过，显示加载状态（此时认证系统应该会自动跳转到登录页）
      return <FullScreenLoading spinnerSize={24} />;
    }

    // 认证通过，显示子组件
    return <>{children}</>;
  } else {
    // 非敏感域名
    if (loading) {
      // 认证状态正在加载中，显示加载状态
      return <FullScreenLoading spinnerSize={24} />;
    }

    // 非敏感域名，无论认证状态如何都显示子组件
    return <>{children}</>;
  }
}

// 导出useAuth hook以便在应用中使用
export { useAuthActions };
