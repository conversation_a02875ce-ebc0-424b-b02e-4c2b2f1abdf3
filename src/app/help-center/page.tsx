import React from "react";
import Navigator from "../../components/ui/Navbar/Navigator";
import Footer from "../../components/ui/Footer/Footer";
import HelpCenterHero from "../../components/page-components/help-center/HelpCenterHero";
import GettingStartedGuide from "../../components/page-components/help-center/GettingStartedGuide";
import MoreResources from "../../components/page-components/help-center/MoreResources";
import FloatingConsultButton from "../../components/ui/FloatingConsultButton";

export default function HelpCenter() {
  return (
    <div className="min-h-screen bg-background relative">
      {/* 背景渐变 - 移动到最外层容器 */}
      <div className="absolute inset-0 bg-gradient-to-br from-background to-[#F0F0FF] z-0"></div>

      {/* 顶部导航 */}
      <Navigator currentPath="/help-center" />

      {/* 悬浮咨询按钮 */}
      <FloatingConsultButton />

      {/* 内容区域 */}
      <div className="relative z-10">
        {/* Hero Section */}
        <HelpCenterHero
          title={
            <>
              Quote <span className="text-primary">帮助中心</span>
            </>
          }
          subtitle="获取 Quote 产品的帮助和支持，解决您在使用过程中遇到的问题"
        />

        {/* 入门指南模块 */}
        <GettingStartedGuide />

        {/* 更多资源模块 */}
        <MoreResources />

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}
