"use client";

import React from "react";
import { useAuthActions } from "@/hooks/useAuthActions";

export default function Dashboard() {
  const { isAuthenticated, user, handleLogout } = useAuthActions();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            项目管理
          </h1>

          {isAuthenticated && (
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-600 dark:text-gray-300">
                欢迎，{user?.name || user?.email || "用户"}
              </div>
              <button
                onClick={handleLogout}
                className="px-3 py-1.5 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                登出
              </button>
            </div>
          )}
        </div>

        <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg p-6">
          <div className="text-center py-12">
            <div className="mb-4 text-gray-400 dark:text-gray-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-16 w-16 mx-auto"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <h2 className="text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">
              暂无项目
            </h2>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              您尚未创建任何项目，点击下方按钮创建您的第一个项目
            </p>
            <button className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors">
              创建新项目
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
