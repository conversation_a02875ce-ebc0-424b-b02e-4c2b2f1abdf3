import { NextRequest, NextResponse } from "next/server";
import { getDocContent, getFolderContent } from "@/utils/docs";

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  const path = params.path || [];

  // 请求文档树结构
  if (path[0] === "tree") {
    try {
      const { getDocumentTree } = await import("@/utils/docs");
      const tree = await getDocumentTree();
      return NextResponse.json(tree);
    } catch (error: any) {
      return NextResponse.json(
        { error: "Failed to load document tree", message: error.message },
        { status: 500 }
      );
    }
  }

  // 请求文件夹内容
  if (path[0] === "folder" && path[1]) {
    try {
      const folderId = path[1];
      const folderContent = await getFolderContent(folderId);

      if (!folderContent) {
        return NextResponse.json(
          { error: "Folder not found" },
          { status: 404 }
        );
      }

      return NextResponse.json(folderContent);
    } catch (error: any) {
      return NextResponse.json(
        { error: "Failed to load folder content", message: error.message },
        { status: 500 }
      );
    }
  }

  // 请求文档内容
  if (path[0] === "content" && path[1]) {
    try {
      const docId = path[1];
      const doc = await getDocContent(docId);

      if (!doc) {
        return NextResponse.json(
          { error: "Document not found" },
          { status: 404 }
        );
      }

      return NextResponse.json(doc);
    } catch (error: any) {
      return NextResponse.json(
        { error: "Failed to load document", message: error.message },
        { status: 500 }
      );
    }
  }

  // 无效的路径
  return NextResponse.json({ error: "Invalid API path" }, { status: 400 });
}
