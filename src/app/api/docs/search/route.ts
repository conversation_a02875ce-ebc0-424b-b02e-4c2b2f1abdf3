import { NextRequest, NextResponse } from "next/server";
import fs from "fs";
import path from "path";
import matter from "gray-matter";

const DOCS_DIRECTORY = path.join(process.cwd(), "public/docs");

// 文档搜索结果类型
interface SearchResult {
  id: string;
  title: string;
  type: "file" | "folder";
}

/**
 * 递归获取所有 Markdown 文件
 */
async function getAllMarkdownFiles(
  dir: string,
  basePath: string = ""
): Promise<string[]> {
  const entries = fs.readdirSync(dir, { withFileTypes: true });

  const files = await Promise.all(
    entries.map(async (entry) => {
      const fullPath = path.join(dir, entry.name);
      const relativePath = path.join(basePath, entry.name);

      if (entry.isDirectory()) {
        return getAllMarkdownFiles(fullPath, relativePath);
      } else if (entry.name.endsWith(".md")) {
        return [fullPath];
      }

      return [];
    })
  );

  return files.flat();
}

/**
 * 搜索文档
 */
async function searchDocuments(query: string): Promise<SearchResult[]> {
  if (!query || query.trim().length < 1) {
    return [];
  }

  const lowerCaseQuery = query.toLowerCase();
  const results: SearchResult[] = [];

  try {
    // 获取所有 Markdown 文件
    const files = await getAllMarkdownFiles(DOCS_DIRECTORY);

    // 处理每个文件
    for (const file of files) {
      try {
        const content = fs.readFileSync(file, "utf8");
        const { data } = matter(content);

        const id = path.basename(file, ".md");
        const title = data.title || id;

        // 只检查标题是否包含查询词
        const titleMatch = title.toLowerCase().includes(lowerCaseQuery);

        // 如果标题匹配，添加到结果中
        if (titleMatch) {
          results.push({
            id,
            title,
            type: "file",
          });
        }
      } catch (err) {
        console.error(`Error processing file ${file}:`, err);
      }
    }

    // 按标题字母顺序排序
    return results.sort((a, b) => a.title.localeCompare(b.title));
  } catch (error) {
    console.error("Error searching documents:", error);
    return [];
  }
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get("q") || "";

  if (!query || query.trim().length < 1) {
    return NextResponse.json([]);
  }

  try {
    const results = await searchDocuments(query);
    return NextResponse.json(results);
  } catch (error) {
    console.error("Search API error:", error);
    return NextResponse.json(
      { error: "Failed to search documents" },
      { status: 500 }
    );
  }
}
