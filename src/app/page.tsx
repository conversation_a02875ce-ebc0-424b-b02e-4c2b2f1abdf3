import Navigator from "../components/ui/Navbar/Navigator";
import Logo from "../components/ui/Navbar/Logo";
import Container from "../components/ui/Container";
import Hero from "../components/page-components/overview/Hero";
import KeyFeatures from "../components/page-components/overview/KeyFeatures";
import FunctionalHighlights from "../components/page-components/overview/FunctionalHighlights";
import DataSecurity from "../components/page-components/overview/DataSecurity";
import CustomerCases from "../components/page-components/overview/CustomerCases";
import Streamer from "../components/ui/Streamer";
import TrustBadges from "../components/page-components/overview/TrustBadges";
import Footer from "../components/ui/Footer/Footer";
import NavTabs from "../components/ui/FunctionTabs/NavTabs";
import FloatingConsultButton from "../components/ui/FloatingConsultButton";
import { ReactNode } from "react";

// 创建一个包装组件来统一处理标题样式
interface SectionTitleProps {
  children: ReactNode;
}

const SectionTitle = ({ children }: SectionTitleProps) => (
  <span className="text-3xl sm:text-3xl md:text-4xl font-bold">{children}</span>
);

export default function Home() {
  return (
    <div className="min-h-screen bg-background relative">
      {/* 背景渐变 - 移动到最外层容器 */}
      <div className="absolute inset-0 bg-gradient-to-br from-background to-[#F0F0FF] z-0"></div>

      {/* 顶部导航 */}
      <Navigator currentPath="/" />

      {/* 悬浮咨询按钮 */}
      <FloatingConsultButton />

      {/* 内容区域 - 移除顶部内边距 */}
      <div className="relative z-10">
        {/* Hero Section */}
        <Hero
          title={
            <>
              <span className="text-primary font-sans">AI 时代的</span>
              法律生产力平台
            </>
          }
          subtitle="不止是 DeepSeek 对话，更深入法律场景的 AI 能力，让法律人的时间更有价值"
          primaryButtonText="Try Beta"
        />

        {/* Trust Badges Section */}
        <div className="bg-white dark:bg-gray-900 py-6 sm:py-8">
          <TrustBadges />
        </div>

        {/* Nav Tabs Section */}
        <div className="bg-white dark:bg-gray-900 py-4">
          <NavTabs
            items={[
              {
                id: "product",
                label: "Product Platforms",
                targetId: "keyfeatures",
              },
              {
                id: "features",
                label: "Functional Highlights",
                targetId: "functionalhighlights",
              },
              // { id: "cases", label: "Enterprise cooperation cases" }, // 暂时隐藏
              {
                id: "security",
                label: "Service & Security",
                targetId: "datasecurity",
              },
            ]}
            defaultTab="product"
          />
        </div>

        {/* Features Section */}
        <div className="bg-white dark:bg-gray-900">
          <KeyFeatures
            className="py-12 sm:py-16 md:py-16"
            title={<SectionTitle>深度融合法律场景的AI生产力平台</SectionTitle>}
          />
        </div>

        {/* Functional Highlights Section */}
        <div className="bg-white dark:bg-gray-900">
          <FunctionalHighlights
            title={<SectionTitle>源自法律团队的最佳实践</SectionTitle>}
            className="py-12 sm:py-16 md:py-16"
          />
        </div>

        {/* Data Security Section */}
        <div className="bg-white dark:bg-gray-900">
          <DataSecurity className="py-12 sm:py-16 md:py-20" />
        </div>

        {/* Customer Cases Section */}
        <div className="bg-white dark:bg-gray-900">
          <CustomerCases
            title={<SectionTitle>各行业先进企业的信赖之选</SectionTitle>}
            className="py-12 sm:py-16 md:py-20"
          />
        </div>

        {/* Contact Us Section */}
        <div className="bg-white dark:bg-gray-900 py-12 sm:py-16 md:py-20">
          <Streamer />
        </div>

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}
