"use client";

import React, { useState, useEffect } from "react";
import LoginForm from "../../components/LoginForm";
import VerificationForm from "../../components/VerificationForm";
import { useAuthActions } from "@/hooks/useAuthActions";
import { useSearchParams } from "next/navigation";
import FullScreenLoading from "@/components/ui/loading/FullScreenLoading";
import { getSafeRedirectUrl } from "@/utils/auth";

export default function Login() {
  const { isAuthenticated, loading } = useAuthActions();
  const searchParams = useSearchParams();
  const redirect = searchParams.get("redirect");

  const [step, setStep] = useState<"login" | "verification">("login");
  const [contactInfo, setContactInfo] = useState<{
    type: "email" | "phone";
    value: string;
  }>({
    type: "email",
    value: "",
  });
  const [isPageLoading, setIsPageLoading] = useState(true);

  // 页面加载时检查认证状态
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        if (isAuthenticated) {
          // 如果已经认证，处理安全重定向（不再添加 SSO 标识）
          const safeRedirectUrl = getSafeRedirectUrl(redirect);
          window.location.href = safeRedirectUrl;
          return;
        }
      } catch (error) {
        console.error("检查认证状态失败:", error);
      } finally {
        setIsPageLoading(false);
      }
    };

    if (!loading) {
      checkAuthStatus();
    }
  }, [isAuthenticated, loading, redirect]);

  // 处理登录表单成功提交（发送验证码成功）
  const handleLoginSuccess = (data: {
    type: "email" | "phone";
    value: string;
  }) => {
    // 保存联系信息并跳转到验证码页面
    setContactInfo(data);
    setStep("verification");
  };

  // 处理验证成功后的重定向
  const handleVerificationSuccess = () => {
    console.log("验证成功，准备重定向");

    // 验证成功后处理安全重定向（不再添加 SSO 标识）
    const safeRedirectUrl = getSafeRedirectUrl(redirect);
    window.location.href = safeRedirectUrl;
  };

  // 返回到登录页
  const handleBack = () => {
    setStep("login");
  };

  // 如果正在加载状态，显示一个空白的加载界面，避免闪烁
  if (isPageLoading || loading) {
    return <FullScreenLoading spinnerSize={24} />;
  }

  return (
    <div className="h-screen w-screen overflow-hidden relative">
      {/* 背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-primary/5 via-transparent to-transparent"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-purple-500/5 via-transparent to-transparent"></div>
      </div>

      {/* Logo */}
      <div className="absolute top-6 right-8 z-20">
        <img src="/logo/quote-logo.svg" alt="Quote Logo" className="h-[22px]" />
      </div>

      {/* 内容容器 */}
      <div className="relative z-10 flex h-full">
        {/* 左侧 - 功能展示 - 在xl断点以下隐藏，使用固定宽度 */}
        <div className="hidden lg:flex lg:w-2/5 items-center justify-center shrink-0 overflow-hidden">
          <div className="h-full w-full flex items-center justify-center">
            <img
              src="/images/left-view.png"
              alt="Quote AI法律解决方案"
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        {/* 右侧 - 注册/登录表单 - 在md断点以下占满宽度，大屏幕时自动填充剩余空间 */}
        <div className="w-full md:flex-1 flex items-center justify-center bg-white h-full">
          {step === "login" ? (
            <LoginForm onSuccess={handleLoginSuccess} />
          ) : (
            <VerificationForm
              contact={contactInfo.value}
              onBack={handleBack}
              onSuccess={handleVerificationSuccess}
            />
          )}
        </div>
      </div>
    </div>
  );
}
