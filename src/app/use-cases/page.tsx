"use client";

import Navigator from "../../components/ui/Navbar/Navigator";
import Container from "../../components/ui/Container";
import Footer from "../../components/ui/Footer/Footer";
import Streamer from "../../components/ui/Streamer";
import UseCasesHero from "../../components/page-components/use-cases/UseCasesHero";
import UseCasesCustomers from "../../components/page-components/use-cases/UseCasesCustomers";
import CustomerLogos from "../../components/ui/CustomerLogos";
import FloatingConsultButton from "../../components/ui/FloatingConsultButton";
import { useEffect, useState } from "react";
import { useTheme } from "next-themes";

export default function UseCasesPage() {
  // 为CSS变量设置默认值
  const [mounted, setMounted] = useState(false);

  // 等待客户端渲染后设置mounted状态
  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* 顶部导航 - 透明背景 */}
      <div className="absolute top-0 left-0 right-0 z-50">
        <Navigator currentPath="/use-cases" />
      </div>

      {/* 悬浮咨询按钮 */}
      <FloatingConsultButton />

      {/* Hero部分 - 包含背景图片，不使用Container以便撑满整个宽度 */}
      <div className="relative w-full">
        <UseCasesHero />
      </div>

      {/* 行业解决方案部分 */}
      <div className="bg-white dark:bg-gray-900 py-12 sm:py-16 md:py-20">
        <UseCasesCustomers />
      </div>

      {/* 客户Logo部分 */}
      <div className="bg-white dark:bg-gray-900 py-0 sm:py-0">
        <CustomerLogos title="更多行业客户的选择" />
      </div>

      {/* 联系我们部分 */}
      <div className="bg-white dark:bg-gray-900">
        <Streamer
          title="探索更多行业解决方案"
          subtitle="了解Quote如何为您的行业提供定制化AI解决方案，提升工作效率"
          buttonText="Try Beta"
        />
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
}
