"use client";

import Navigator from "../../components/ui/Navbar/Navigator";
import Footer from "../../components/ui/Footer/Footer";
import Streamer from "../../components/ui/Streamer";
import CaseStudyHero from "../../components/page-components/case-study/CaseStudyHero";
import CaseStudyContent from "../../components/page-components/case-study/CaseStudyContent";
import FloatingConsultButton from "../../components/ui/FloatingConsultButton";
import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";

// 案例数据类型
interface CaseStudyData {
  id: string;
  title: string;
  company: string;
  industry: string;
  imageSrc: string;
  logoSrc: string;
  challenges: string[];
  solutions: string[];
  results: { metric: string; value: string }[];
}

// 案例数据映射
const caseStudies: Record<string, CaseStudyData> = {
  "finance-industry": {
    id: "finance-industry",
    title: "金融机构智能合规解决方案",
    company: "中国某大型金融机构",
    industry: "金融行业",
    imageSrc: "/images/law1.jpeg",
    logoSrc: "/images/logo2.png",
    challenges: [
      "日益复杂的监管环境和合规要求",
      "大量法律文件和合规审查需要处理",
      "传统人工审核耗时长且容易出现疏漏",
      "合规风险管理压力大",
    ],
    solutions: [
      "智能合同审核系统，自动识别风险条款",
      "法规变更实时追踪与提醒",
      "文档智能分类与管理平台",
      "合规风险评估报告自动生成",
    ],
    results: [
      { metric: "合规审核效率", value: "提升75%" },
      { metric: "人工审核成本", value: "降低60%" },
      { metric: "风险识别准确率", value: "95%以上" },
      { metric: "合规团队工作效率", value: "提升80%" },
    ],
  },
  "healthcare-industry": {
    id: "healthcare-industry",
    title: "医疗健康行业数据隐私保护方案",
    company: "全国知名医疗机构",
    industry: "医疗健康",
    imageSrc: "/images/law2.png",
    logoSrc: "/images/logo2.png",
    challenges: [
      "患者数据隐私保护要求严格",
      "医疗纠纷案件处理复杂",
      "医疗法规更新频繁",
      "知识产权保护需求高",
    ],
    solutions: [
      "智能隐私保护系统，自动识别敏感信息",
      "医疗纠纷案例智能分析平台",
      "医疗法规实时更新与合规检查",
      "医疗知识产权保护解决方案",
    ],
    results: [
      { metric: "数据隐私合规率", value: "提升至99%" },
      { metric: "医疗纠纷处理时间", value: "缩短65%" },
      { metric: "法规更新响应速度", value: "提升80%" },
      { metric: "知识产权保护效率", value: "提升70%" },
    ],
  },
  "tech-industry": {
    id: "tech-industry",
    title: "科技企业知识产权与数据合规方案",
    company: "领先科技创新企业",
    industry: "科技行业",
    imageSrc: "/images/law3.jpeg",
    logoSrc: "/images/logo2.png",
    challenges: [
      "知识产权保护需求强烈",
      "数据合规要求日益严格",
      "商业合同管理复杂",
      "跨国业务面临多地区法规挑战",
    ],
    solutions: [
      "知识产权智能管理与监控系统",
      "全球数据合规自动化检查平台",
      "商业合同智能化管理系统",
      "多地区法规合规分析工具",
    ],
    results: [
      { metric: "知识产权保护效率", value: "提升85%" },
      { metric: "数据合规风险", value: "降低70%" },
      { metric: "合同审核时间", value: "缩短80%" },
      { metric: "跨国业务合规成本", value: "降低50%" },
    ],
  },
  // 默认案例
  "case-study": {
    id: "case-study",
    title: "企业AI法律解决方案",
    company: "某行业领先企业",
    industry: "企业服务",
    imageSrc: "/images/law1.jpeg",
    logoSrc: "/images/logo2.png",
    challenges: [
      "法律文件处理效率低",
      "合规风险管理复杂",
      "法律咨询成本高",
      "跨部门协作困难",
    ],
    solutions: [
      "AI法律文件智能处理系统",
      "合规风险自动化监控平台",
      "智能法律咨询系统",
      "跨部门法律协作平台",
    ],
    results: [
      { metric: "法律文件处理效率", value: "提升70%" },
      { metric: "合规风险识别率", value: "提升85%" },
      { metric: "法律咨询成本", value: "降低55%" },
      { metric: "跨部门协作效率", value: "提升65%" },
    ],
  },
};

export default function CaseStudyPage() {
  // 获取URL参数
  const searchParams = useSearchParams();
  const caseId = searchParams.get("id") || "case-study";

  // 获取当前案例数据
  const [caseData, setCaseData] = useState<CaseStudyData>(
    caseStudies["case-study"]
  );
  const [mounted, setMounted] = useState(false);

  // 根据URL参数加载对应案例数据
  useEffect(() => {
    setMounted(true);
    const currentCase = caseStudies[caseId] || caseStudies["case-study"];
    setCaseData(currentCase);
  }, [caseId]);

  if (!mounted) {
    return null; // 避免服务端渲染不匹配
  }

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* 顶部导航 - 透明背景 */}
      <div className="absolute top-0 left-0 right-0 z-50">
        <Navigator currentPath="/case-study" />
      </div>

      {/* 悬浮咨询按钮 */}
      <FloatingConsultButton />

      {/* Hero部分 - 包含背景图片，不使用Container以便撑满整个宽度 */}
      <div className="relative w-full">
        <CaseStudyHero
          title={caseData.title}
          company={caseData.company}
          industry={caseData.industry}
          imageSrc={caseData.imageSrc}
          logoSrc={caseData.logoSrc}
        />
      </div>

      {/* 案例详情内容 - 使用新的CaseStudyContent组件 */}
      <CaseStudyContent
        company={caseData.company}
        challenges={caseData.challenges}
        solutions={caseData.solutions}
        results={caseData.results}
      />

      {/* 联系我们部分 */}
      <div className="bg-white dark:bg-gray-900">
        <Streamer
          title="探索更多行业解决方案"
          subtitle="了解Quote如何为您的企业提供定制化AI法律解决方案，提升工作效率"
          buttonText="Try Beta"
        />
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
}
