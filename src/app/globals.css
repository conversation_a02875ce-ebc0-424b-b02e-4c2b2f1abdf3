@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* 移除所有元素的默认焦点样式 */
  *:focus {
    @apply outline-none;
  }

  /* 设置 html 和 body 的背景色，解决 Safari 橡皮筋滚动效果背景色问题 */
  html {
    @apply bg-background;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* 全局滚动条 */
  * {
    scrollbar-width: thin; /* "auto" 或 "thin" */
    scrollbar-color: rgba(127, 132, 142, 0.4) transparent; /* thumb 和 track 颜色 */
  }

  /* 针对 .dark 类的深色模式 */
  .dark * {
    scrollbar-color: rgba(111, 123, 140, 0.3) transparent;
  }
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 10, 10, 10;
    --background-end-rgb: 10, 10, 10;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

/* 波浪动画 */
@keyframes wave-pulse {
  0%,
  100% {
    opacity: 0.2;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

.wave-dot-0-0 {
  animation: wave-pulse 1.2s 0s infinite ease-in-out;
}
.wave-dot-0-1 {
  animation: wave-pulse 1.2s 0.1s infinite ease-in-out;
}
.wave-dot-1-0 {
  animation: wave-pulse 1.2s 0.1s infinite ease-in-out;
}
.wave-dot-0-2 {
  animation: wave-pulse 1.2s 0.2s infinite ease-in-out;
}
.wave-dot-1-1 {
  animation: wave-pulse 1.2s 0.2s infinite ease-in-out;
}
.wave-dot-2-0 {
  animation: wave-pulse 1.2s 0.2s infinite ease-in-out;
}
.wave-dot-1-2 {
  animation: wave-pulse 1.2s 0.3s infinite ease-in-out;
}
.wave-dot-2-1 {
  animation: wave-pulse 1.2s 0.3s infinite ease-in-out;
}
.wave-dot-2-2 {
  animation: wave-pulse 1.2s 0.4s infinite ease-in-out;
}

/* Markdown 样式增强 */
.prose pre {
  @apply bg-gray-100 dark:bg-gray-800 p-4 rounded-md overflow-x-auto;
}

.prose code:not(pre code) {
  @apply bg-gray-100 dark:bg-gray-800 px-1.5 py-0.5 rounded text-sm;
}

.prose a {
  @apply text-blue-600 dark:text-blue-400 hover:underline;
}

.prose img {
  @apply mx-auto rounded-md shadow-md;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4 {
  @apply font-bold scroll-mt-20;
}

.prose h2 {
  @apply border-b border-gray-200 dark:border-gray-700 pb-2 mt-8 mb-4;
}

.prose blockquote {
  @apply border-l-4 border-gray-300 dark:border-gray-700 pl-4 italic;
}

.prose table {
  @apply border-collapse border border-gray-300 dark:border-gray-700;
}

.prose th,
.prose td {
  @apply border border-gray-300 dark:border-gray-700 p-2;
}

.prose thead {
  @apply bg-gray-100 dark:bg-gray-800;
}

@layer components {
  /* 电话输入组件样式 */
  .PhoneInput {
    @apply flex items-center w-full;
  }

  .PhoneInputInput {
    @apply flex-1 min-w-0;
  }

  .PhoneInput--focus {
    @apply outline-none;
  }

  .PhoneInputCountry {
    @apply relative self-stretch flex items-center;
  }

  /* 自定义滚动容器类 */
  .custom-scrollbar {
    @apply overflow-auto;
    /* 确保在所有浏览器中使用上面定义的滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.4) transparent;
  }

  .dark .custom-scrollbar {
    scrollbar-color: rgba(111, 123, 140, 0.3) transparent;
  }

  /* 无滚动条样式 */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  /* 垂直文本样式 */
  .writing-vertical-rl {
    writing-mode: vertical-rl;
    text-orientation: mixed;
  }
}

/* 去除按钮焦点状态边框 */
button:focus,
[role="button"]:focus,
a:focus,
input:focus,
select:focus,
textarea:focus,
[tabindex]:focus {
  outline: none !important;
  box-shadow: none !important;
}
