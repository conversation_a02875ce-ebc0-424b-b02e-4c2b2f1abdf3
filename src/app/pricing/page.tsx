"use client";

import Navigator from "../../components/ui/Navbar/Navigator";
import Container from "../../components/ui/Container";
import Footer from "../../components/ui/Footer/Footer";
import PricingPlans from "../../components/page-components/pricing/PricingPlans";
import ComparisonTable from "../../components/page-components/pricing/ComparisonTable";
import CustomerLogos from "../../components/ui/CustomerLogos";
import Streamer from "../../components/ui/Streamer";
import FloatingPricingBar from "../../components/ui/FloatingPricingBar";
import FloatingConsultButton from "../../components/ui/FloatingConsultButton";
import { useEffect, useState } from "react";
import { useTheme } from "next-themes";

export default function PricingPage() {
  // 为CSS变量设置默认值
  const [mounted, setMounted] = useState(false);
  const { theme } = useTheme();

  // 等待客户端渲染后设置mounted状态
  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className="min-h-screen bg-background relative">
      {/* 背景渐变 */}
      <div className="absolute inset-0 z-0">
        {/* 上半部分渐变背景 */}
        <div className="absolute top-0 left-0 right-0 h-[500px] bg-pageHeader-gradientStart"></div>
        {/* 渐变过渡区域 */}
        <div className="absolute top-[300px] left-0 right-0 h-[200px] bg-gradient-to-b from-pageHeader-gradientStart to-pageHeader-gradientEnd"></div>
        {/* 下半部分背景 */}
        <div className="absolute top-[500px] left-0 right-0 bottom-0 bg-pageHeader-gradientEnd"></div>
      </div>

      {/* 顶部导航 */}
      <Navigator currentPath="/pricing" />

      {/* 悬浮价格导航栏 */}
      <FloatingPricingBar />

      {/* 悬浮咨询按钮 */}
      <FloatingConsultButton />

      {/* 内容区域 */}
      <div className="relative z-10">
        {/* 价格部分 */}
        <div className="pt-36">
          <Container>
            <div className="text-center mb-6">
              <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6">
                为您的组织选择最佳解决方案
              </h1>
            </div>
            <PricingPlans />
          </Container>
        </div>

        {/* 版本对比表格部分 */}
        <div className="bg-white dark:bg-gray-900 mt-12">
          <Container>
            <ComparisonTable />
          </Container>
        </div>

        {/* 客户Logo品牌墙部分 */}
        <div className="bg-white dark:bg-gray-900">
          <CustomerLogos />
        </div>

        {/* FAQ 部分（可选，后续可添加） */}
        {/* <div className="bg-white dark:bg-gray-900">
          <FAQSection className="py-12 sm:py-16 md:py-20" />
        </div> */}

        {/* 联系我们部分 */}
        <div className="bg-white dark:bg-gray-900">
          <Streamer
            title="Quote将为您提供全力支持"
            subtitle="选择最适合您的方案，立即联系我们获取专业咨询和支持"
            buttonText="Try Beta"
          />
        </div>

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}
