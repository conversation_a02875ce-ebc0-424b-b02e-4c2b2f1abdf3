"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import Navigator from "../../components/ui/Navbar/Navigator";
import { useAuthActions } from "@/hooks/useAuthActions";

export default function ProfilePage() {
  const { user, isAuthenticated, loading, handleLogout } = useAuthActions();
  const router = useRouter();

  // 重定向未登录用户
  useEffect(() => {
    if (!loading && !isAuthenticated) {
      router.push("/login");
    }
  }, [loading, isAuthenticated, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        加载中...
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // 重定向已处理，返回null避免闪烁
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <Navigator />

      <div className="pt-16">
        <div className="container mx-auto px-4 py-10">
          <div className="max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
              用户资料
            </h1>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="flex flex-col items-center">
                <div className="w-32 h-32 bg-primary text-white flex items-center justify-center rounded-full text-4xl font-medium mb-4">
                  {user?.name?.charAt(0).toUpperCase() || "U"}
                </div>
                <button
                  className="mt-4 px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-lg text-sm transition-colors"
                  onClick={handleLogout}
                >
                  退出登录
                </button>
              </div>

              <div className="md:col-span-2">
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                      用户ID
                    </label>
                    <div className="text-lg text-gray-900 dark:text-white">
                      {user?.id || "未知"}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                      用户名
                    </label>
                    <div className="text-lg text-gray-900 dark:text-white">
                      {user?.name || "未设置"}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                      邮箱
                    </label>
                    <div className="text-lg text-gray-900 dark:text-white">
                      {user?.email || "未设置"}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                      手机号
                    </label>
                    <div className="text-lg text-gray-900 dark:text-white">
                      {(user as any)?.phone || "未设置"}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
