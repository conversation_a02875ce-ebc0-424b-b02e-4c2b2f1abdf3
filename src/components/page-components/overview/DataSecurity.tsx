"use client";

import * as React from "react";
import Container from "../../ui/Container";
import { motion } from "framer-motion";
import Image from "next/image";

// 组件属性接口
interface DataSecurityProps {
  className?: string;
  id?: string;
  title?: React.ReactNode;
}

// 毛玻璃卡片数据
const glassCards = [
  { id: 1, delay: 0.1 },
  { id: 2, delay: 0.2 },
  { id: 3, delay: 0.3 },
  { id: 4, delay: 0.4 },
  { id: 5, delay: 0.5 },
  { id: 6, delay: 0.6 },
];

export default function DataSecurity({
  className = "",
  id = "datasecurity",
  title,
}: DataSecurityProps) {
  return (
    <section className={`bg-white dark:bg-gray-900 ${className}`} id={id}>
      {/* 使用更大的容器 */}
      <div className="max-w-[1400px] mx-auto px-4 md:px-6 lg:px-8">
        {/* 标题部分 - 仅当提供了 title 属性时显示 */}
        {title && (
          <div className="flex justify-center mb-6 sm:mb-8 md:mb-12">
            <h2 className="font-bold font-sans text-center text-gray-800 dark:text-white max-w-[800px] leading-tight">
              {title}
            </h2>
          </div>
        )}

        {/* 渐变容器 - 使用自适应高度 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="w-full"
        >
          <div className="relative bg-gradient-to-b from-card-securityGradientFrom to-card-securityGradientTo dark:from-card-securityGradientFrom dark:to-card-securityGradientTo rounded-lg overflow-hidden">
            {/* 使用flex布局代替固定高度，确保内容能撑开容器 */}
            <div className="relative w-full flex flex-col md:min-h-[400px]">
              {/* 小屏幕下的地球图片 - 缩小并居中显示 */}
              <div className="absolute right-0 bottom-0 w-full h-full z-0 md:hidden block opacity-20">
                <div className="relative w-full h-full">
                  <Image
                    src="/images/safety-card/earth.png"
                    alt="Global Security"
                    fill
                    className="object-contain"
                  />
                </div>
              </div>

              {/* 大屏幕下的地球图片 - 放大并向下移动 */}
              <div className="absolute right-[-5%] bottom-[-55%] h-[170%] aspect-square z-0 md:block hidden">
                <div className="relative w-full h-full">
                  <Image
                    src="/images/safety-card/earth.png"
                    alt="Global Security"
                    fill
                    className="object-contain"
                  />
                </div>
              </div>

              {/* 内容区域 - 文本 */}
              <div className="relative p-5 pb-3 md:p-8 lg:p-12 xl:p-16 flex flex-col justify-start items-start z-10">
                <div className="max-w-lg md:max-w-[45%] lg:max-w-[42%]">
                  <h2 className="text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-800 dark:text-white leading-tight mb-2 md:mb-4">
                    <span className="block mb-1 md:mb-2 md:whitespace-nowrap">
                      符合国内和国际高标准合规性要求
                    </span>
                    全力保护企业组织的数据安全
                  </h2>
                  <p className="text-xs md:text-sm lg:text-base font-medium text-gray-500 dark:text-gray-400 mb-3 md:mb-8">
                    提供数据安全、隐私保护以及安全合规，保障企业组织的数字化安全
                  </p>

                  {/* 两个卡片 - 始终左右排布 */}
                  <div className="flex flex-row gap-3 md:gap-4 lg:gap-6 mb-4 md:mb-0">
                    {/* 卡片1 */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-3 md:p-6 md:py-7 shadow-sm flex-1 flex flex-col items-center text-center">
                      <div className="flex flex-col items-center">
                        <div className="mb-1.5 md:mb-3 flex-shrink-0">
                          <Image
                            src="/images/safety-card/dunpai.svg"
                            alt="Shield Icon"
                            width={28}
                            height={32}
                            className="w-6 h-7 md:w-9 md:h-10"
                          />
                        </div>
                        <div>
                          <p className="text-[10px] md:text-xs font-medium text-gray-500 dark:text-gray-400 mb-0.5 md:mb-1">
                            全面保障客户数据安全与隐私
                          </p>
                          <h3 className="font-bold text-gray-800 dark:text-white text-sm md:text-base lg:text-lg">
                            全周期数据安全保障
                          </h3>
                        </div>
                      </div>
                    </div>

                    {/* 卡片2 */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-3 md:p-6 md:py-7 shadow-sm flex-1 flex flex-col items-center text-center">
                      <div className="flex flex-col items-center">
                        <div className="mb-1.5 md:mb-3 flex-shrink-0">
                          <Image
                            src="/images/safety-card/fenceng.svg"
                            alt="Layer Icon"
                            width={28}
                            height={28}
                            className="w-6 h-6 md:w-9 md:h-9"
                          />
                        </div>
                        <div>
                          <p className="text-[10px] md:text-xs font-medium text-gray-500 dark:text-gray-400 mb-0.5 md:mb-1">
                            确保组织核心资产可控可用
                          </p>
                          <h3 className="font-bold text-gray-800 dark:text-white text-sm md:text-base lg:text-lg">
                            分层保护组织数据
                          </h3>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 毛玻璃卡片容器 - 大屏幕时位于右侧，使用固定定位避免挤压 */}
              <div className="absolute top-[40%] right-0 w-[50%] z-10 hidden md:block lg:right-[3%]">
                {/* 使用grid布局但固定卡片尺寸，不使用gap属性 */}
                <div className="grid grid-cols-3 gap-x-1 mb-4">
                  {glassCards.slice(0, 3).map((card) => (
                    <motion.div
                      key={card.id}
                      initial={{ opacity: 0, y: 10 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: card.delay }}
                      viewport={{ once: true }}
                      className="backdrop-blur-md bg-white/60 dark:bg-gray-800/20 rounded-lg py-10 border border-white/30 dark:border-gray-700/30 w-[90%] mx-auto aspect-[7/4]"
                    />
                  ))}
                </div>
                <div className="grid grid-cols-3 gap-x-1">
                  {glassCards.slice(3, 6).map((card) => (
                    <motion.div
                      key={card.id}
                      initial={{ opacity: 0, y: 10 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: card.delay }}
                      viewport={{ once: true }}
                      className="backdrop-blur-md bg-white/60 dark:bg-gray-800/20 rounded-lg py-10 border border-white/30 dark:border-gray-700/30 w-[90%] mx-auto aspect-[7/4]"
                    />
                  ))}
                </div>
              </div>

              {/* 底部间距占位符，确保内容有足够的底部空间 */}
              <div className="hidden md:block md:h-24"></div>
            </div>

            {/* 小屏幕下的毛玻璃卡片容器 - 位于内容区域下方 */}
            <div className="md:hidden block pt-3 pb-4 px-4">
              {/* 小屏幕下改为2列布局，使用固定尺寸 */}
              <div className="grid grid-cols-2 gap-x-2 gap-y-3 mb-3">
                {glassCards.slice(0, 2).map((card) => (
                  <motion.div
                    key={card.id}
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: card.delay }}
                    viewport={{ once: true }}
                    className="backdrop-blur-md bg-white/60 dark:bg-gray-800/20 rounded-lg py-4 border border-white/30 dark:border-gray-700/30 w-[90%] mx-auto aspect-[3/2]"
                  />
                ))}
              </div>
              <div className="grid grid-cols-2 gap-x-2 gap-y-3 mb-3">
                {glassCards.slice(2, 4).map((card) => (
                  <motion.div
                    key={card.id}
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: card.delay }}
                    viewport={{ once: true }}
                    className="backdrop-blur-md bg-white/60 dark:bg-gray-800/20 rounded-lg py-4 border border-white/30 dark:border-gray-700/30 w-[90%] mx-auto aspect-[3/2]"
                  />
                ))}
              </div>
              <div className="grid grid-cols-2 gap-x-2 gap-y-3">
                {glassCards.slice(4, 6).map((card) => (
                  <motion.div
                    key={card.id}
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: card.delay }}
                    viewport={{ once: true }}
                    className="backdrop-blur-md bg-white/60 dark:bg-gray-800/20 rounded-lg py-4 border border-white/30 dark:border-gray-700/30 w-[90%] mx-auto aspect-[3/2]"
                  />
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
