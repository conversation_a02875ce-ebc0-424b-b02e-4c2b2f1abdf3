"use client";

import * as React from "react";
import { useState } from "react";
import Container from "../../ui/Container";
import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";
import Image from "next/image";
import QRCodeModal from "../../ui/QRCodeModal";

// 定义功能亮点项数据结构
interface HighlightItem {
  title: string;
  description: string;
  statistic?: string; // 添加统计数据字段
  icon?: React.ReactNode;
  gridArea?: string;
  className?: string;
}

// 组件属性接口
interface FunctionalHighlightsProps {
  title: React.ReactNode;
  subtitle?: string;
  className?: string;
  id?: string;
}

// 功能亮点数据数组
const highlightItems: HighlightItem[] = [
  {
    title: "极速提效，释放律师专业价值",
    description: "为高价值法律工作每周释放12.5小时",
    statistic: "12+",
    gridArea: "knowledge",
    className: "flex-1",
  },
  {
    title: "从「AI 怀疑者」到「效率倍增者」",
    description: "初始阶段律师对 AI的信任率仅34%，使用后这一比例升至91%",
    statistic: "91%",
    gridArea: "contract",
    className: "flex-1",
  },
  {
    title: "预约演示，\n获取企业合作方案",
    description: "想要进一步了解？",
    gridArea: "document",
    className: "col-span-3 row-span-2",
  },
  {
    title: "让 AI 成为能|「听懂指令、分解任务、自动执行」|的超级助理",
    description: "律师使用产品后，日常平均文书撰写时间减少76%",
    statistic: "减少76%",
    gridArea: "analysis",
    className: "col-span-9",
  },
];

// 亮点卡片组件
const HighlightCard = ({
  title,
  description,
  statistic,
  className,
}: HighlightItem) => {
  // 检查是否是预约演示卡片（通过标题中是否包含换行符来判断）
  const isDemoCard = title.includes("\n");
  // 添加二维码浮层状态
  const [isQRCodeVisible, setIsQRCodeVisible] = useState(false);

  // 处理按钮点击
  const handleButtonClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsQRCodeVisible(true);
  };

  // 关闭二维码浮层
  const handleCloseQRCode = () => {
    setIsQRCodeVisible(false);
  };

  return (
    <>
      {/* 使用通用二维码浮层组件 */}
      <QRCodeModal isVisible={isQRCodeVisible} onClose={handleCloseQRCode} />

      <div
        className={`rounded-xl h-full bg-gradient-to-b from-card-gradientFrom to-card-gradientTo dark:from-card-gradientFrom dark:to-card-gradientTo ${
          className || ""
        }`}
      >
        <div
          className={`p-7 ${
            isDemoCard ? "pb-0" : "pb-5"
          } flex flex-col h-full justify-between space-y-4`}
        >
          {/* 预约演示卡片的布局：副标题在上，主标题在中间，按钮在标题下方 */}
          {isDemoCard ? (
            <>
              <div className="flex-grow">
                <p className="text-gray-400 dark:text-gray-300 text-sm leading-relaxed font-sans mb-2">
                  {description}
                </p>
                <h3 className="text-xl font-bold font-sans text-gray-800 dark:text-white mb-4">
                  <span className="text-primary">{title.split("\n")[0]}</span>
                  <br />
                  <span>{title.split("\n")[1]}</span>
                </h3>
                <button
                  className="px-6 py-2.5 bg-primary text-white rounded-lg hover:bg-opacity-90 transition font-bold flex items-center gap-2 text-sm cursor-pointer z-10 relative overflow-hidden before:absolute before:w-[0.4rem] before:h-[20rem] before:top-0 before:translate-x-[-8rem] hover:before:translate-x-[20rem] before:duration-[0.8s] before:-skew-x-[10deg] before:transition-all before:bg-white before:blur-[8px] before:opacity-70 shadow-lg shadow-primary/30 hover:shadow-xl hover:shadow-primary/40"
                  onClick={handleButtonClick}
                >
                  Contact Us
                  <ArrowRight className="w-4 h-4" />
                </button>
              </div>
              <div className="mt-auto relative">
                <div className="min-h-[40px] flex items-end">
                  {/* 底部空间保留，保持卡片高度一致 */}
                </div>
                <div className="absolute bottom-0 right-0 transform translate-x-1 pointer-events-none">
                  <div className="-mb-[1px]">
                    {" "}
                    {/* 微调以实现精准对齐 */}
                    <Image
                      src="/images/madou.png"
                      alt="客服形象"
                      width={280}
                      height={280}
                      className="object-contain md:block hidden"
                      quality={100} // 确保图片质量不被压缩
                      priority // 优先加载图片
                    />
                    {/* 移动端使用较小的图片 */}
                    <Image
                      src="/images/madou.png"
                      alt="客服形象"
                      width={150}
                      height={150}
                      className="object-contain md:hidden block"
                      quality={100}
                      priority
                    />
                  </div>
                </div>
              </div>
            </>
          ) : (
            /* 其他卡片的常规布局：主标题在上，副标题在下 */
            <>
              <div className="flex-grow flex flex-col">
                <h3 className="text-xl font-bold font-sans text-gray-800 dark:text-white mb-auto">
                  {title.includes("|") ? (
                    <>
                      {title.split("|").map((part, index) => {
                        return index === 1 ? (
                          <span key={index} className="text-primary">
                            {part}
                          </span>
                        ) : (
                          <span key={index}>{part}</span>
                        );
                      })}
                    </>
                  ) : (
                    title
                  )}
                </h3>
                {statistic && (
                  <div className="text-5xl font-bold text-primary mt-auto mb-2">
                    {statistic}
                  </div>
                )}
              </div>
              <div>
                <div className="min-h-[40px]">
                  <p className="text-gray-400 dark:text-gray-300 text-sm leading-relaxed font-sans line-clamp-2 w-full">
                    {description}
                  </p>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default function FunctionalHighlights({
  title = "",
  subtitle = "",
  className = "",
  id = "functionalhighlights",
}: FunctionalHighlightsProps) {
  return (
    <section className={`bg-white dark:bg-gray-900 ${className}`} id={id}>
      <Container>
        {/* 标题部分 */}
        <div className="flex justify-center mb-6 sm:mb-8 md:mb-12">
          <h2 className="font-bold font-sans text-center text-gray-800 dark:text-white max-w-[800px] leading-tight">
            {title}
          </h2>
        </div>

        {/* 自定义布局 - 使用flex布局 */}
        <div className="hidden md:flex flex-wrap gap-4">
          {/* 左侧 - 法律文书生成卡片 */}
          <div className="flex-shrink-0 w-full max-w-[380px] lg:w-[380px] flex-grow-0 flex">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="h-full w-full"
            >
              <div className="h-full">
                <HighlightCard
                  title={highlightItems[2].title}
                  description={highlightItems[2].description}
                  className="h-full overflow-hidden"
                />
              </div>
            </motion.div>
          </div>

          {/* 右侧 - 其他卡片容器 */}
          <div className="flex-1 flex flex-col gap-4 min-w-0">
            {/* 上方卡片容器 - 法律知识库和智能合同审核 */}
            <div className="flex gap-4 h-[calc(50%-8px)] w-full">
              {/* 法律知识库 */}
              <div className="w-1/2 min-w-0">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  viewport={{ once: true }}
                  className="h-full"
                >
                  <HighlightCard
                    title={highlightItems[0].title}
                    description={highlightItems[0].description}
                    statistic={highlightItems[0].statistic}
                  />
                </motion.div>
              </div>

              {/* 智能合同审核 */}
              <div className="w-1/2 min-w-0">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  viewport={{ once: true }}
                  className="h-full"
                >
                  <HighlightCard
                    title={highlightItems[1].title}
                    description={highlightItems[1].description}
                    statistic={highlightItems[1].statistic}
                  />
                </motion.div>
              </div>
            </div>

            {/* 下方卡片 - 案例分析系统 */}
            <div className="h-[calc(50%-8px)]">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                viewport={{ once: true }}
                className="h-full"
              >
                <HighlightCard
                  title={highlightItems[3].title}
                  description={highlightItems[3].description}
                  statistic={highlightItems[3].statistic}
                />
              </motion.div>
            </div>
          </div>
        </div>

        {/* 移动端布局 - 单列布局 */}
        <div className="grid grid-cols-1 gap-3 sm:gap-4 md:hidden">
          {highlightItems.map((item, index) => (
            <motion.div
              key={`highlight-mobile-${index}`}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className={item.title.includes("\n") ? "min-h-[300px]" : ""}
            >
              <HighlightCard
                title={item.title}
                description={item.description}
                statistic={item.statistic}
                className={item.title.includes("\n") ? "h-full" : ""}
              />
            </motion.div>
          ))}
        </div>
      </Container>
    </section>
  );
}
