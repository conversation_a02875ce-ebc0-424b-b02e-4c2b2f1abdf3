import React from "react";
import Container from "../../ui/Container";

interface TrustBadgesProps {
  title?: string;
  className?: string;
}

export default function TrustBadges({
  title = "Trusted by leading law firms",
  className = "",
}: TrustBadgesProps) {
  return (
    <div className="relative bg-white dark:bg-gray-900 py-8">
      <Container>
        <div
          className={`flex flex-wrap justify-center gap-x-12 gap-y-6 opacity-70 ${className}`}
        >
          <div className="w-full flex flex-wrap justify-center items-center gap-8">
            {Array.from({ length: 8 }).map((_, i) => (
              <div
                key={i}
                className="h-8 w-24 bg-gray-300 dark:bg-gray-700 rounded opacity-50"
              ></div>
            ))}
          </div>
        </div>
      </Container>
    </div>
  );
}
