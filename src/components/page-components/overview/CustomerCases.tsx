"use client";

import * as React from "react";
import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";
import Container from "../../ui/Container";
import { ArrowRight } from "lucide-react";

// 案例卡片数据接口
interface CaseItem {
  id: string;
  name: string;
  imageSrc: string;
  description?: string;
  industry?: string;
  slug?: string; // 添加slug字段用于构建URL
}

// 组件属性接口
interface CustomerCasesProps {
  title?: React.ReactNode;
  subtitle?: string;
  cases?: CaseItem[];
  className?: string;
  id?: string;
  onViewMoreClick?: () => void;
}

// 默认案例数据
const defaultCases: CaseItem[] = [
  {
    id: "case1",
    name: "某大型律师事务所",
    imageSrc: "/images/law1.jpeg",
    description: "AI辅助法律研究与文档起草",
    industry: "法律",
    slug: "law-firm", // 添加slug
  },
  {
    id: "case2",
    name: "某金融科技公司",
    imageSrc: "/images/law2.png",
    description: "合规文件自动化审核",
    industry: "金融科技",
    slug: "fintech-company", // 添加slug
  },
  {
    id: "case3",
    name: "某跨国企业法务部",
    imageSrc: "/images/law3.jpeg",
    description: "多语言合同智能分析",
    industry: "跨国企业",
    slug: "multinational-corporation", // 添加slug
  },
];

// 案例卡片组件
const CaseCard = ({
  id,
  name,
  imageSrc,
  description,
  industry,
  slug = "case-study",
}: CaseItem) => {
  const [isHovered, setIsHovered] = React.useState(false);

  return (
    <Link
      href={`/case-study?id=${slug}`}
      className="block h-full"
      target="_blank"
      rel="noopener noreferrer"
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        viewport={{ once: true }}
        className={`rounded-lg overflow-hidden transition-all duration-300 hover:scale-[1.02] h-full cursor-pointer ${
          isHovered
            ? "shadow-xl shadow-gray-300/50 dark:shadow-gray-900/50"
            : "shadow-md shadow-gray-200/50 dark:shadow-gray-900/30"
        }`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="flex flex-col h-full">
          {/* 图片容器 - 上半部分，保持高度不变 */}
          <div className="relative w-full h-[240px] overflow-hidden">
            <div
              className="w-full h-full transition-transform duration-500"
              style={{
                transform: isHovered ? "scale(1.1)" : "scale(1)",
              }}
            >
              <Image
                src={imageSrc}
                alt={name}
                fill
                className="object-cover"
                quality={100}
              />
            </div>
          </div>

          {/* 内容区 - 下半部分，使用默认背景色 */}
          <div className="bg-background dark:bg-gray-900 p-6 flex flex-col justify-center h-[140px]">
            {/* 行业标签 - 使用tailwind.config.js中定义的CardTags颜色 */}
            {industry && (
              <div className="mb-3">
                <span className="px-3 py-1 text-xs font-medium bg-CardTags text-CardTags-text rounded-sm">
                  {industry}
                </span>
              </div>
            )}

            <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-2">
              {name}
            </h3>
            {description && (
              <p className="text-sm text-gray-500 dark:text-gray-300">
                {description}
              </p>
            )}
          </div>
        </div>
      </motion.div>
    </Link>
  );
};

export default function CustomerCases({
  title = "各行业先进企业的信赖之选",
  subtitle,
  cases = defaultCases,
  className = "",
  id = "customercases",
  onViewMoreClick,
}: CustomerCasesProps) {
  const handleViewMoreClick = () => {
    if (onViewMoreClick) {
      onViewMoreClick();
    } else {
      // 默认跳转到客户案例页面
      window.location.href = "/use-cases";
    }
  };

  return (
    <section className={`bg-white dark:bg-gray-900 ${className}`} id={id}>
      {/* 使用Container组件包裹，与DataSecurity.tsx保持一致 */}
      <Container>
        {/* 标题部分 */}
        <div className="text-center mb-6 sm:mb-8 md:mb-12">
          <h2 className="font-bold text-gray-800 dark:text-white mb-4">
            {title}
          </h2>
          {subtitle && (
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              {subtitle}
            </p>
          )}
        </div>

        {/* 案例卡片容器 - 改进响应式布局 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 md:gap-6">
          {cases.map((caseItem) => (
            <div key={caseItem.id} className="w-full">
              <CaseCard
                id={caseItem.id}
                name={caseItem.name}
                imageSrc={caseItem.imageSrc}
                description={caseItem.description}
                industry={caseItem.industry}
                slug={caseItem.slug}
              />
            </div>
          ))}
        </div>

        {/* 查看更多按钮 */}
        <div className="flex justify-center mt-8 sm:mt-10 md:mt-12">
          <button
            onClick={handleViewMoreClick}
            className="px-6 py-3 border border-gray-800 dark:border-white rounded-lg text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-300 flex items-center gap-2 font-medium"
          >
            查看更多企业合作案例
            <ArrowRight className="w-4 h-4" />
          </button>
        </div>
      </Container>
    </section>
  );
}
