"use client";

import React, { useState, useId, useRef, useEffect, useCallback } from "react";
import Container from "../../ui/Container";
import FloatingContainer from "../../ui/FloatingContainer";
import Image from "next/image";
import { ArrowRight, X } from "lucide-react";
import {
  motion,
  AnimatePresence,
  useScroll,
  useTransform,
} from "framer-motion";
import ArticleContent from "./articles/KeyFeatureContent";

// 定义特性项数据结构
interface FeatureItem {
  title: string;
  image: string;
  alt: string;
  articleId: string; // 修改为articleId，用于加载对应的文章内容
}

// 修改主组件的接口，添加标题相关属性
interface KeyFeaturesProps {
  title: React.ReactNode;
  subtitle?: string;
  className?: string;
  id?: string; // 添加可选的唯一ID属性
}

// 卡片特性数据数组，方便统一管理
const featureItems: FeatureItem[] = [
  {
    title: "Knowledge based deep research",
    image: "/images/cards/card1.png",
    alt: "Deep Research",
    articleId: "knowledge-research",
  },
  {
    title: "Background contract audit system",
    image: "/images/cards/card2.png",
    alt: "Contract Audit",
    articleId: "contract-audit",
  },
  {
    title: "Legal document agent",
    image: "/images/cards/card3.png",
    alt: "Document Agent",
    articleId: "legal-document",
  },
];

// 特性卡片组件 - 使用React.memo防止不必要的重渲染
const FeatureCard = React.memo(
  ({
    title,
    image,
    alt,
    articleId,
    instanceId,
  }: FeatureItem & { instanceId: string }) => {
    // 使用useState来管理卡片打开状态和悬停状态
    const [isOpen, setIsOpen] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    const [isCloseHovered, setIsCloseHovered] = useState(false);

    // 生成组件内唯一ID - 使用React的useId和传入的instanceId确保全局唯一
    const componentId = useId();
    const uniqueCardId = `${instanceId}-${componentId}`;

    // 根据卡片类型设置不同的背景色
    let cardBgClass = "bg-card hover:bg-card-hover";
    if (articleId === "knowledge-research") {
      cardBgClass = "bg-card-card1 hover:bg-card-card1/90";
    } else if (articleId === "contract-audit") {
      cardBgClass = "bg-card-card2 hover:bg-card-card2/90";
    } else if (articleId === "legal-document") {
      cardBgClass = "bg-card-card3 hover:bg-card-card3/90";
    }

    // 引用容器宽度
    const [containerWidth, setContainerWidth] = useState(0);
    const containerRef = useRef<HTMLDivElement>(null);

    // 引用标题元素，用于计算距离顶部的距离
    const titleRef = useRef<HTMLDivElement>(null);
    const contentRef = useRef<HTMLDivElement>(null);

    // 状态用于存储标题距离顶部的距离
    const [titleTopDistance, setTitleTopDistance] = useState(1); // 默认为1，确保初始状态图像完全显示

    // 添加一个状态来控制动画是否应该生效
    const [animationEnabled, setAnimationEnabled] = useState(false);

    // 使用useState和useEffect安全地检测设备类型
    const [isMobile, setIsMobile] = useState(false);
    const [isTablet, setIsTablet] = useState(false);

    // 检测设备类型
    useEffect(() => {
      const checkDeviceType = () => {
        setIsMobile(window.innerWidth < 640);
        setIsTablet(window.innerWidth >= 640 && window.innerWidth < 768);
      };

      checkDeviceType();
      window.addEventListener("resize", checkDeviceType);

      return () => {
        window.removeEventListener("resize", checkDeviceType);
      };
    }, []);

    // 监听容器宽度变化
    useEffect(() => {
      const updateWidth = () => {
        if (containerRef.current) {
          const container = document.querySelector(".container") as HTMLElement;
          if (container) {
            setContainerWidth(container.offsetWidth);
          }
        }
      };

      updateWidth();
      window.addEventListener("resize", updateWidth);
      return () => window.removeEventListener("resize", updateWidth);
    }, []);

    // 监听标题距离顶部的距离
    useEffect(() => {
      if (!isOpen) return;

      // 移除延迟启用动画的逻辑，直接启用
      setAnimationEnabled(true);

      const handleScroll = () => {
        if (titleRef.current && contentRef.current) {
          const titleRect = titleRef.current.getBoundingClientRect();
          const contentRect = contentRef.current.getBoundingClientRect();

          // 计算标题相对于内容顶部的距离
          const distanceFromTop = titleRect.top - contentRect.top;

          // 计算最大距离（初始状态下标题到内容顶部的距离）
          // 根据屏幕宽度动态调整最大距离

          // 移动端使用更小的最大距离，以便更快完成透明度变化
          let maxDistance;
          if (isMobile) {
            maxDistance = Math.min(200, contentRect.height * 0.2);
          } else if (isTablet) {
            maxDistance = Math.min(300, contentRect.height * 0.3);
          } else {
            maxDistance = Math.min(500, contentRect.height * 0.5);
          }

          // 计算距离比例（0到1之间）
          const distanceRatio = Math.max(
            0,
            Math.min(1, distanceFromTop / maxDistance)
          );

          setTitleTopDistance(distanceRatio);
        }
      };

      const contentElement = contentRef.current;
      if (contentElement) {
        contentElement.addEventListener("scroll", handleScroll);
        // 初始化时也调用一次
        handleScroll();

        return () => {
          contentElement.removeEventListener("scroll", handleScroll);
        };
      }
    }, [isOpen, isMobile, isTablet]);

    // 当浮层关闭时，重置状态
    useEffect(() => {
      if (!isOpen) {
        setAnimationEnabled(false);
        setTitleTopDistance(1);
      }
    }, [isOpen]);

    // 控制背景滚动
    useEffect(() => {
      if (isOpen) {
        // 禁用背景滚动
        document.body.style.overflow = "hidden";
      } else {
        // 恢复背景滚动
        document.body.style.overflow = "auto";
      }

      // 组件卸载时恢复滚动
      return () => {
        document.body.style.overflow = "auto";
      };
    }, [isOpen]);

    // 基于标题距离计算的图像缩放和透明度
    // 根据屏幕尺寸调整缩放和透明度计算
    const imageScale =
      titleTopDistance * (isMobile ? 0.15 : 0.2) + (isMobile ? 0.9 : 0.85); // 移动端缩放范围更小
    // 使用指数函数让透明度变化更快，移动端使用更高的指数
    const imageOpacity = Math.pow(titleTopDistance, isMobile ? 3 : 4); // 从1到0，变化更快

    // 计算标题背景透明度 - 与图像透明度相反
    // 移动设备上标题背景透明度变化更快
    const titleBgOpacity = isMobile
      ? 1 - titleTopDistance * 15 // 移动端
      : 1 - titleTopDistance * 20; // 桌面端

    return (
      <div className="relative" ref={containerRef}>
        {/* 基础卡片 */}
        <motion.div
          className={`relative group overflow-hidden rounded-xl aspect-[0.85] w-full md:w-[360px] ${cardBgClass} transition-colors duration-300 cursor-pointer`}
          onClick={() => setIsOpen(true)}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          layoutId={`card-container-${uniqueCardId}`}
        >
          {/* 图片背景 */}
          <motion.div
            className="absolute inset-0 w-full h-full"
            layoutId={`card-image-container-${uniqueCardId}`}
          >
            <motion.div
              className="w-full h-full"
              animate={{
                scale: isHovered ? 1.05 : 1,
              }}
              transition={{ duration: 0.3 }}
            >
              <Image
                src={image}
                alt={alt}
                fill
                className="object-cover"
                quality={100}
              />
            </motion.div>

            {/* 原始卡片上不需要渐变遮罩 */}
          </motion.div>

          {/* 内容 - 标题在左下角 */}
          <motion.div
            className="absolute bottom-0 left-0 p-7 flex items-end justify-between w-full"
            layoutId={`card-content-${uniqueCardId}`}
          >
            <motion.h3
              className="text-xl font-bold text-foreground transition-colors duration-300 max-w-[220px] line-clamp-2"
              layoutId={`card-title-${uniqueCardId}`}
            >
              {title}
            </motion.h3>
            <motion.div
              className={`rounded-full p-3 transition-colors duration-300 ${
                isHovered ? "bg-black" : "bg-white"
              }`}
              style={{
                opacity: 1,
              }}
              animate={{
                scale: 1,
              }}
              transition={{ duration: 0.2 }}
              layoutId={`card-arrow-${uniqueCardId}`}
            >
              <ArrowRight
                className={`w-5 h-5 ${
                  isHovered ? "text-white" : "text-gray-800"
                }`}
                strokeWidth={2.5}
              />
            </motion.div>
          </motion.div>
        </motion.div>

        {/* 使用AnimatePresence的同步模式确保动画完成后再删除元素 */}
        <AnimatePresence mode="wait">
          {isOpen && (
            <motion.div
              key={`modal-${uniqueCardId}`}
              className="fixed inset-0 z-50 flex items-end justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {/* 背景遮罩 */}
              <motion.div
                className="absolute inset-0 bg-black/70"
                onClick={() => setIsOpen(false)}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              />

              {/* 弹出的详情卡片 - 使用容器宽度，固定高度并贴底 */}
              <FloatingContainer className="h-[90vh] flex items-end">
                <motion.div
                  className="relative bg-card rounded-t-3xl w-full overflow-hidden shadow-xl mx-auto h-[85vh] flex flex-col"
                  layoutId={`card-container-${uniqueCardId}`}
                  style={{
                    maxWidth: containerWidth > 0 ? containerWidth : "100%",
                  }}
                >
                  {/* 图片部分 - 作为整个浮层的背景 */}
                  <motion.div
                    className="absolute inset-0 w-full h-full"
                    layoutId={`card-image-container-${uniqueCardId}`}
                  >
                    {/* 使用内部div来控制缩放和透明度，避免与layoutId冲突 */}
                    <motion.div
                      className="w-full h-full"
                      initial={{ scale: 1, opacity: 0.4 }}
                      animate={{
                        scale: imageScale,
                        opacity: Math.min(imageOpacity * 0.4, 0.4),
                      }}
                      transition={{ duration: 0.1 }}
                    >
                      <Image
                        src={image}
                        alt={alt}
                        fill
                        className="object-cover"
                        quality={100}
                      />
                    </motion.div>

                    {/* 浮层状态下添加渐变遮罩 - 整个图片区域的渐变 */}
                    <motion.div
                      className="absolute inset-0 dark:hidden"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ delay: 0.2 }}
                      style={{
                        background:
                          "linear-gradient(to top, #EBE9E7 0%, rgba(235, 233, 231, 0.9) 20%, rgba(235, 233, 231, 0.6) 40%, rgba(235, 233, 231, 0.3) 60%)",
                      }}
                    />

                    {/* 为深色模式添加单独的遮罩 */}
                    <motion.div
                      className="absolute inset-0 hidden dark:block"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ delay: 0.2 }}
                      style={{
                        background:
                          "linear-gradient(to top, #1C1F26 0%, rgba(28, 31, 38, 0.9) 20%, rgba(28, 31, 38, 0.6) 40%, rgba(28, 31, 38, 0.3) 60%)",
                      }}
                    />
                  </motion.div>

                  {/* 内容部分 - 在图片背景上方滚动 */}
                  <motion.div
                    className="relative w-full h-full overflow-y-auto z-10 flex flex-col custom-scrollbar"
                    ref={contentRef}
                  >
                    {/* 给内容顶部一个足够大的空间，让图片能够显示 */}
                    <div className="h-[25vh] sm:h-[30vh] md:h-[40vh] flex-shrink-0"></div>

                    {/* 内容区域 - 在底部显示，带有圆角 */}
                    <div className="pb-28 rounded-t-3xl bg-card/0">
                      {/* 标题使用粘性定位，固定在顶部 */}
                      <motion.div
                        className="sticky top-0 pt-12 md:pt-16 pb-4 md:pb-8 z-20 flex justify-center"
                        ref={titleRef}
                      >
                        {/* 标题背景 - 深色模式 */}
                        <motion.div
                          className="absolute inset-0 hidden dark:block"
                          style={{
                            backgroundColor: `rgba(28, 31, 38, ${titleBgOpacity})`,
                          }}
                        />

                        {/* 标题背景 - 浅色模式 */}
                        <motion.div
                          className="absolute inset-0 dark:hidden"
                          style={{
                            backgroundColor: `rgba(235, 233, 231, ${titleBgOpacity})`,
                          }}
                        />

                        <motion.h3
                          className="text-[28px] sm:text-[36px] md:text-[42px] font-bold text-foreground relative z-10 text-center px-4"
                          layoutId={`card-title-${uniqueCardId}`}
                          style={{
                            fontSize:
                              titleTopDistance > 0.2
                                ? undefined
                                : isMobile
                                ? "28px"
                                : isTablet
                                ? "32px"
                                : "32px",
                            transition: "font-size 0.2s ease-in-out",
                          }}
                        >
                          {title}
                        </motion.h3>
                      </motion.div>

                      {/* 描述内容 - 添加上边距确保不会与标题重叠 */}
                      <motion.div
                        className="px-4 sm:px-8 md:px-16 lg:px-32 mx-2 sm:mx-6 md:mx-12 lg:mx-20"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.2 }}
                      >
                        <ArticleContent id={articleId} />
                      </motion.div>
                    </div>
                  </motion.div>

                  {/* 关闭按钮 - 移到最外层并增加z-index确保可点击 */}
                  <button
                    onClick={() => setIsOpen(false)}
                    onMouseEnter={() => setIsCloseHovered(true)}
                    onMouseLeave={() => setIsCloseHovered(false)}
                    className={`absolute top-3 sm:top-4 md:top-6 right-3 sm:right-4 md:right-6 p-2 sm:p-2.5 rounded-full transition-colors duration-200 z-50 ${
                      isCloseHovered
                        ? "bg-black text-white"
                        : "bg-white text-gray-800 "
                    }`}
                    aria-label="close"
                  >
                    <X className="w-5 h-5 sm:w-5 sm:h-5" />
                  </button>
                </motion.div>
              </FloatingContainer>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }
);

// 设置组件显示名称，方便调试
FeatureCard.displayName = "FeatureCard";

export default function KeyFeatures({
  title = "",
  subtitle = "",
  className = "",
  id = "keyfeatures", // 设置默认ID
}: KeyFeaturesProps) {
  // 使用组件级唯一ID确保多个实例不冲突
  const sectionId = useId();
  const uniqueSectionId = `${id}-${sectionId}`;

  // 添加滚动参考
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 添加状态来控制箭头按钮的显示
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  // 添加状态来控制左右按钮的禁用状态
  const [isAtStart, setIsAtStart] = useState(true);
  const [isAtEnd, setIsAtEnd] = useState(false);

  // 处理左右滚动
  const handleScroll = (direction: "left" | "right") => {
    if (scrollContainerRef.current) {
      const scrollAmount = 400; // 每次滚动的距离
      const currentScroll = scrollContainerRef.current.scrollLeft;

      scrollContainerRef.current.scrollTo({
        left:
          direction === "left"
            ? currentScroll - scrollAmount
            : currentScroll + scrollAmount,
        behavior: "smooth",
      });
    }
  };

  // 检查滚动位置并更新按钮状态
  const updateScrollButtonStates = useCallback(() => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } =
        scrollContainerRef.current;

      // 检查是否在滚动开始位置（左侧）
      setIsAtStart(scrollLeft <= 0);

      // 检查是否在滚动结束位置（右侧）- 添加1px的容差
      setIsAtEnd(Math.ceil(scrollLeft + clientWidth) >= scrollWidth - 1);

      // 检查是否需要滚动按钮
      setShowScrollButtons(scrollWidth > clientWidth);
    }
  }, []);

  // 初始检查和监听resize事件
  useEffect(() => {
    // 初始检查
    updateScrollButtonStates();

    // 添加resize监听器
    window.addEventListener("resize", updateScrollButtonStates);

    return () => {
      window.removeEventListener("resize", updateScrollButtonStates);
    };
  }, [updateScrollButtonStates]);

  // 监听滚动事件
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener("scroll", updateScrollButtonStates);

      return () => {
        scrollContainer.removeEventListener("scroll", updateScrollButtonStates);
      };
    }
  }, [updateScrollButtonStates]);

  return (
    <section className={`bg-white dark:bg-gray-900 ${className}`} id={id}>
      <Container>
        {/* 标题部分 - 居中对齐，移除副标题 */}
        <div className="flex justify-center mb-6 sm:mb-8 md:mb-12">
          <h2 className="font-bold font-sans text-center text-gray-800 dark:text-white max-w-[800px] leading-tight">
            {title}
          </h2>
        </div>

        {/* 在非移动端上使用横向滚动 */}
        <div className="relative">
          {/* 滚动控制按钮 - 只在非移动端且内容可滚动时显示 */}
          {showScrollButtons && (
            <div className="hidden sm:block">
              <button
                onClick={() => handleScroll("left")}
                className={`absolute left-[-20px] top-1/2 transform -translate-y-1/2 z-10 bg-background/80 hover:bg-background p-2 rounded-full shadow-md transition-opacity duration-300 ${
                  isAtStart ? "opacity-30 cursor-not-allowed" : "opacity-80"
                }`}
                disabled={isAtStart}
                aria-label="向左滚动"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M15 18l-6-6 6-6" />
                </svg>
              </button>
              <button
                onClick={() => handleScroll("right")}
                className={`absolute right-[-20px] top-1/2 transform -translate-y-1/2 z-10 bg-background/80 hover:bg-background p-2 rounded-full shadow-md transition-opacity duration-300 ${
                  isAtEnd ? "opacity-30 cursor-not-allowed" : "opacity-80"
                }`}
                disabled={isAtEnd}
                aria-label="向右滚动"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M9 18l6-6-6-6" />
                </svg>
              </button>
            </div>
          )}

          {/* 移动端使用垂直布局，非移动端使用横向滚动 */}
          <div
            ref={scrollContainerRef}
            className="flex flex-col sm:flex-row gap-4 sm:overflow-x-auto sm:scroll-smooth sm:snap-x sm:no-scrollbar"
            style={{ scrollbarWidth: "none" }}
            onScroll={updateScrollButtonStates}
          >
            {featureItems.map((feature, index) => (
              <div
                key={`${uniqueSectionId}-card-${index}`}
                className="sm:min-w-[360px] sm:snap-start"
              >
                <FeatureCard
                  title={feature.title}
                  image={feature.image}
                  alt={feature.alt}
                  articleId={feature.articleId}
                  instanceId={uniqueSectionId}
                />
              </div>
            ))}
          </div>
        </div>
      </Container>
    </section>
  );
}
