"use client";

import React, { useEffect, useState } from "react";
import Container from "../../ui/Container";
import Image from "next/image";
import { ArrowRight, Edit, Copy, RotateCcw } from "lucide-react";
import { useRouter } from "next/navigation";

interface HeroProps {
  title: React.ReactNode;
  subtitle: string;
  primaryButtonText: string;
}

export default function Hero({
  title = (
    <>
      <span className="text-primary">AI-Powered</span> Legal Intelligence
    </>
  ),
  subtitle = "Transforming legal practice with precision AI agents that analyze, research, and draft with unparalleled accuracy and efficiency.",
  primaryButtonText = "Schedule Demo",
}: HeroProps) {
  // 添加路由
  const router = useRouter();

  // 添加滚动状态
  const [scrollY, setScrollY] = useState(0);
  const [showCards, setShowCards] = useState(false);
  // 添加屏幕尺寸状态
  const [screenWidth, setScreenWidth] = useState(0);
  const [isNarrowScreen, setIsNarrowScreen] = useState(false);
  // 添加小屏幕检测状态
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  // 处理按钮点击事件
  const handleButtonClick = () => {
    router.push("/register");
  };

  // 监听滚动事件和屏幕尺寸变化
  useEffect(() => {
    // 初始化屏幕宽度
    setScreenWidth(window.innerWidth);
    setIsNarrowScreen(window.innerWidth < 1024);
    setIsSmallScreen(window.innerWidth < 768); // 设置小屏幕阈值为768px

    const handleScroll = () => {
      setScrollY(window.scrollY);

      // 当页面滚动超过50px时显示卡片（原来是100px）
      if (window.scrollY > 50) {
        setShowCards(true);
      } else {
        setShowCards(false);
      }
    };

    const handleResize = () => {
      const width = window.innerWidth;
      setScreenWidth(width);
      setIsNarrowScreen(width < 1024);
      setIsSmallScreen(width < 768); // 更新小屏幕状态
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    window.addEventListener("resize", handleResize, { passive: true });

    return () => {
      window.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return (
    <section
      className={`relative flex items-center ${
        isSmallScreen
          ? "min-h-[calc(50vh-4rem)] py-8"
          : "min-h-[calc(100vh-4rem)] pb-2 md:pb-3 pt-16"
      } overflow-hidden isolate`}
    >
      {/* 渐变背景效果 - 使用蓝绿色到紫色的渐变 */}
      <div className="absolute inset-0 -z-10">
        {/* 更丰富的渐变背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-cyan-50 via-indigo-50 to-violet-50 dark:from-cyan-900/20 dark:via-indigo-900/15 dark:to-violet-900/20" />

        {/* 蓝绿色光晕 */}
        <div className="absolute top-[-10%] left-[-5%] w-[45%] h-[45%] rounded-full bg-cyan-500/20 blur-[70px] opacity-70" />

        {/* 靛蓝色光晕 */}
        <div className="absolute top-[30%] left-[25%] w-[40%] h-[40%] rounded-full bg-indigo-500/15 blur-[60px] opacity-60" />

        {/* 紫色光晕 */}
        <div className="absolute bottom-[-5%] right-[-5%] w-[50%] h-[50%] rounded-full bg-violet-500/20 blur-[70px] opacity-70" />

        {/* 轻微毛玻璃效果 */}
        <div className="absolute inset-0 backdrop-blur-[12px] bg-white/25 dark:bg-gray-950/30" />
      </div>

      <Container className="relative">
        <div className="flex flex-col items-center text-center">
          <div
            className={`w-full max-w-3xl ${
              isSmallScreen ? "mt-4 mb-0" : "mt-16 mb-12"
            }`}
          >
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 md:mb-6 text-gray-800 dark:text-white leading-tight font-sans">
              {title}
            </h1>
            <p
              className={`text-base sm:text-lg md:text-xl ${
                isSmallScreen ? "mb-4" : "mb-6 md:mb-8"
              } font-medium text-foreground/50 mx-auto max-w-3xl leading-relaxed`}
            >
              {subtitle}
            </p>
            <div className="flex justify-center">
              <button
                onClick={handleButtonClick}
                className={`${
                  isSmallScreen ? "px-6 py-3 text-base" : "px-10 py-4 text-xl"
                } bg-primary text-white rounded-lg hover:bg-opacity-90 transition font-bold flex items-center gap-2 md:gap-3 relative overflow-hidden before:absolute before:w-[0.4rem] before:h-[20rem] before:top-0 before:translate-x-[-8rem] hover:before:translate-x-[20rem] before:duration-[0.8s] before:-skew-x-[10deg] before:transition-all before:bg-white before:blur-[8px] before:opacity-70 shadow-lg shadow-primary/30 hover:shadow-xl hover:shadow-primary/40`}
              >
                {primaryButtonText}
                <ArrowRight
                  className={`${isSmallScreen ? "w-4 h-4" : "w-5 h-5"}`}
                />
              </button>
            </div>
          </div>

          {/* 主视觉容器 - 只在非小屏幕上显示 */}
          {!isSmallScreen && (
            <div className="w-full relative">
              {/* 左侧悬浮卡片 - 在窄屏下调整位置或隐藏 */}
              <div
                className={`hidden md:block absolute ${
                  isNarrowScreen ? "left-[-5%]" : "left-[-10%] lg:left-[-10%]"
                } top-[65%] transform -translate-y-1/2 z-10 w-[20%] md:w-[25%] lg:w-[270px] h-auto transition-all duration-700 ease-out ${
                  showCards
                    ? "opacity-100 translate-y-[-50%]"
                    : "opacity-0 translate-y-[-30%]"
                } ${isNarrowScreen && screenWidth < 900 ? "opacity-0" : ""}`}
              >
                <div className="relative w-full h-full rounded-xl overflow-hidden shadow-2xl">
                  <Image
                    src="/images/left-card.png"
                    alt="Feature Card"
                    width={270}
                    height={180}
                    className="object-cover w-full h-auto"
                  />
                </div>
              </div>

              {/* 右侧毛玻璃悬浮卡片 - 在窄屏下调整位置或隐藏 */}
              <div
                className={`hidden md:block absolute ${
                  isNarrowScreen ? "right-[-5%]" : "right-[-5%] lg:right-[-10%]"
                } top-[40%] transform -translate-y-1/2 z-10 w-[30%] lg:w-[360px] max-w-[360px] h-auto transition-all duration-700 ease-out ${
                  showCards
                    ? "opacity-100 translate-y-[-50%]"
                    : "opacity-0 translate-y-[-30%]"
                } ${isNarrowScreen && screenWidth < 900 ? "opacity-0" : ""}`}
              >
                <div className="relative w-full rounded-xl overflow-hidden backdrop-blur-md bg-white/20 dark:bg-gray-800/30 border border-white/40 dark:border-gray-700/40 shadow-xl p-2 md:p-3 lg:p-4">
                  {/* 添加card-module.png图片到顶部 */}
                  <div className="w-full mb-2 md:mb-3 lg:mb-4">
                    <Image
                      src="/images/card-module.png"
                      alt="Card Module"
                      width={340}
                      height={150}
                      className="w-full h-auto rounded-lg"
                    />
                  </div>

                  {/* 添加指定文本，替换装饰性元素 */}
                  <p className="text-[10px] md:text-xs text-gray-500 dark:text-gray-200 font-medium leading-relaxed text-left mb-2 md:mb-3 lg:mb-4 line-clamp-3 md:line-clamp-none">
                    基于这 3 份展期协议和 2
                    份债权人清单的内容，帮我完善债务清偿这个部分
                  </p>

                  {/* 灰色分割线 */}
                  <div className="w-full h-px bg-gray-200 dark:bg-gray-700 my-2 md:my-3"></div>

                  {/* 底部操作区域 */}
                  <div className="flex justify-between items-center mt-1 md:mt-2 text-[8px] md:text-[10px]">
                    <div className="flex items-center text-gray-500 dark:text-gray-400">
                      <RotateCcw className="w-2.5 h-2.5 md:w-3 md:h-3 mr-1" />
                      <span className="hidden sm:inline">
                        Restore checkpoint
                      </span>
                      <span className="sm:hidden">Restore</span>
                    </div>
                    <div className="flex items-center gap-2 md:gap-3">
                      <button className="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-colors">
                        <Edit className="w-2.5 h-2.5 md:w-3.5 md:h-3.5" />
                      </button>
                      <button className="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-colors">
                        <Copy className="w-2.5 h-2.5 md:w-3.5 md:h-3.5" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* 主视觉图片容器 */}
              <div className="w-full max-w-full mx-auto pb-[56.25%] relative mb-6 md:mb-10">
                {/* 主要图形元素 */}
                <div className="absolute inset-0 rounded-lg md:rounded-xl overflow-hidden shadow-xl shadow-gray-300/50 dark:shadow-gray-900/30">
                  <div className="absolute inset-0 bg-foreground/10"></div>

                  {/* 主视觉图片 */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Image
                      src="/images/zhuye.png"
                      alt="AI-Powered Legal Intelligence"
                      fill
                      className="object-contain"
                      priority
                    />
                  </div>
                </div>

                {/* 底部阴影效果 */}
                <div className="absolute -bottom-5 left-1/2 transform -translate-x-1/2 w-[96%] h-6 bg-gradient-to-b from-gray-400/20 to-transparent rounded-full blur-xl"></div>
              </div>
            </div>
          )}
        </div>
      </Container>
    </section>
  );
}
