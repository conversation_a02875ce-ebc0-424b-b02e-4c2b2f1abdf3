import React from "react";

interface ArticleContentProps {
  id: string; // 文章ID，用于加载对应的内容
}

// 文章内容示例
const articleContents: Record<string, string> = {
  "knowledge-research": `
    <div class="article-content">
      <h2 class="text-2xl font-bold mb-6">知识库深度研究</h2>
      
      <p class="mb-4">通过利用先进的知识库系统进行深入研究，我们帮助用户快速获取专业知识和见解。我们的系统能够分析复杂的法律文本，提取关键信息，并提供详细的背景解释。</p>
      
      <h3 class="text-xl font-semibold mt-8 mb-4">主要功能</h3>
      <ul class="list-disc pl-5 mb-6 space-y-2">
        <li>智能文本分析与关键信息提取</li>
        <li>多维度法律知识关联</li>
        <li>专业术语解释与背景说明</li>
        <li>相关案例推荐与分析</li>
      </ul>
      
      <blockquote class="border-l-4 border-card-hover dark:border-gray-600 pl-4 italic my-6">
        "知识库深度研究功能帮助我们节省了80%的案例研究时间，显著提高了工作效率。" — 某知名律所合伙人
      </blockquote>
      
      <h3 class="text-xl font-semibold mt-8 mb-4">应用场景</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div class="bg-gray-50 dark:bg-gray-600/30 p-4 rounded-lg">
          <h4 class="font-medium mb-2">法律研究</h4>
          <p>快速获取相关法规、判例和学术观点</p>
        </div>
        <div class="bg-gray-50 dark:bg-gray-600/30 p-4 rounded-lg">
          <h4 class="font-medium mb-2">合同审核</h4>
          <p>识别潜在风险条款并提供改进建议</p>
        </div>
        <div class="bg-gray-50 dark:bg-gray-600/30 p-4 rounded-lg">
          <h4 class="font-medium mb-2">案例分析</h4>
          <p>发现类似案例并分析判决依据</p>
        </div>
        <div class="bg-gray-50 dark:bg-gray-600/30 p-4 rounded-lg">
          <h4 class="font-medium mb-2">法律咨询</h4>
          <p>提供准确的法律建议和解决方案</p>
        </div>
      </div>
      
      <div class="border-t border-card-hover dark:border-gray-700 pt-6 mt-8">
        <h3 class="text-xl font-semibold mb-4">技术亮点</h3>
        <p class="mb-4">我们的知识库深度研究功能采用了最新的自然语言处理技术，能够理解复杂的法律语境，识别专业术语，并建立知识图谱，为用户提供全面而准确的法律信息。</p>
      </div>
    </div>
  `,

  "contract-audit": `
    <div class="article-content">
      <h2 class="text-2xl font-bold mb-6">背景合同审计系统</h2>
      
      <p class="mb-4">背景合同审计系统自动检查合同中的关键条款和潜在风险，确保法律文件符合相关法规和最佳实践。该系统能够识别异常条款和缺失内容，提供专业的审计报告。</p>
      
      <div class="bg-gray-50 dark:bg-gray-600/30 p-4 rounded-lg mb-6">
        <h4 class="font-medium mb-2">系统优势</h4>
        <p>自动化审计流程，减少人为错误，提高效率，降低法律风险</p>
      </div>
      
      <h3 class="text-xl font-semibold mt-8 mb-4">审计流程</h3>
      <ol class="list-decimal pl-5 mb-6 space-y-2">
        <li>上传合同文件</li>
        <li>系统自动识别合同类型</li>
        <li>条款提取与分析</li>
        <li>风险评估与标记</li>
        <li>生成详细审计报告</li>
      </ol>
      
      <div class="grid grid-cols-1 gap-4 mb-8">
        <div class="border border-card-hover dark:border-gray-700 rounded-lg p-4">
          <h4 class="font-medium mb-2 flex items-center">
            <span class="inline-block w-4 h-4 rounded-full bg-red-500 mr-2"></span>
            高风险条款
          </h4>
          <p>系统会自动标记可能导致重大法律或商业风险的条款</p>
        </div>
        <div class="border border-card-hover dark:border-gray-700 rounded-lg p-4">
          <h4 class="font-medium mb-2 flex items-center">
            <span class="inline-block w-4 h-4 rounded-full bg-yellow-500 mr-2"></span>
            中风险条款
          </h4>
          <p>需要注意但不会造成重大影响的条款</p>
        </div>
        <div class="border border-card-hover dark:border-gray-700 rounded-lg p-4">
          <h4 class="font-medium mb-2 flex items-center">
            <span class="inline-block w-4 h-4 rounded-full bg-green-500 mr-2"></span>
            标准条款
          </h4>
          <p>符合行业标准和最佳实践的条款</p>
        </div>
      </div>
      
      <div class="not-prose overflow-hidden rounded-lg border border-card-hover dark:border-gray-700">
        <table class="w-full border-collapse m-0">
          <thead>
            <tr class="bg-card-hover dark:bg-gray-800">
              <th class="border-b border-r border-card-hover dark:border-gray-700 p-2 text-left">审计项目</th>
              <th class="border-b border-card-hover dark:border-gray-700 p-2 text-left">自动化程度</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="border-b border-r border-card-hover dark:border-gray-700 p-2">条款完整性检查</td>
              <td class="border-b border-card-hover dark:border-gray-700 p-2">100%</td>
            </tr>
            <tr>
              <td class="border-b border-r border-card-hover dark:border-gray-700 p-2">法规合规性分析</td>
              <td class="border-b border-card-hover dark:border-gray-700 p-2">95%</td>
            </tr>
            <tr>
              <td class="border-b border-r border-card-hover dark:border-gray-700 p-2">风险条款识别</td>
              <td class="border-b border-card-hover dark:border-gray-700 p-2">90%</td>
            </tr>
            <tr>
              <td class="border-r border-card-hover dark:border-gray-700 p-2">商业条款评估</td>
              <td class="p-2">85%</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  `,

  "legal-document": `
    <div class="article-content">
      <h2 class="text-2xl font-bold mb-6">法律文档助手</h2>
      
      <p class="mb-4">法律文档助手可以帮助起草、审核和管理各类法律文件，确保文件符合最新的监管要求。该助手提供模板推荐、条款优化和风险评估，简化法律文档处理流程。</p>
      
      <div class="flex flex-col md:flex-row gap-6 my-8">
        <div class="flex-1 bg-gray-50 dark:bg-gray-600/30 p-5 rounded-lg">
          <h3 class="text-xl font-semibold mb-3">文档起草</h3>
          <p>根据用户需求和行业标准，智能生成专业法律文档初稿</p>
        </div>
        <div class="flex-1 bg-gray-50 dark:bg-gray-600/30 p-5 rounded-lg">
          <h3 class="text-xl font-semibold mb-3">文档审核</h3>
          <p>全面检查文档内容，确保法律准确性和合规性</p>
        </div>
        <div class="flex-1 bg-gray-50 dark:bg-gray-600/30 p-5 rounded-lg">
          <h3 class="text-xl font-semibold mb-3">文档管理</h3>
          <p>集中存储、分类和检索各类法律文档，便于协作和追踪</p>
        </div>
      </div>
      
      <h3 class="text-xl font-semibold mt-8 mb-4">支持的文档类型</h3>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
        <div class="text-center p-3 border border-card-hover dark:border-gray-700 rounded-lg">
          <p>合同协议</p>
        </div>
        <div class="text-center p-3 border border-card-hover dark:border-gray-700 rounded-lg">
          <p>法律意见书</p>
        </div>
        <div class="text-center p-3 border border-card-hover dark:border-gray-700 rounded-lg">
          <p>诉讼文书</p>
        </div>
        <div class="text-center p-3 border border-card-hover dark:border-gray-700 rounded-lg">
          <p>公司章程</p>
        </div>
        <div class="text-center p-3 border border-card-hover dark:border-gray-700 rounded-lg">
          <p>知识产权文件</p>
        </div>
        <div class="text-center p-3 border border-card-hover dark:border-gray-700 rounded-lg">
          <p>合规报告</p>
        </div>
        <div class="text-center p-3 border border-card-hover dark:border-gray-700 rounded-lg">
          <p>监管申报</p>
        </div>
        <div class="text-center p-3 border border-card-hover dark:border-gray-700 rounded-lg">
          <p>商业协议</p>
        </div>
      </div>
      
      <div class="bg-gray-50 dark:bg-gray-600/30 p-6 rounded-lg my-8">
        <h3 class="text-xl font-semibold mb-4">工作流程</h3>
        <div class="flex flex-col md:flex-row items-center justify-between">
          <div class="text-center p-4">
            <div class="w-12 h-12 rounded-full bg-card-hover dark:bg-gray-700 flex items-center justify-center mx-auto mb-2">1</div>
            <p>需求分析</p>
          </div>
          <div class="hidden md:block">→</div>
          <div class="text-center p-4">
            <div class="w-12 h-12 rounded-full bg-card-hover dark:bg-gray-700 flex items-center justify-center mx-auto mb-2">2</div>
            <p>模板选择</p>
          </div>
          <div class="hidden md:block">→</div>
          <div class="text-center p-4">
            <div class="w-12 h-12 rounded-full bg-card-hover dark:bg-gray-700 flex items-center justify-center mx-auto mb-2">3</div>
            <p>内容生成</p>
          </div>
          <div class="hidden md:block">→</div>
          <div class="text-center p-4">
            <div class="w-12 h-12 rounded-full bg-card-hover dark:bg-gray-700 flex items-center justify-center mx-auto mb-2">4</div>
            <p>审核优化</p>
          </div>
          <div class="hidden md:block">→</div>
          <div class="text-center p-4">
            <div class="w-12 h-12 rounded-full bg-card-hover dark:bg-gray-700 flex items-center justify-center mx-auto mb-2">5</div>
            <p>完成归档</p>
          </div>
        </div>
      </div>
      
      <div class="border-t border-card-hover dark:border-gray-700 pt-6 mt-8">
        <h3 class="text-xl font-semibold mb-4">用户反馈</h3>
        <div class="italic">
          "法律文档助手帮助我们公司标准化了所有合同流程，大大提高了法务部门的工作效率，减少了合同审核时间。"
        </div>
        <p class="text-right mt-2 text-sm">— 某跨国公司法务总监</p>
      </div>
    </div>
  `,
};

const ArticleContent: React.FC<ArticleContentProps> = ({ id }) => {
  // 获取对应ID的文章内容，如果不存在则显示默认内容
  const content = articleContents[id] || "<p>文章内容不存在</p>";

  return (
    <div
      className="max-w-none"
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

export default ArticleContent;
