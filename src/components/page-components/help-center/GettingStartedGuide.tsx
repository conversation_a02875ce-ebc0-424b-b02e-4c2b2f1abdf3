"use client";

import React from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import Image from "next/image";
import { ChevronRight } from "lucide-react";
import Container from "../../ui/Container";

export default function GettingStartedGuide() {
  // 卡片数据
  const guideCards = [
    {
      id: 1,
      title: "认识 Quote",
      description:
        "了解 Quote 的核心功能和价值，探索我们如何帮助您提高工作效率",
      href: "/document?id=introduction",
      color: "bg-blue-50 dark:bg-blue-900/20",
    },
    {
      id: 2,
      title: "新用户入门",
      description: "从零开始学习使用 Quote，快速掌握基础操作和核心功能",
      href: "/document?id=quick-start",
      color: "bg-green-50 dark:bg-green-900/20",
    },
  ];

  return (
    <section className="bg-white dark:bg-gray-900 py-16 md:py-20">
      <Container>
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100">
            入门指南
          </h2>
        </div>

        <div className="flex flex-wrap justify-center gap-8 px-4 sm:px-6">
          {guideCards.map((card) => (
            <motion.div
              key={card.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: card.id * 0.1 }}
              className="w-full max-w-[95vw] sm:max-w-[400px]"
            >
              <Link href={card.href} target="_blank" rel="noopener noreferrer">
                <div
                  className={`p-0 rounded-xl w-full h-[380px] transition-all duration-300 hover:shadow-lg hover:-translate-y-1 cursor-pointer relative overflow-hidden`}
                >
                  {card.id === 1 ? (
                    <>
                      <Image
                        src="/images/user-cards/Quote-1.png"
                        alt={card.title}
                        fill
                        quality={100}
                        className="object-cover"
                        priority
                      />
                    </>
                  ) : card.id === 2 ? (
                    <>
                      <Image
                        src="/images/user-cards/Quote-2.png"
                        alt={card.title}
                        fill
                        quality={100}
                        className="object-cover"
                        priority
                      />
                    </>
                  ) : (
                    <div className={`${card.color} w-full h-full`}></div>
                  )}

                  <div className="absolute inset-0 p-6 md:p-8 flex flex-col">
                    <div className="text-left z-10">
                      <div className="flex items-center mb-3">
                        <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                          {card.title}
                        </h3>
                        <ChevronRight className="w-5 h-5 ml-2 text-gray-900" />
                      </div>
                      <p className="text-gray-600 dark:text-white font-regular">
                        {card.description}
                      </p>
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      </Container>
    </section>
  );
}
