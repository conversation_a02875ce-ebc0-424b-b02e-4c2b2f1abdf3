"use client";

import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import Container from "../../ui/Container";
import { useTheme } from "next-themes";

interface HelpCenterHeroProps {
  title?: React.ReactNode;
  subtitle?: string;
}

export default function HelpCenterHero({
  title = (
    <>
      Quote <span className="text-primary font-sans">帮助中心</span>
    </>
  ),
  subtitle = "获取关于 Quote 产品的帮助和支持，解决您在使用过程中遇到的问题",
}: HelpCenterHeroProps) {
  // 添加小屏幕检测状态
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const { theme } = useTheme();

  // 监听屏幕尺寸变化
  useEffect(() => {
    // 初始化小屏幕状态
    setIsSmallScreen(window.innerWidth < 768);
    setMounted(true);

    const handleResize = () => {
      setIsSmallScreen(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize, { passive: true });

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  // 根据主题确定渐变颜色
  const gradientStart = "#F0F0FF";
  const gradientEndLight = "#FFFFFF";
  const gradientEndDark = "#101010";

  const gradientEndColor =
    mounted && theme === "dark" ? gradientEndDark : gradientEndLight;

  return (
    <div className="relative w-full overflow-hidden bg-white dark:bg-gray-900">
      {/* 在小屏幕上显示渐变背景，大屏幕上显示背景图片 */}
      {isSmallScreen ? (
        <div
          className="absolute inset-0 z-0"
          style={{
            background: mounted
              ? `linear-gradient(to bottom, 
                ${gradientStart} 0%, 
                ${gradientEndColor} 30%)`
              : "transparent",
          }}
        ></div>
      ) : (
        <>
          {/* 背景图片 - 覆盖整个区域，撑满宽度，高质量不压缩 */}
          <div className="absolute inset-0 w-full h-full">
            <Image
              src="/images/help-2.png"
              alt="帮助中心背景"
              fill
              priority
              quality={100}
              className="object-cover w-full"
              unoptimized
            />

            {/* 左侧白色渐变透明遮罩 */}
            <div className="absolute left-0 top-0 bottom-0 w-1/6 md:w-1/5 bg-gradient-to-r from-white to-transparent dark:from-gray-900 dark:to-transparent"></div>

            {/* 右侧白色渐变透明遮罩 */}
            <div className="absolute right-0 top-0 bottom-0 w-1/6 md:w-1/5 bg-gradient-to-l from-white to-transparent dark:from-gray-900 dark:to-transparent"></div>
          </div>
        </>
      )}

      {/* 内容区域 - 小屏幕时降低高度 */}
      <div
        className={`relative z-10 w-full ${
          isSmallScreen
            ? "pt-24 pb-12 min-h-[280px]"
            : "pt-36 pb-16 md:pt-44 md:pb-20 min-h-[380px]"
        }`}
      >
        <Container>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex items-center h-full"
          >
            <div
              className={`w-full max-w-2xl ${
                isSmallScreen ? "text-center mx-auto" : "text-left"
              }`}
            >
              <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 md:mb-6 text-gray-800 dark:text-white leading-tight font-sans">
                {title}
              </h1>
              <p
                className={`text-base sm:text-lg md:text-xl mb-6 md:mb-8 font-medium text-gray-700 dark:text-gray-200 ${
                  isSmallScreen ? "mx-auto" : ""
                } max-w-xl leading-relaxed`}
              >
                {subtitle}
              </p>
            </div>
          </motion.div>
        </Container>
      </div>
    </div>
  );
}
