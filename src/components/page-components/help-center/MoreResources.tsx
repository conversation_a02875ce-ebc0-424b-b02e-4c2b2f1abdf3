"use client";

import * as React from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { ChevronRight, Rocket, Users } from "lucide-react";
import Container from "../../ui/Container";

export default function MoreResources() {
  // 资源卡片数据
  const resourceCards = [
    {
      id: 1,
      title: "产品更新",
      items: [
        {
          text: "产品介绍：Quote 是一款专为法律专业人士设计的 AI 驱动法律知识引擎",
          href: "/document?id=introduction",
        },
        {
          text: "快速开始：帮助您快速上手我们的产品，了解基本功能和操作流程",
          href: "/document?id=quick-start",
        },
        {
          text: "基本功能：多种功能帮助您高效处理法律文档和知识管理",
          href: "/document?id=basic-features",
        },
      ],
      icon: <Rocket className="w-6 h-6" />,
      href: "/document",
      color: "bg-background dark:bg-gray-900",
      iconColor: "text-blue-500 dark:text-blue-400",
      buttonText: "查看更多",
      openInNewTab: false,
    },
    {
      id: 2,
      title: "客户案例",
      items: [
        {
          text: "金融行业解决方案：为银行、保险、证券等金融机构提供合规审核",
          href: "/case-study?id=finance-industry",
        },
        {
          text: "医疗健康行业解决方案：帮助医疗机构处理患者隐私保护与合规",
          href: "/case-study?id=healthcare-industry",
        },
        {
          text: "科技企业解决方案：为科技公司提供知识产权保护与数据合规",
          href: "/case-study?id=tech-industry",
        },
      ],
      icon: <Users className="w-6 h-6" />,
      href: "/use-cases",
      color: "bg-background dark:bg-gray-900",
      iconColor: "text-green-500 dark:text-green-400",
      buttonText: "查看更多",
      openInNewTab: true,
    },
  ];

  return (
    <section className="bg-white dark:bg-gray-900 py-16 md:py-20">
      <Container>
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100">
            更多资源
          </h2>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {resourceCards.map((card) => (
            <motion.div
              key={card.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: card.id * 0.1 }}
              className={`${card.color} rounded-xl overflow-hidden shadow-md shadow-gray-200/50 dark:shadow-gray-900/30`}
            >
              {/* 卡片头部 */}
              <div className="p-6 flex items-center">
                <div
                  className={`p-3 rounded-lg ${card.iconColor} bg-white dark:bg-gray-800 mr-4`}
                >
                  {card.icon}
                </div>
                <h3 className="text-xl font-bold">{card.title}</h3>
              </div>

              {/* 卡片内容 - 列表项 */}
              <div className="px-6 pb-4">
                <ul className="space-y-4">
                  {card.items.map((item, index) => (
                    <li key={index} className="flex">
                      <span className="text-lg text-primary mr-2">•</span>
                      <Link
                        href={item.href}
                        className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 flex-1 truncate"
                        target={card.openInNewTab ? "_blank" : undefined}
                        rel={
                          card.openInNewTab ? "noopener noreferrer" : undefined
                        }
                      >
                        {item.text}
                      </Link>
                      <ChevronRight className="w-4 h-4 text-gray-400 dark:text-gray-500 flex-shrink-0 mt-1" />
                    </li>
                  ))}
                </ul>
              </div>

              {/* 卡片底部 - 查看更多按钮 */}
              <div className="px-6 pb-6">
                <Link
                  href={card.href}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-800 transition-colors duration-200"
                  target={card.openInNewTab ? "_blank" : undefined}
                  rel={card.openInNewTab ? "noopener noreferrer" : undefined}
                >
                  <span>{card.buttonText}</span>
                  <ChevronRight className="w-4 h-4 ml-1" />
                </Link>
              </div>
            </motion.div>
          ))}
        </div>
      </Container>
    </section>
  );
}
