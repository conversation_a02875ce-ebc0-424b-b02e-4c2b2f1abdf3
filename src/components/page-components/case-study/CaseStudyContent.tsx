"use client";

import React from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import Container from "../../ui/Container";
import FloatingCard from "./FloatingCard";

interface ResultItem {
  metric: string;
  value: string;
}

interface CaseStudyContentProps {
  company: string;
  challenges: string[];
  solutions: string[];
  results: ResultItem[];
  testimonial?: {
    quote: string;
    avatar: string;
    name: string;
    title: string;
    logo: string;
  };
}

// 创建一个示例数据对象，用于展示360数字安全集团的案例
const example360Case = {
  company: "360数字安全集团",
  challenges: [
    "需要支持近百款标品的持续迭代，同时支撑大量的定制化项目设计",
    "计划中往往面临诸多应急工作，影响正常工作进度",
    "设计团队规模有限，难以满足多元安全业务的全面需求",
    "toB、toG多元安全业务协作效率低下，流程复杂",
  ],
  solutions: [
    "引入Quote高效设计生产力工具，提升团队日常工作效率",
    "构建设计规范和UI组件模版等高保真资源，推送赋能给产品团队",
    "在常规OEM类产品和定制化项目上采用轻度介入协作方式",
    "通过共性设计资源的搭建提升团队自身设计效能",
  ],
  results: [
    { metric: "协作效能提升", value: "提升85%" },
    { metric: "设计交付速度", value: "提升70%" },
    { metric: "业务流程简化", value: "减少50%" },
    { metric: "团队工作ROI", value: "提升65%" },
  ],
  testimonial: {
    quote:
      "通过Quote，我们将设计规范、UI组件模版等高保真资源推送赋能给产品团队，在一些常规的OEM类产品、定制化项目上不再重度依赖UI设计投入，转为以校对、把控的轻度介入协作方式，大幅提升协作效能。",
    avatar: "/images/madou.png", // 假设有这个头像图片
    name: "彭波",
    title: "360数字安全集团研发共享支持中心-用户体验设计负责人",
    logo: "/logo/logo.png", // 使用现有的logo
  },
};

// 引用留言卡片组件
const TestimonialCard = ({
  testimonial,
}: {
  testimonial: CaseStudyContentProps["testimonial"];
}) => {
  if (!testimonial) return null;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-12">
      <div className="mb-8">
        <svg
          className="w-8 h-8 text-gray-400 dark:text-gray-500 mb-4"
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
        </svg>
        <p className="text-lg text-gray-700 dark:text-gray-300 italic">
          "{testimonial.quote}"
        </p>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex items-start">
          <div className="flex-shrink-0 mr-4">
            <Image
              src={testimonial.avatar}
              alt={testimonial.name}
              width={50}
              height={50}
              className="rounded-full"
            />
          </div>
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white">
              {testimonial.name}
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {testimonial.title}
            </p>
          </div>
        </div>
        <div className="flex-shrink-0">
          <Image
            src={testimonial.logo}
            alt="公司logo"
            width={100}
            height={40}
            className="h-8 w-auto object-contain"
          />
        </div>
      </div>
    </div>
  );
};

export default function CaseStudyContent({
  company = example360Case.company,
  challenges = example360Case.challenges,
  solutions = example360Case.solutions,
  results = example360Case.results,
  testimonial = example360Case.testimonial,
}: CaseStudyContentProps) {
  return (
    <div className="bg-white dark:bg-gray-900 py-12 sm:py-16 md:py-20">
      <Container>
        <div className="flex flex-col lg:flex-row gap-8">
          <div className="lg:w-3/4">
            {/* 引用留言卡片 */}
            <TestimonialCard testimonial={testimonial} />

            {/* 客户挑战部分 */}
            <div className="mb-16">
              <h3 className="text-2xl md:text-3xl font-bold text-gray-800 dark:text-white mb-6">
                客户挑战
              </h3>
              <div className="mb-8">
                <p className="text-lg text-gray-700 dark:text-gray-300 mb-4">
                  作为数字安全的领导者，{company}
                  秉持"上山下海助小微"战略方针，以"安全即服务"为核心发展理念。在人员有限的情况下，如何提高团队的协作和产出效率成为了重中之重。团队面临以下挑战：
                </p>
                <ul className="list-disc pl-5 space-y-2">
                  {challenges.map((challenge, index) => (
                    <li
                      key={`challenge-${index}`}
                      className="text-gray-700 dark:text-gray-300"
                    >
                      {challenge}
                    </li>
                  ))}
                </ul>
              </div>
              <div className="rounded-lg overflow-hidden shadow-md">
                <Image
                  src="/images/law1.jpeg"
                  alt="客户挑战"
                  width={1000}
                  height={600}
                  className="w-full h-auto object-cover"
                />
              </div>
            </div>

            {/* 解决方案部分 */}
            <div className="mb-16">
              <h3 className="text-2xl md:text-3xl font-bold text-gray-800 dark:text-white mb-6">
                解决方案
              </h3>
              <div className="mb-8">
                <p className="text-lg text-gray-700 dark:text-gray-300 mb-4">
                  Quote为{company}
                  提供了定制化的设计协作解决方案，实现"业务赋能做减法，能力边界做加法"的目标：
                </p>
                <ul className="list-disc pl-5 space-y-2">
                  {solutions.map((solution, index) => (
                    <li
                      key={`solution-${index}`}
                      className="text-gray-700 dark:text-gray-300"
                    >
                      {solution}
                    </li>
                  ))}
                </ul>
              </div>
              <div className="rounded-lg overflow-hidden shadow-md">
                <Image
                  src="/images/law2.png"
                  alt="解决方案"
                  width={1000}
                  height={600}
                  className="w-full h-auto object-cover"
                />
              </div>
            </div>

            {/* 实施效果部分 */}
            <div>
              <h3 className="text-2xl md:text-3xl font-bold text-gray-800 dark:text-white mb-6">
                实施效果
              </h3>
              <p className="text-lg text-gray-700 dark:text-gray-300 mb-4">
                通过Quote的解决方案，{company}实现了显著的业务提升：
              </p>
              <ul className="list-disc pl-5 space-y-2 mb-8">
                {results.map((result, index) => (
                  <li
                    key={`result-${index}`}
                    className="text-gray-700 dark:text-gray-300"
                  >
                    <span className="font-medium">{result.metric}：</span>
                    {result.value}
                  </li>
                ))}
              </ul>
              <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
                "通过Quote，我们将设计规范、UI组件模版等高保真资源推送赋能给产品团队，在一些常规的OEM类产品、定制化项目上不再重度依赖UI设计投入，转为以校对、把控的轻度介入协作方式，大幅提升协作效能。团队在Quote平台上通过共性设计资源的搭建不仅提升团队自身设计效能，而且将资源开放至产研业务团队，达到深度赋能的效果，在生产执行过程中实现岗位角色补位，简化协作流程自然就形成了对产研流程的减法。"
                — 研发共享支持中心-用户体验设计负责人彭波
              </p>
              <div className="rounded-lg overflow-hidden shadow-md">
                <Image
                  src="/images/law3.jpeg"
                  alt="实施效果"
                  width={1000}
                  height={600}
                  className="w-full h-auto object-cover"
                />
              </div>
            </div>
          </div>

          {/* 右侧浮窗卡片 */}
          <div className="lg:w-1/4 lg:sticky lg:top-24 h-fit">
            <FloatingCard />
          </div>
        </div>
      </Container>
    </div>
  );
}
