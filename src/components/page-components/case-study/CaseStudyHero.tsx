"use client";

import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import Container from "../../ui/Container";

interface CaseStudyHeroProps {
  title?: string;
  company?: string;
  industry?: string;
  imageSrc?: string;
  logoSrc?: string;
}

export default function CaseStudyHero({
  title = "某金融机构智能合规解决方案",
  company = "中国某大型金融机构",
  industry = "金融行业",
  imageSrc = "/images/law1.jpeg",
  logoSrc = "/images/logo2.png",
}: CaseStudyHeroProps) {
  // 添加小屏幕检测状态
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  // 监听屏幕尺寸变化
  useEffect(() => {
    // 初始化小屏幕状态
    setIsSmallScreen(window.innerWidth < 768);

    const handleResize = () => {
      setIsSmallScreen(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize, { passive: true });

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return (
    <div className="relative w-full overflow-hidden bg-white dark:bg-gray-900 mt-16">
      {/* 贯穿页面左右的背景图片 - 添加顶部边距，避免与导航栏重叠 */}
      <div className="absolute inset-0 w-full h-full">
        <Image
          src={imageSrc}
          alt={title}
          fill
          priority
          quality={100}
          className="object-cover w-full"
          unoptimized
        />

        {/* 暗色遮罩，提高文字可读性 */}
        <div className="absolute inset-0 bg-black/50"></div>

        {/* 左侧渐变透明遮罩 */}
        <div className="absolute left-0 top-0 bottom-0 w-1/6 md:w-1/5 bg-gradient-to-r from-black/70 to-transparent"></div>

        {/* 右侧渐变透明遮罩 */}
        <div className="absolute right-0 top-0 bottom-0 w-1/6 md:w-1/5 bg-gradient-to-l from-black/70 to-transparent"></div>
      </div>

      {/* 内容区域 - 调整上下内边距，确保与导航栏有足够间距 */}
      <div className="relative z-10 w-full py-32 sm:py-36 md:py-40">
        <Container>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex flex-col items-start"
          >
            <div className="max-w-2xl">
              {/* 公司logo */}
              {logoSrc && (
                <div className="mb-6 bg-white/90 dark:bg-gray-800/90 p-3 rounded-lg inline-block">
                  <Image
                    src={logoSrc}
                    alt={company}
                    width={120}
                    height={40}
                    className="h-8 w-auto object-contain"
                  />
                </div>
              )}

              {/* 标题 */}
              <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 text-white leading-tight">
                {title}
              </h1>

              {/* 公司名称 */}
              <p className="text-lg sm:text-xl text-white/80 font-medium">
                {company}
              </p>
            </div>
          </motion.div>
        </Container>
      </div>
    </div>
  );
}
