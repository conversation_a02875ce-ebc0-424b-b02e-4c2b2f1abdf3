"use client";

import React, { useState } from "react";
import Image from "next/image";
import QRCodeModal from "../../ui/QRCodeModal";

export default function FloatingCard() {
  // 添加二维码浮层状态
  const [isQRCodeVisible, setIsQRCodeVisible] = useState(false);

  // 处理按钮点击事件
  const handleButtonClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsQRCodeVisible(true);
  };

  // 关闭二维码浮层
  const handleCloseQRCode = () => {
    setIsQRCodeVisible(false);
  };

  return (
    <>
      {/* 使用通用二维码浮层组件 */}
      <QRCodeModal isVisible={isQRCodeVisible} onClose={handleCloseQRCode} />

      <div className="rounded-lg overflow-hidden shadow-lg sticky top-24 w-[280px]">
        {/* 上半部分 - 浮窗卡片背景 */}
        <div className="bg-floatingCard p-6 text-center flex flex-col items-center">
          <div className="mb-2">
            <Image
              src="/logo/quote-logo.svg"
              alt="Quote Logo"
              width={120}
              height={40}
              className="h-8 w-auto"
            />
          </div>
          <p className="text-sm font-medium mt-2 text-floatingCard-text/70">
            让法律人的时间更有价值
          </p>
        </div>

        {/* 下半部分 - 白色背景和联系按钮 */}
        <div className="bg-white dark:bg-gray-800 p-6">
          <button
            onClick={handleButtonClick}
            className="block w-full bg-primary hover:bg-primary/90 text-white text-center py-3 px-4 rounded-md transition-colors duration-200 font-medium"
          >
            联系我们
          </button>
        </div>
      </div>
    </>
  );
}
