"use client";

import * as React from "react";
import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";
import Container from "../../ui/Container";
import { ArrowRight } from "lucide-react";

// 案例卡片数据接口
interface CustomerItem {
  id: string;
  name: string;
  imageSrc: string;
  description?: string;
  solution?: string;
  logoSrc?: string;
  slug?: string; // 添加slug字段用于构建URL
}

// 组件属性接口
interface UseCasesCustomersProps {
  title?: React.ReactNode;
  customers?: CustomerItem[];
  className?: string;
  id?: string;
}

// 默认案例数据 - 针对不同行业的解决方案
const defaultCustomers: CustomerItem[] = [
  {
    id: "customer1",
    name: "金融行业解决方案",
    imageSrc: "/images/law1.jpeg",
    description:
      "为银行、保险、证券等金融机构提供合规审核、风险评估和智能合同管理",
    solution: "合规审核与风险管理",
    logoSrc: "/images/logo2.png",
    slug: "finance-industry", // 添加slug
  },
  {
    id: "customer2",
    name: "医疗健康行业解决方案",
    imageSrc: "/images/law2.png",
    description:
      "帮助医疗机构处理患者隐私保护、医疗纠纷和知识产权保护等法律问题",
    solution: "隐私保护与合规",
    logoSrc: "/images/logo2.png",
    slug: "healthcare-industry", // 添加slug
  },
  {
    id: "customer3",
    name: "科技企业解决方案",
    imageSrc: "/images/law3.jpeg",
    description: "为科技公司提供知识产权保护、数据合规和商业合同智能化管理",
    solution: "知识产权与数据合规",
    logoSrc: "/images/logo2.png",
    slug: "tech-industry", // 添加slug
  },
];

// 客户案例卡片组件
const CustomerCard = ({
  id,
  name,
  imageSrc,
  solution,
  logoSrc,
  slug = "case-study",
}: CustomerItem) => {
  const [isHovered, setIsHovered] = React.useState(false);

  return (
    <Link
      href={`/case-study?id=${slug}`}
      className="block h-full"
      target="_blank"
      rel="noopener noreferrer"
    >
      <div
        className={`rounded-lg overflow-hidden transition-all duration-300 hover:scale-[1.02] h-full cursor-pointer ${
          isHovered
            ? "shadow-xl shadow-gray-300/50 dark:shadow-gray-900/50"
            : "shadow-md shadow-gray-200/50 dark:shadow-gray-900/30"
        }`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="flex flex-col h-full">
          {/* 图片容器 */}
          <div className="relative w-full h-[220px] overflow-hidden">
            {/* 图片元素 - 独立放置，可以缩放 */}
            <div
              className="w-full h-full transition-transform duration-500"
              style={{
                transform: isHovered ? "scale(1.1)" : "scale(1)",
              }}
            >
              <Image
                src={imageSrc}
                alt={name}
                fill
                className="object-cover"
                quality={100}
              />
            </div>

            {/* 解决方案标签 - 放在图片容器内但独立于缩放的图片，使用绝对定位 */}
            {solution && (
              <div className="absolute bottom-4 left-4 z-10">
                <span className="px-3 py-1 text-xs font-medium bg-CardTags text-CardTags-text rounded-sm">
                  {solution}
                </span>
              </div>
            )}
          </div>

          {/* 内容区 */}
          <div className="bg-background dark:bg-gray-900 p-6 flex flex-col justify-between flex-grow">
            <div>
              <h3 className="text-lg font-bold text-gray-800 dark:text-white">
                {name}
              </h3>
            </div>

            {/* 底部区域：仅显示客户logo */}
            <div className="mt-4 flex">
              {/* 客户logo */}
              {logoSrc && (
                <div className="h-8 w-auto relative">
                  <Image
                    src={logoSrc}
                    alt="客户logo"
                    width={100}
                    height={32}
                    className="h-8 w-auto object-contain"
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default function UseCasesCustomers({
  title = "客户案例",
  customers = defaultCustomers,
  className = "",
  id = "use-cases-customers",
}: UseCasesCustomersProps) {
  return (
    <section className={`bg-white dark:bg-gray-900 ${className}`} id={id}>
      <Container>
        {/* 标题部分 */}
        <div className="text-center mb-8 sm:mb-10 md:mb-12">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 dark:text-white">
            {title}
          </h2>
        </div>

        {/* 案例卡片容器 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
          {customers.map((customer, index) => (
            <motion.div
              key={customer.id}
              className="w-full"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <CustomerCard
                id={customer.id}
                name={customer.name}
                imageSrc={customer.imageSrc}
                solution={customer.solution}
                logoSrc={customer.logoSrc}
                slug={customer.slug}
              />
            </motion.div>
          ))}
        </div>
      </Container>
    </section>
  );
}
