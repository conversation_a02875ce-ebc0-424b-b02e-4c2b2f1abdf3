"use client";

import { Check } from "lucide-react";
import Container from "../../ui/Container";
import { useState } from "react";
import QRCodeModal from "../../ui/QRCodeModal";

type PlanFeature = {
  text: string;
  included: boolean;
};

type PricingPlan = {
  name: string;
  price: string;
  description: string;
  features: PlanFeature[];
  buttonText: string;
  popular?: boolean;
};

const plans: PricingPlan[] = [
  {
    name: "基础版",
    price: "¥0",
    description: "适合小型团队和个人使用",
    features: [
      { text: "最多 3 个用户", included: true },
      { text: "基础文档管理", included: true },
      { text: "标准客户支持", included: true },
      { text: "每月 100 次 API 调用", included: true },
      { text: "高级分析功能", included: false },
      { text: "自定义集成", included: false },
    ],
    buttonText: "立即免费体验",
  },
  {
    name: "专业版",
    price: "¥499",
    description: "适合中型团队和企业",
    features: [
      { text: "最多 10 个用户", included: true },
      { text: "高级文档管理", included: true },
      { text: "优先客户支持", included: true },
      { text: "每月 1000 次 API 调用", included: true },
      { text: "高级分析功能", included: true },
      { text: "自定义集成", included: false },
    ],
    buttonText: "立即购买",
    popular: true,
  },
  {
    name: "企业版",
    price: "详询报价",
    description: "适合大型企业和组织",
    features: [
      { text: "无限用户", included: true },
      { text: "企业级文档管理", included: true },
      { text: "24/7 专属支持", included: true },
      { text: "无限 API 调用", included: true },
      { text: "高级分析功能", included: true },
      { text: "自定义集成", included: true },
    ],
    buttonText: "联系我们",
  },
];

export default function PricingPlans() {
  // 添加二维码浮层状态
  const [isQRCodeVisible, setIsQRCodeVisible] = useState(false);

  // 处理按钮点击事件
  const handleButtonClick = (planName: string) => {
    if (planName === "企业版") {
      setIsQRCodeVisible(true);
    } else {
      // 其他版本的按钮处理逻辑
      console.log(`${planName} 按钮被点击`);
    }
  };

  // 关闭二维码浮层
  const handleCloseQRCode = () => {
    setIsQRCodeVisible(false);
  };

  return (
    <>
      {/* 使用通用二维码浮层组件 */}
      <QRCodeModal isVisible={isQRCodeVisible} onClose={handleCloseQRCode} />

      <div
        className="w-full py-12 sm:py-16"
        data-pricing-section
        id="pricing-plans"
      >
        <Container>
          {/* 添加年付费提示文字 */}
          <div className="flex justify-end mb-4">
            <p className="text-sm text-gray-400 dark:text-gray-400">
              *所有版本需按年付费
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mx-auto">
            {plans.map((plan, index) => (
              <div
                key={index}
                className={`rounded-lg shadow-md p-6 sm:p-8 flex flex-col h-full bg-white dark:bg-gray-900 text-center max-w-sm mx-auto w-full`}
              >
                <div className="mb-6">
                  <h3 className="text-2xl sm:text-2xl md:text-3xl font-bold mb-2">
                    {plan.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                    {plan.description}
                  </p>
                  <div className="flex items-baseline justify-center">
                    <span className="text-4xl font-bold">{plan.price}</span>
                    {plan.name !== "企业版" && (
                      <span className="text-gray-600 dark:text-gray-400 ml-2">
                        人/月
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex-grow">
                  <ul className="space-y-3 mb-8 text-left mx-auto w-fit">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center">
                        {feature.included ? (
                          <div className="h-4 w-4 rounded-full bg-green-500 flex items-center justify-center mr-2 flex-shrink-0">
                            <Check
                              className="h-2.5 w-2.5 text-white"
                              strokeWidth={3}
                            />
                          </div>
                        ) : (
                          <div className="h-4 w-4 rounded-full border border-gray-300 dark:border-gray-700 mr-2 flex-shrink-0" />
                        )}
                        <span
                          className={
                            feature.included
                              ? "text-gray-900 dark:text-gray-100"
                              : "text-gray-500 dark:text-gray-400"
                          }
                        >
                          {feature.text}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>

                <button
                  onClick={() => handleButtonClick(plan.name)}
                  className={`w-full py-3 px-4 rounded-lg font-medium ${
                    plan.name === "企业版"
                      ? "bg-enterprise-button text-enterprise-buttonText hover:bg-enterprise-button/90"
                      : plan.name === "基础版"
                      ? "border border-gray-300 dark:border-gray-700 bg-transparent text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800"
                      : plan.popular
                      ? "bg-primary text-white hover:bg-primary/90"
                      : "bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-700"
                  } transition-colors`}
                >
                  {plan.buttonText}
                </button>
              </div>
            ))}
          </div>
        </Container>
      </div>
    </>
  );
}
