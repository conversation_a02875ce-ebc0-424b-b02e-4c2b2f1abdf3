"use client";

import { Check, Minus, ChevronDown } from "lucide-react";
import Container from "../../ui/Container";
import { useState } from "react";
import { motion } from "framer-motion";

type Feature = {
  name: string;
  description?: string;
  basic: boolean | string;
  pro: boolean | string;
  enterprise: boolean | string;
};

// 移除了所有功能的category属性
const features: Feature[] = [
  {
    name: "文档管理",
    description: "上传、组织和管理法律文档",
    basic: true,
    pro: true,
    enterprise: true,
  },
  {
    name: "AI 辅助分析",
    description: "基础的AI文档分析功能",
    basic: true,
    pro: true,
    enterprise: true,
  },
  {
    name: "法律文书生成",
    description: "自动生成常见法律文书",
    basic: "基础模板",
    pro: "高级模板",
    enterprise: "定制模板",
  },
  {
    name: "多语言支持",
    description: "支持多种语言的文档处理",
    basic: false,
    pro: true,
    enterprise: true,
  },
  {
    name: "批量处理",
    description: "同时处理多个文档",
    basic: false,
    pro: true,
    enterprise: true,
  },
  {
    name: "高级数据提取",
    description: "从复杂文档中提取关键信息",
    basic: false,
    pro: true,
    enterprise: true,
  },
  {
    name: "自定义集成",
    description: "与现有系统无缝集成",
    basic: false,
    pro: false,
    enterprise: true,
  },
  {
    name: "专属支持",
    description: "24/7专业技术支持",
    basic: false,
    pro: false,
    enterprise: true,
  },
  {
    name: "定制化开发",
    description: "根据企业需求定制功能",
    basic: false,
    pro: false,
    enterprise: true,
  },
];

// 新增服务支持数据
const supportFeatures: Feature[] = [
  {
    name: "响应时间",
    basic: "24小时",
    pro: "12小时",
    enterprise: "4小时",
  },
  {
    name: "技术支持渠道",
    basic: "邮件",
    pro: "邮件、在线",
    enterprise: "邮件、在线、电话",
  },
  {
    name: "专属客户经理",
    basic: false,
    pro: false,
    enterprise: true,
  },
  {
    name: "培训服务",
    basic: "基础指南",
    pro: "在线培训",
    enterprise: "现场培训",
  },
  {
    name: "服务等级协议(SLA)",
    basic: false,
    pro: true,
    enterprise: true,
  },
];

export default function ComparisonTable() {
  // 添加展开/收起状态，默认为展开
  const [isExpanded, setIsExpanded] = useState(true);

  // 切换展开/收起状态
  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  // 渲染带有圆形背景的图标
  const renderIcon = (included: boolean) => {
    if (included) {
      return (
        <div className="h-5 w-5 rounded-full bg-green-500 flex items-center justify-center mx-auto">
          <Check className="h-3 w-3 text-white" strokeWidth={3} />
        </div>
      );
    } else {
      return (
        <Minus className="h-4 w-4 sm:h-5 sm:w-5 text-gray-300 dark:text-gray-600 mx-auto" />
      );
    }
  };

  return (
    <div className="w-full py-8 sm:py-12 md:py-16">
      <Container>
        <div className="text-center mb-6 sm:mb-8">
          <button
            onClick={toggleExpand}
            className="inline-flex items-center gap-2 text-3xl sm:text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 hover:text-primary dark:hover:text-primary transition-colors"
          >
            <span>版本对比</span>
            <motion.div
              animate={{ rotate: isExpanded ? 180 : 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            >
              <ChevronDown className="w-5 h-5 sm:w-6 sm:h-6" />
            </motion.div>
          </button>
        </div>

        <div
          className={`overflow-hidden transition-all duration-500 ease-in-out ${
            isExpanded ? "max-h-[2000px] opacity-100" : "max-h-0 opacity-0"
          }`}
        >
          <div className="overflow-x-auto">
            {/* 功能表格 */}
            <table
              className="w-full border-collapse mb-8 sm:mb-10"
              data-features-table
            >
              <thead>
                <tr>
                  <th className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 text-left font-bold text-base sm:text-lg md:text-xl text-gray-800 dark:text-gray-200 w-1/3">
                    功能
                  </th>
                  <th className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 text-center font-bold text-base sm:text-lg md:text-xl text-gray-800 dark:text-gray-200 w-[22%]">
                    基础版
                  </th>
                  <th className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 text-center font-bold text-base sm:text-lg md:text-xl text-gray-800 dark:text-gray-200 w-[22%]">
                    专业版
                  </th>
                  <th className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 text-center font-bold text-base sm:text-lg md:text-xl text-gray-800 dark:text-gray-200 w-[22%]">
                    企业版
                  </th>
                </tr>
              </thead>
              <tbody>
                {features.map((feature, index) => (
                  <tr
                    key={`feature-${index}`}
                    className={`${
                      index % 2 === 0
                        ? "bg-gray-50 dark:bg-gray-800/50"
                        : "bg-white dark:bg-gray-900"
                    }`}
                  >
                    <td className="py-3 sm:py-4 px-2 sm:px-4 w-1/3">
                      <div className="font-medium text-sm sm:text-base text-gray-900 dark:text-gray-100">
                        {feature.name}
                      </div>
                    </td>
                    <td className="py-3 sm:py-4 px-2 sm:px-4 text-center w-[22%]">
                      {typeof feature.basic === "boolean" ? (
                        renderIcon(feature.basic)
                      ) : (
                        <span className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-300">
                          {feature.basic}
                        </span>
                      )}
                    </td>
                    <td className="py-3 sm:py-4 px-2 sm:px-4 text-center w-[22%]">
                      {typeof feature.pro === "boolean" ? (
                        renderIcon(feature.pro)
                      ) : (
                        <span className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-300">
                          {feature.pro}
                        </span>
                      )}
                    </td>
                    <td className="py-3 sm:py-4 px-2 sm:px-4 text-center w-[22%]">
                      {typeof feature.enterprise === "boolean" ? (
                        renderIcon(feature.enterprise)
                      ) : (
                        <span className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-300">
                          {feature.enterprise}
                        </span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {/* 服务支持表格 */}
            <table className="w-full border-collapse" data-support-table>
              <thead>
                <tr>
                  <th className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 text-left font-bold text-base sm:text-lg md:text-xl text-gray-800 dark:text-gray-200 w-1/3">
                    服务支持
                  </th>
                  <th className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 text-center font-bold text-base sm:text-lg md:text-xl text-gray-800 dark:text-gray-200 w-[22%]">
                    基础版
                  </th>
                  <th className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 text-center font-bold text-base sm:text-lg md:text-xl text-gray-800 dark:text-gray-200 w-[22%]">
                    专业版
                  </th>
                  <th className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 text-center font-bold text-base sm:text-lg md:text-xl text-gray-800 dark:text-gray-200 w-[22%]">
                    企业版
                  </th>
                </tr>
              </thead>
              <tbody>
                {supportFeatures.map((feature, index) => (
                  <tr
                    key={`support-${index}`}
                    className={`${
                      index % 2 === 0
                        ? "bg-gray-50 dark:bg-gray-800/50"
                        : "bg-white dark:bg-gray-900"
                    }`}
                  >
                    <td className="py-3 sm:py-4 px-2 sm:px-4 w-1/3">
                      <div className="font-medium text-sm sm:text-base text-gray-900 dark:text-gray-100">
                        {feature.name}
                      </div>
                    </td>
                    <td className="py-3 sm:py-4 px-2 sm:px-4 text-center w-[22%]">
                      {typeof feature.basic === "boolean" ? (
                        renderIcon(feature.basic)
                      ) : (
                        <span className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-300">
                          {feature.basic}
                        </span>
                      )}
                    </td>
                    <td className="py-3 sm:py-4 px-2 sm:px-4 text-center w-[22%]">
                      {typeof feature.pro === "boolean" ? (
                        renderIcon(feature.pro)
                      ) : (
                        <span className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-300">
                          {feature.pro}
                        </span>
                      )}
                    </td>
                    <td className="py-3 sm:py-4 px-2 sm:px-4 text-center w-[22%]">
                      {typeof feature.enterprise === "boolean" ? (
                        renderIcon(feature.enterprise)
                      ) : (
                        <span className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-300">
                          {feature.enterprise}
                        </span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </Container>
    </div>
  );
}
