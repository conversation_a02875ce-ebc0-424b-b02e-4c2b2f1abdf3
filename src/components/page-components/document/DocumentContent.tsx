"use client";

import React, { useState, useEffect, memo, useCallback } from "react";
import { File, ChevronRight, Folder } from "lucide-react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import rehypeSanitize from "rehype-sanitize";
import { defaultSchema } from "hast-util-sanitize";
import { motion, AnimatePresence } from "framer-motion";
import { DocMetadata, FolderContent } from "@/utils/docs";
import DocumentPagination from "./components/DocumentPagination";
import { useDocumentPreloader } from "@/hooks/useDocumentPreloader";

// 自定义 sanitize 配置，允许样式化的 HTML
const customSanitizeSchema = {
  ...defaultSchema,
  attributes: {
    ...defaultSchema.attributes,
    div: [...(defaultSchema.attributes?.div || []), "style", "className"],
    span: [...(defaultSchema.attributes?.span || []), "style", "className"],
    p: [...(defaultSchema.attributes?.p || []), "style", "className"],
    h1: [...(defaultSchema.attributes?.h1 || []), "style", "className"],
    h2: [...(defaultSchema.attributes?.h2 || []), "style", "className"],
    h3: [...(defaultSchema.attributes?.h3 || []), "style", "className"],
    h4: [...(defaultSchema.attributes?.h4 || []), "style", "className"],
    h5: [...(defaultSchema.attributes?.h5 || []), "style", "className"],
    h6: [...(defaultSchema.attributes?.h6 || []), "style", "className"],
    iframe: [
      ...(defaultSchema.attributes?.iframe || []),
      "width",
      "height",
      "src",
      "frameborder",
      "allowfullscreen",
    ],
    video: [
      ...(defaultSchema.attributes?.video || []),
      "width",
      "height",
      "controls",
    ],
    audio: [...(defaultSchema.attributes?.audio || []), "controls"],
    "*": [...(defaultSchema.attributes?.["*"] || []), "style", "className"],
  },
  tagNames: [
    ...(defaultSchema.tagNames || []),
    "div",
    "span",
    "iframe",
    "video",
    "audio",
  ],
};

// 动画配置
const fadeInOutVariants = {
  initial: { opacity: 0, y: 8 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -8 },
};

// 骨架屏专用动画配置（无位移）
const skeletonVariants = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  exit: { opacity: 0 },
};

const fadeTransition = {
  duration: 0.25,
  ease: [0.4, 0, 0.2, 1] as const,
};

// 生成标题ID的辅助函数
const generateHeadingId = (children: React.ReactNode): string => {
  return children
    ? String(children)
        .toLowerCase()
        .replace(/[：:]/g, "") // 移除中文和英文冒号
        .replace(/[^\w\s\u4e00-\u9fa5-]/g, "") // 保留中文字符、英文字母、数字、空格和连字符
        .replace(/\s+/g, "-")
        .trim()
    : "";
};

// 创建标题组件的辅助函数
const createHeadingComponent = (level: 1 | 2 | 3 | 4 | 5 | 6) => {
  return ({ node, ...props }: any) => {
    const id = generateHeadingId(props.children);
    const HeadingTag = `h${level}` as keyof JSX.IntrinsicElements;
    return <HeadingTag id={id} {...props} />;
  };
};

// 加载状态组件
const LoadingState = memo(() => {
  return (
    <motion.div
      variants={skeletonVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={fadeTransition}
      className=""
    >
      <div className="animate-pulse">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-6"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full mb-4"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full mb-4"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6 mb-6"></div>
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full mb-4"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full mb-4"></div>
      </div>
    </motion.div>
  );
});

LoadingState.displayName = "LoadingState";

// 错误状态组件
const ErrorState = memo(
  ({ error, onRetry }: { error: string; onRetry: () => void }) => {
    return (
      <motion.div
        variants={fadeInOutVariants}
        initial="initial"
        animate="animate"
        exit="exit"
        transition={fadeTransition}
        className=""
      >
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
          <h2 className="text-lg font-bold text-red-600 dark:text-red-400 mb-2">
            An error occurred while loading:
          </h2>
          <p className="text-red-600 dark:text-red-400">{error}</p>
          <button
            className="mt-3 text-sm px-3 py-1 bg-red-100 dark:bg-red-800/30 hover:bg-red-200 dark:hover:bg-red-800/50 text-red-600 dark:text-red-400 rounded"
            onClick={onRetry}
          >
            Retry
          </button>
        </div>
      </motion.div>
    );
  }
);

ErrorState.displayName = "ErrorState";

// 文件夹内容项组件
const FolderContentItem = memo(
  ({
    item,
    onClick,
  }: {
    item: { id: string; title: string; type: string; description?: string };
    onClick: (id: string) => void;
  }) => {
    return (
      <div className="p-6 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 transition-colors hover:shadow-sm">
        <div className="flex items-start">
          <div className="mt-1 mr-4">
            {item.type === "file" ? (
              <File className="w-5 h-5 text-blue-500" />
            ) : (
              <Folder className="w-5 h-5 text-blue-500" />
            )}
          </div>
          <div className="flex-1">
            <button
              className="group text-lg font-medium text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 flex items-center"
              onClick={() => onClick(item.id)}
            >
              {item.title}
              <ChevronRight className="w-4 h-4 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
            </button>
            {item.description && (
              <p className="mt-1 text-gray-600 dark:text-gray-400">
                {item.description}
              </p>
            )}
          </div>
        </div>
      </div>
    );
  }
);

FolderContentItem.displayName = "FolderContentItem";

// 文件夹内容组件
const FolderContentView = memo(
  ({
    folderContent,
    onItemClick,
    onBreadcrumbChange,
  }: {
    folderContent: FolderContent;
    onItemClick: (id: string) => void;
    onBreadcrumbChange?: (breadcrumb: BreadcrumbInfo | null) => void;
  }) => {
    // 通知父组件面包屑信息
    React.useEffect(() => {
      if (onBreadcrumbChange) {
        onBreadcrumbChange({
          path: folderContent.id,
          title: folderContent.title,
        });
      }
    }, [folderContent.id, folderContent.title, onBreadcrumbChange]);

    return (
      <motion.div
        variants={fadeInOutVariants}
        initial="initial"
        animate="animate"
        exit="exit"
        transition={fadeTransition}
        className=""
      >
        <h1 className="text-3xl font-bold mb-6">{folderContent.title}</h1>
        <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
          {folderContent.children.map((item) => (
            <FolderContentItem
              key={item.id}
              item={item}
              onClick={onItemClick}
            />
          ))}
        </div>
      </motion.div>
    );
  }
);

FolderContentView.displayName = "FolderContentView";

// 文档内容组件
const DocumentView = memo(
  ({
    document,
    onBreadcrumbChange,
    onDocSelect,
  }: {
    document: { metadata: DocMetadata; content: string };
    onBreadcrumbChange?: (breadcrumb: BreadcrumbInfo | null) => void;
    onDocSelect?: (docId: string) => void;
  }) => {
    // 为文档标题生成ID
    const titleId = generateHeadingId(document.metadata.title);

    // 通知父组件面包屑信息
    React.useEffect(() => {
      if (onBreadcrumbChange) {
        onBreadcrumbChange({
          path: document.metadata.path,
          title: document.metadata.title,
        });
      }
    }, [document.metadata.path, document.metadata.title, onBreadcrumbChange]);

    // 格式化日期，去掉时间部分
    const formatDate = (dateString: string) => {
      if (!dateString) return "";

      // 尝试解析日期
      try {
        // 如果是ISO格式的日期，则只保留日期部分
        if (dateString.includes("T")) {
          return dateString.split("T")[0];
        }
        return dateString;
      } catch (error) {
        console.error("日期格式化错误:", error);
        return dateString;
      }
    };

    return (
      <motion.div
        variants={fadeInOutVariants}
        initial="initial"
        animate="animate"
        exit="exit"
        transition={fadeTransition}
        className=""
      >
        <h1 className="text-3xl font-bold mb-2" id={titleId}>
          {document.metadata.title}
        </h1>
        {document.metadata.lastUpdated && (
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
            最后更新: {formatDate(document.metadata.lastUpdated)}
          </p>
        )}
        <div className="prose prose-blue dark:prose-invert max-w-none">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeRaw, [rehypeSanitize, customSanitizeSchema]]}
            components={{
              h1: createHeadingComponent(1),
              h2: createHeadingComponent(2),
              h3: createHeadingComponent(3),
              h4: createHeadingComponent(4),
              h5: createHeadingComponent(5),
              h6: createHeadingComponent(6),
            }}
          >
            {document.content}
          </ReactMarkdown>
        </div>

        {/* 文档分页导航 */}
        <DocumentPagination
          currentDocId={document.metadata.id}
          onDocSelect={onDocSelect}
        />
      </motion.div>
    );
  }
);

DocumentView.displayName = "DocumentView";

// 面包屑信息接口
interface BreadcrumbInfo {
  path?: string;
  title: string;
}

// 组件属性
interface DocumentContentProps {
  docId: string;
  onDocSelect?: (docId: string) => void;
  onBreadcrumbChange?: (breadcrumb: BreadcrumbInfo | null) => void;
}

const DocumentContent = memo(
  ({ docId, onDocSelect, onBreadcrumbChange }: DocumentContentProps) => {
    const [loading, setLoading] = useState(false);
    const [document, setDocument] = useState<{
      metadata: DocMetadata;
      content: string;
    } | null>(null);
    const [folderContent, setFolderContent] = useState<FolderContent | null>(
      null
    );
    const [error, setError] = useState<string | null>(null);
    // 添加正在加载的文档ID状态，用于跟踪当前加载进度
    const [pendingDocId, setPendingDocId] = useState<string | null>(null);
    // 简单的内容缓存，避免重复加载
    const [contentCache] = useState(new Map<string, any>());

    // 检查docId是否是文件夹
    const isFolderContent = docId.startsWith("folder-");
    // 如果是文件夹，提取实际的文件夹ID
    const actualFolderId = isFolderContent ? docId.replace("folder-", "") : "";

    // 处理文档项点击
    const handleDocItemClick = useCallback(
      (itemId: string) => {
        if (onDocSelect) {
          onDocSelect(itemId);
        }
      },
      [onDocSelect]
    );

    // 处理重试
    const handleRetry = useCallback(() => {
      window.location.reload();
    }, []);

    // 启用智能预加载
    const preloadStatus = useDocumentPreloader(docId, contentCache, {
      maxCacheSize: 15, // 缓存最多15个文档
      maxConcurrentRequests: 5, // 最多同时预加载2个文档
      preloadDelay: 800, // 当前文档加载完成后800ms开始预加载
      enableNetworkAwarePreload: true, // 启用网络感知预加载
    });

    // 获取文档内容 - 优化版本，避免闪烁
    useEffect(() => {
      // 如果请求的内容已经是当前显示的内容，则不需要重新加载
      if (isFolderContent && folderContent?.id === actualFolderId) {
        return;
      }
      if (!isFolderContent && document?.metadata.id === docId) {
        return;
      }

      // 检查缓存
      const cacheKey = isFolderContent ? `folder-${actualFolderId}` : docId;
      const cachedContent = contentCache.get(cacheKey);
      if (cachedContent) {
        // 使用缓存内容，立即更新
        if (isFolderContent) {
          setFolderContent(cachedContent);
          setDocument(null);
        } else {
          setDocument(cachedContent);
          setFolderContent(null);
        }
        setError(null);
        return;
      }

      // 设置正在加载的文档ID
      setPendingDocId(docId);
      setError(null);

      // 只有在没有任何内容时才立即显示加载状态
      const hasNoContent = !document && !folderContent;

      // 设置延迟显示加载状态的定时器
      const loadingTimer = setTimeout(
        () => {
          // 只有在仍在加载相同文档时才显示加载状态
          setPendingDocId((current) => {
            if (current === docId) {
              setLoading(true);
            }
            return current;
          });
        },
        hasNoContent ? 0 : 300
      ); // 无内容时立即显示，有内容时延迟显示

      // 清理定时器
      const cleanup = () => clearTimeout(loadingTimer);

      // 异步加载内容
      const loadContent = async () => {
        try {
          let data;
          if (isFolderContent) {
            // 获取文件夹内容
            const response = await fetch(`/api/docs/folder/${actualFolderId}`);
            if (!response.ok) {
              throw new Error(
                `Failed to load folder content: ${response.statusText}`
              );
            }
            data = await response.json();
          } else {
            // 获取文档内容
            const response = await fetch(`/api/docs/content/${docId}`);
            if (!response.ok) {
              // 如果是404错误，则显示文档不存在
              if (response.status === 404) {
                data = {
                  metadata: {
                    id: docId,
                    title: "Document Not Found",
                  },
                  content:
                    "Sorry, the requested document does not exist or has been removed.",
                };
              } else {
                throw new Error(
                  `Failed to load document: ${response.statusText}`
                );
              }
            } else {
              data = await response.json();
            }
          }

          // 只有在仍在加载相同文档时才更新状态
          setPendingDocId((current) => {
            if (current === docId) {
              // 缓存内容
              contentCache.set(cacheKey, data);

              // 更新内容
              if (isFolderContent) {
                setFolderContent(data);
                setDocument(null);
              } else {
                setDocument(data);
                setFolderContent(null);
              }

              setLoading(false);
              setError(null);
              return null;
            }
            return current;
          });
        } catch (err: any) {
          console.error("Error loading document:", err);
          // 只有在仍在加载相同文档时才设置错误状态
          setPendingDocId((current) => {
            if (current === docId) {
              setLoading(false);
              setError(`Failed to load document: ${err.message}`);
              return null;
            }
            return current;
          });
        } finally {
          cleanup();
        }
      };

      loadContent();
      return cleanup;
    }, [
      docId,
      isFolderContent,
      actualFolderId,
      document,
      folderContent,
      contentCache,
    ]);

    // 确定当前应该显示的内容（优化版本，避免闪烁）
    const getCurrentContent = () => {
      // 优先显示错误状态
      if (error) {
        return (
          <ErrorState
            key={`error-${docId}`}
            error={error}
            onRetry={handleRetry}
          />
        );
      }

      // 显示文件夹内容
      if (folderContent) {
        return (
          <FolderContentView
            key={`folder-${folderContent.id}`}
            folderContent={folderContent}
            onItemClick={handleDocItemClick}
            onBreadcrumbChange={onBreadcrumbChange}
          />
        );
      }

      // 显示文档内容
      if (document) {
        return (
          <DocumentView
            key={`document-${document.metadata.id}`}
            document={document}
            onBreadcrumbChange={onBreadcrumbChange}
            onDocSelect={onDocSelect}
          />
        );
      }

      // 只有在没有任何内容且正在加载时才显示骨架屏
      if (loading) {
        return <LoadingState key={`loading-${docId}`} />;
      }

      // 避免返回 null 导致的闪烁，显示一个最小的加载状态
      return <LoadingState key={`fallback-${docId}`} />;
    };

    return (
      <>
        <AnimatePresence mode="wait">{getCurrentContent()}</AnimatePresence>

        {/* 开发环境下显示预加载状态 */}
        {/* {process.env.NODE_ENV === "development" && (
          <div className="fixed bottom-4 right-4 bg-black/80 text-white text-xs p-3 rounded z-50 font-mono">
            <div className="text-green-400 font-bold mb-1">
              📄 Preload Status
            </div>
            <div>
              Cache: {preloadStatus.cacheSize}/{preloadStatus.maxCacheSize}
            </div>
            <div>Loading: {preloadStatus.activeRequests}</div>
            <div>Queue: {preloadStatus.queueSize}</div>
            <div className="text-gray-400 mt-1">Doc: {docId}</div>
            <div className="text-xs text-gray-500 mt-1">
              Check console for detailed logs
            </div>
          </div>
        )} */}
      </>
    );
  }
);

DocumentContent.displayName = "DocumentContent";

export default DocumentContent;
