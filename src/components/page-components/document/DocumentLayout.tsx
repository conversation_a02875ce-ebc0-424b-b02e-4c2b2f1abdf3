"use client";

import React, { useState, useCallback, useEffect, memo } from "react";
import { useRouter, usePathname } from "next/navigation";
// 移除了 lucide-react 图标，使用自定义 SVG
import { motion, AnimatePresence } from "framer-motion";

import DocumentTreeView from "./components/DocumentTreeView";
import DocumentContent from "./DocumentContent";
import DocumentNavigation from "./components/DocumentNavigation";
import DocumentTopBar from "./components/DocumentTopBar";
import DocumentBreadcrumb from "./components/DocumentBreadcrumb";
import "./document.css";

// 面包屑信息接口
interface BreadcrumbInfo {
  path?: string;
  title: string;
}

// 左侧边栏组件 - 悬浮布局，可调宽度
const LeftSidebar = memo(
  ({
    activeDocId,
    onDocSelect,
    isExpanded,
    width,
    onWidthChange,
    onToggle,
    isLoaded,
  }: {
    activeDocId: string;
    onDocSelect: (docId: string) => void;
    isExpanded: boolean;
    width: number;
    onWidthChange: (width: number) => void;
    onToggle: () => void;
    isLoaded: boolean;
  }) => {
    const [isResizing, setIsResizing] = useState(false);

    const handleMouseDown = (e: React.MouseEvent) => {
      e.preventDefault();
      setIsResizing(true);

      const startX = e.clientX;
      const startWidth = width;

      const handleMouseMove = (e: MouseEvent) => {
        const deltaX = e.clientX - startX;
        const newWidth = Math.max(280, Math.min(400, startWidth + deltaX)); // 最小宽度280px，最大宽度600px
        onWidthChange(newWidth);
      };

      const handleMouseUp = () => {
        setIsResizing(false);
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };

      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    };

    return (
      <>
        {/* 侧边栏容器 - 始终存在，通过宽度动画控制显示 */}
        <div className="hidden lg:block fixed top-16 left-0 h-full z-20">
          <motion.div
            animate={{
              width: isExpanded ? width : 0,
              opacity: isExpanded ? 1 : 0,
            }}
            transition={
              isLoaded
                ? {
                    type: "spring",
                    stiffness: 300, // 降低刚度，更柔和
                    damping: 30, // 适中的阻尼，平衡速度和稳定性
                    mass: 0.8, // 适中的质量
                    duration: 0.4, // 设置最大持续时间
                  }
                : {
                    duration: 0, // 初始加载时不使用动画
                  }
            }
            className="relative bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm dark:border-gray-700 overflow-hidden h-full group"
          >
            {/* 文档树内容 - 占满整个高度 */}
            <div className="h-full overflow-y-auto thin-scrollbar">
              <DocumentTreeView
                activeDocId={activeDocId}
                onDocSelect={onDocSelect}
              />
            </div>

            {/* 拖拽调整宽度的区域 - 整个高度可拖拽 */}
            <div
              className="absolute top-0 right-0 w-4 h-full cursor-col-resize group"
              onMouseDown={handleMouseDown}
            >
              {/* 背景细线条 - hover 及拖拽时显示 */}
              <div
                className={`absolute top-0 left-1/2 -translate-x-1/2 w-0.5 h-full transition-opacity duration-200 ${
                  isResizing
                    ? "bg-primary/20 opacity-100"
                    : "bg-gray-100 dark:bg-gray-600 opacity-0 group-hover:opacity-100"
                }`}
              />
            </div>
          </motion.div>
        </div>

        {/* 折叠按钮 - 独立定位，使用 fixed 避免布局抖动 */}
        <motion.div
          className="hidden lg:block fixed top-1/2 z-30 pointer-events-none"
          animate={{
            left: width - 22, // 根据侧边栏宽度动态计算位置
          }}
          transition={
            isLoaded
              ? {
                  type: "spring",
                  stiffness: 300,
                  damping: 30,
                  mass: 0.8,
                  duration: 0.4,
                }
              : {
                  duration: 0,
                }
          }
        >
          <AnimatePresence>
            {isExpanded && (
              <motion.button
                key="collapse-button"
                onClick={onToggle}
                initial={{ scale: 0, opacity: 0, x: 20 }}
                animate={{
                  scale: 1,
                  opacity: 1,
                  x: 0,
                }}
                exit={{ scale: 0, opacity: 0, x: 20 }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                transition={
                  isLoaded
                    ? {
                        type: "spring",
                        stiffness: 400,
                        damping: 25,
                        mass: 0.6,
                        delay: 0.15, // 稍微延迟出现，让展开按钮退出动画完成
                      }
                    : {
                        duration: 0,
                      }
                }
                className="flex w-6 h-14 text-gray-300 hover:text-gray-600 dark:text-gray-400 bg-white dark:bg-gray-800 rounded-md hover:bg-gray-100 items-center justify-center transition-colors duration-300 pointer-events-auto"
              >
                {/* 自定义左箭头图标 - 165度夹角 */}
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path
                    d="M10 1.5L5.5 8L10 14.5"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </motion.button>
            )}
          </AnimatePresence>
        </motion.div>

        {/* 展开按钮 - 完全独立的容器，不受侧边栏影响 */}
        <div className="hidden lg:block fixed top-1/2 left-3 h-full z-20 pointer-events-none">
          <AnimatePresence>
            {!isExpanded && (
              <motion.button
                key="expand-button"
                onClick={onToggle}
                initial={{ scale: 0, opacity: 0, x: -20 }}
                animate={{ scale: 1, opacity: 1, x: 0 }}
                exit={{ scale: 0, opacity: 0, x: -20 }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                transition={
                  isLoaded
                    ? {
                        type: "spring",
                        stiffness: 400,
                        damping: 25,
                        mass: 0.6,
                        delay: 0.15, // 与折叠按钮保持一致的延迟
                      }
                    : {
                        duration: 0,
                      }
                }
                className="w-6 h-14 text-gray-400 hover:text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/95 rounded-md hover:bg-gray-200/70 dark:hover:bg-gray-800 transition-colors pointer-events-auto"
              >
                {/* 自定义右箭头图标 - 165度夹角 */}
                <svg width="16" height="16" viewBox="0 0 8 16" fill="none">
                  <path
                    d="M6 1.5L10.5 8L6 14.5"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </motion.button>
            )}
          </AnimatePresence>
        </div>
      </>
    );
  }
);

LeftSidebar.displayName = "LeftSidebar";

// 主内容区域组件
const MainContent = memo(
  ({
    docId,
    onDocSelect,
    sidebarWidth,
    sidebarExpanded,
  }: {
    docId: string;
    onDocSelect: (docId: string) => void;
    sidebarWidth: number;
    sidebarExpanded: boolean;
  }) => {
    // 面包屑状态
    const [breadcrumbInfo, setBreadcrumbInfo] = useState<BreadcrumbInfo | null>(
      null
    );
    // 设置合理的初始值，避免页面加载时的布局动画
    // 使用函数式初始化，在客户端立即获取真实的屏幕尺寸
    const [isLgScreen, setIsLgScreen] = useState(() => {
      if (typeof window !== "undefined") {
        return window.innerWidth >= 1024;
      }
      return false; // 服务端渲染时默认为移动端，避免布局跳动
    });

    const [windowWidth, setWindowWidth] = useState(() => {
      if (typeof window !== "undefined") {
        return window.innerWidth;
      }
      return 768; // 服务端渲染时使用移动端默认宽度
    });

    const [isInitialized, setIsInitialized] = useState(false);

    // 监听屏幕尺寸变化
    useEffect(() => {
      const checkScreenSize = () => {
        const width = window.innerWidth;
        setIsLgScreen(width >= 1024);
        setWindowWidth(width);
        if (!isInitialized) {
          setIsInitialized(true);
        }
      };

      checkScreenSize();
      window.addEventListener("resize", checkScreenSize);

      return () => {
        window.removeEventListener("resize", checkScreenSize);
      };
    }, [isInitialized]);

    // 计算左边距：根据屏幕尺寸和侧边栏状态
    const leftMargin =
      windowWidth >= 2280
        ? 700 // 大于1920px：固定左边距
        : windowWidth >= 1920
        ? 420 // 大于1280px：固定左边距
        : isLgScreen && sidebarExpanded
        ? sidebarWidth + 48 // lg屏幕且侧边栏展开：侧边栏宽度 + 间距
        : windowWidth >= 1600
        ? 328 // 大于1000px：固定左边距
        : windowWidth >= 1280
        ? 140 // 大于1000px：固定左边距
        : windowWidth >= 1024
        ? 100
        : 0; // 小于1000px：无左边距

    return (
      <div className="h-full relative">
        {/* 固定的面包屑容器 */}
        <motion.div
          animate={{
            marginLeft: leftMargin,
          }}
          transition={
            isInitialized
              ? {
                  type: "spring",
                  stiffness: 300,
                  damping: 30,
                  mass: 0.8,
                  duration: 0.4,
                }
              : {
                  duration: 0,
                }
          }
          className="sticky top-0 z-10 px-8 sm:px-10 py-4 bg-white/20 dark:bg-gray-900/95 backdrop-blur-sm"
        >
          {breadcrumbInfo && (
            <DocumentBreadcrumb
              path={breadcrumbInfo.path}
              title={breadcrumbInfo.title}
            />
          )}
        </motion.div>

        {/* 可滚动的内容区域 */}
        <div className="h-[calc(100vh-64px)] overflow-y-auto thin-scrollbar document-scroll-container">
          <motion.div
            animate={{
              marginLeft: leftMargin,
            }}
            transition={
              isInitialized
                ? {
                    type: "spring",
                    stiffness: 300,
                    damping: 30,
                    mass: 0.8,
                    duration: 0.4,
                  }
                : {
                    duration: 0,
                  }
            }
            className="max-w-8xl mx-auto mr-0 md:mr-0 lg:mr-20 xl:mr-80 1.5xl:mr-[360px] 2xl:mr-[460px] 2.5xl:mr-[700px] pt-4 pb-40 px-8 sm:px-10 lg:px-10 xl:px-10 1.5xl:px-10 2xl:px-10"
          >
            <DocumentContent
              docId={docId}
              onDocSelect={onDocSelect}
              onBreadcrumbChange={setBreadcrumbInfo}
            />
          </motion.div>
        </div>
      </div>
    );
  }
);

MainContent.displayName = "MainContent";

// 右侧导航栏组件 - 悬浮布局
const RightNavigation = memo(({ docId }: { docId: string }) => {
  const navigationContainerRef = React.useRef<HTMLDivElement>(null);

  // 处理滚动穿透：将导航区域的滚动事件转发到主内容区域
  React.useEffect(() => {
    const navigationElement = navigationContainerRef.current;
    if (!navigationElement) return;

    const handleWheel = (e: WheelEvent) => {
      // 阻止默认滚动行为
      e.preventDefault();

      // 查找主内容滚动容器
      const scrollContainer = document.querySelector(
        ".document-scroll-container"
      );
      if (scrollContainer) {
        // 将滚动事件转发到主内容区域
        scrollContainer.scrollBy({
          top: e.deltaY,
          behavior: "auto", // 使用auto而不是smooth，保持原生滚动感觉
        });
      }
    };

    // 添加滚动事件监听器，使用passive: false以便可以preventDefault
    navigationElement.addEventListener("wheel", handleWheel, {
      passive: false,
    });

    return () => {
      navigationElement.removeEventListener("wheel", handleWheel);
    };
  }, []);

  return (
    <div
      ref={navigationContainerRef}
      className="hidden xl:block fixed top-16 right-4 w-[280px] 1.5xl:w-[320px] 2xl:w-[360px] 2.5xl:w-[600px]  h-[calc(100vh-5rem)] z-20 backdrop-blur-sm overflow-y-auto scrollbar-hide"
    >
      <DocumentNavigation docId={docId} />
    </div>
  );
});

RightNavigation.displayName = "RightNavigation";

// 主布局容器 - 简化为单列布局，侧边栏悬浮
const MainLayoutContainer = memo(
  ({
    docId,
    onDocSelect,
    sidebarExpanded,
    sidebarWidth,
    onSidebarWidthChange,
    onSidebarToggle,
    isLoaded,
  }: {
    docId: string;
    onDocSelect: (docId: string) => void;
    sidebarExpanded: boolean;
    sidebarWidth: number;
    onSidebarWidthChange: (width: number) => void;
    onSidebarToggle: () => void;
    isLoaded: boolean;
  }) => {
    return (
      <div className="relative w-full h-full overflow-hidden">
        {/* 悬浮的左侧边栏 */}
        <LeftSidebar
          activeDocId={docId}
          onDocSelect={onDocSelect}
          isExpanded={sidebarExpanded}
          width={sidebarWidth}
          onWidthChange={onSidebarWidthChange}
          onToggle={onSidebarToggle}
          isLoaded={isLoaded}
        />

        {/* 主内容区域 - 根据侧边栏宽度调整左边距 */}
        <MainContent
          docId={docId}
          onDocSelect={onDocSelect}
          sidebarWidth={sidebarWidth}
          sidebarExpanded={sidebarExpanded}
        />

        {/* 悬浮的右侧导航栏 */}
        <RightNavigation docId={docId} />
      </div>
    );
  }
);

MainLayoutContainer.displayName = "MainLayoutContainer";

interface DocumentLayoutProps {
  initialDocId?: string;
}

export default function DocumentLayout({ initialDocId }: DocumentLayoutProps) {
  // 当前选中的文档ID
  const [activeDocId, setActiveDocId] = useState<string>(
    initialDocId || "getting-started"
  );
  // 侧边栏展开状态 - 初始化时同步读取 localStorage
  const [sidebarExpanded, setSidebarExpanded] = useState(() => {
    if (typeof window !== "undefined") {
      try {
        const savedExpanded = localStorage.getItem("documentSidebarExpanded");
        if (savedExpanded !== null) {
          return JSON.parse(savedExpanded);
        }
      } catch (error) {
        console.warn(
          "Failed to load sidebar expanded state from localStorage:",
          error
        );
      }
    }
    return true; // 默认展开
  });

  // 侧边栏宽度状态 - 初始化时同步读取 localStorage
  const [sidebarWidth, setSidebarWidth] = useState(() => {
    if (typeof window !== "undefined") {
      try {
        const savedWidth = localStorage.getItem("documentSidebarWidth");
        if (savedWidth !== null) {
          return parseInt(savedWidth, 10);
        }
      } catch (error) {
        console.warn("Failed to load sidebar width from localStorage:", error);
      }
    }
    return 280; // 默认宽度
  });

  // 是否已从 localStorage 加载状态
  const [isLoaded, setIsLoaded] = useState(false);
  // 路由
  const router = useRouter();
  const pathname = usePathname();

  // 处理文档选择
  const handleDocSelect = useCallback(
    (docId: string) => {
      setActiveDocId(docId);

      // 如果是文件夹，不更新URL
      if (docId.startsWith("folder-")) {
        return;
      }

      // 使用 replace 而不是 push，避免页面重载，只更新 URL
      // 这样可以保持所有组件状态，包括 DocumentTreeView
      router.replace(`/document/${docId}`, { scroll: false });
    },
    [router]
  );

  // 处理侧边栏切换
  const handleSidebarToggle = useCallback(() => {
    setSidebarExpanded((prev: boolean) => {
      const newValue = !prev;
      if (isLoaded) {
        localStorage.setItem(
          "documentSidebarExpanded",
          JSON.stringify(newValue)
        );
      }
      return newValue;
    });
  }, [isLoaded]);

  // 监听 URL 变化，同步更新 activeDocId（处理浏览器前进/后退）
  useEffect(() => {
    const pathSegments = pathname.split("/");
    if (pathSegments[1] === "document" && pathSegments[2]) {
      const docIdFromUrl = pathSegments[2];
      if (docIdFromUrl !== activeDocId) {
        setActiveDocId(docIdFromUrl);
      }
    }
  }, [pathname, activeDocId]);

  // 客户端挂载后标记为已加载（状态已在初始化时同步加载）
  useEffect(() => {
    setIsLoaded(true);
  }, []);

  // 保存侧边栏宽度到 localStorage（只在加载完成后保存）
  useEffect(() => {
    if (isLoaded && typeof window !== "undefined") {
      localStorage.setItem("documentSidebarWidth", sidebarWidth.toString());
    }
  }, [sidebarWidth, isLoaded]);

  return (
    <div className="h-screen bg-backgroundDeep overflow-hidden">
      {/* 顶部导航栏 */}
      <DocumentTopBar activeDocId={activeDocId} onDocSelect={handleDocSelect} />

      {/* 主布局容器 */}
      <MainLayoutContainer
        docId={activeDocId}
        onDocSelect={handleDocSelect}
        sidebarExpanded={sidebarExpanded}
        sidebarWidth={sidebarWidth}
        onSidebarWidthChange={setSidebarWidth}
        onSidebarToggle={handleSidebarToggle}
        isLoaded={isLoaded}
      />
    </div>
  );
}
