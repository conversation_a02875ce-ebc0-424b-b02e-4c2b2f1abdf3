"use client";

import * as React from "react";
import { useEffect, useState, memo, useCallback } from "react";

// 组件属性
interface DocumentNavigationProps {
  docId: string;
}

// 导航项类型
interface NavItem {
  id: string;
  title: string;
  level: number;
}

// 生成标题ID的辅助函数，与DocumentContent.tsx中保持一致
const generateHeadingId = (text: string): string => {
  return text
    ? text
        .toLowerCase()
        .replace(/[：:]/g, "") // 移除中文和英文冒号
        .replace(/[^\w\s\u4e00-\u9fa5-]/g, "") // 保留中文字符、英文字母、数字、空格和连字符
        .replace(/\s+/g, "-")
        .trim()
    : "";
};

// 单个导航项组件
const NavItemComponent = memo(
  ({
    item,
    isActive,
    onClick,
  }: {
    item: NavItem;
    isActive: boolean;
    onClick: (id: string) => void;
  }) => {
    return (
      <a
        href={`#${item.id}`}
        className={`block py-1 text-sm ${
          isActive
            ? "text-primary/90 font-medium"
            : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
        } transition-colors duration-200`}
        style={{ paddingLeft: `${(item.level - 2) * 12}px` }}
        onClick={(e) => {
          e.preventDefault();
          onClick(item.id);
        }}
      >
        {item.title}
      </a>
    );
  }
);

NavItemComponent.displayName = "NavItemComponent";

const DocumentNavigation = memo(({ docId }: DocumentNavigationProps) => {
  const [navItems, setNavItems] = useState<NavItem[]>([]);
  const [activeItem, setActiveItem] = useState<string>("");

  // 从文档内容中提取标题
  useEffect(() => {
    const fetchDocumentContent = async () => {
      try {
        // 如果是文件夹，不获取导航
        if (docId.startsWith("folder-")) {
          setNavItems([]);
          return;
        }

        // 获取文档内容
        const response = await fetch(`/api/docs/content/${docId}`);
        if (!response.ok) {
          throw new Error(`Failed to load document: ${response.statusText}`);
        }

        const data = await response.json();
        const content = data.content;

        // 解析 Markdown 内容中的标题
        const headingRegex = /^(#{1,2})\s+(.+)$/gm;
        const extractedHeadings: NavItem[] = [];
        let match;

        while ((match = headingRegex.exec(content)) !== null) {
          const level = match[1].length;
          const title = match[2].trim();

          // 使用共享的ID生成函数
          const id = generateHeadingId(title);

          extractedHeadings.push({
            id,
            title,
            level,
          });
        }

        setNavItems(extractedHeadings);

        // 如果有标题，默认选中第一个
        if (extractedHeadings.length > 0) {
          setActiveItem(extractedHeadings[0].id);
        }
      } catch (error) {
        setNavItems([]);
      }
    };

    fetchDocumentContent();
  }, [docId]);

  // 处理导航项点击，滚动到对应的标题
  const handleItemClick = useCallback((itemId: string) => {
    setActiveItem(itemId);

    // 查找对应ID的元素并滚动到该位置
    const element = document.getElementById(itemId);
    if (element) {
      // 查找滚动容器
      const scrollContainer = document.querySelector(
        ".document-scroll-container"
      );
      if (scrollContainer) {
        // 计算元素相对于滚动容器的位置
        const containerRect = scrollContainer.getBoundingClientRect();
        const elementRect = element.getBoundingClientRect();
        const scrollTop = scrollContainer.scrollTop;
        const targetScrollTop =
          scrollTop + elementRect.top - containerRect.top - 20; // 20px 偏移量

        // 平滑滚动到目标位置
        scrollContainer.scrollTo({
          top: targetScrollTop,
          behavior: "smooth",
        });
      } else {
        // 回退到默认滚动行为
        element.scrollIntoView({ behavior: "smooth", block: "start" });
      }
    }
  }, []);

  // 监听滚动事件，更新当前激活的导航项
  useEffect(() => {
    const handleScroll = () => {
      // 如果没有导航项，不处理
      if (navItems.length === 0) return;

      // 获取滚动容器
      const scrollContainer = document.querySelector(
        ".document-scroll-container"
      );
      if (!scrollContainer) return;

      // 获取所有标题元素
      const headingElements = navItems
        .map((item) => document.getElementById(item.id))
        .filter(Boolean);

      const containerRect = scrollContainer.getBoundingClientRect();
      const scrollTop = scrollContainer.scrollTop;
      const scrollHeight = scrollContainer.scrollHeight;
      const clientHeight = scrollContainer.clientHeight;

      // 检查是否滚动到底部（允许10px的误差）
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;

      // 如果滚动到底部，直接高亮最后一个导航项
      if (isAtBottom && navItems.length > 0) {
        setActiveItem(navItems[navItems.length - 1].id);
        return;
      }

      // 找到当前视口中最靠上的标题
      let currentActiveIndex = 0;

      for (let i = 0; i < headingElements.length; i++) {
        const element = headingElements[i];
        if (element) {
          const elementRect = element.getBoundingClientRect();
          const elementTop = scrollTop + elementRect.top - containerRect.top;

          if (elementTop <= scrollTop + 120) {
            // 120px 偏移量
            currentActiveIndex = i;
          } else {
            break;
          }
        }
      }

      // 更新激活的导航项
      if (headingElements[currentActiveIndex]) {
        setActiveItem(navItems[currentActiveIndex].id);
      }
    };

    // 获取滚动容器并添加事件监听器
    const scrollContainer = document.querySelector(
      ".document-scroll-container"
    );
    if (scrollContainer) {
      scrollContainer.addEventListener("scroll", handleScroll);

      // 初始调用一次，设置初始激活项
      handleScroll();

      // 清理函数
      return () => {
        scrollContainer.removeEventListener("scroll", handleScroll);
      };
    }
  }, [navItems]);

  // 如果没有导航项，则不显示
  if (navItems.length === 0) {
    return null;
  }

  // 完整导航项组件
  return (
    <div className="py-6 px-4 h-full">
      <h3 className="py-2 text-[13px] font-bold text-gray-400 dark:text-gray-400 uppercase tracking-wider mb-2">
        catalogs
      </h3>
      <nav className="space-y-1">
        {navItems.map((item) => (
          <NavItemComponent
            key={item.id}
            item={item}
            isActive={activeItem === item.id}
            onClick={handleItemClick}
          />
        ))}
      </nav>
    </div>
  );
});

DocumentNavigation.displayName = "DocumentNavigation";

export default DocumentNavigation;
