"use client";

import React, { useState, useEffect, memo } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { motion } from "framer-motion";
import { useDocumentContext, TreeNode } from "@/contexts/DocumentContext";

// 导航项类型
interface NavItem {
  id: string;
  title: string;
  category: string;
}

// 组件属性
interface DocumentPaginationProps {
  currentDocId: string;
  onDocSelect?: (docId: string) => void;
}

// 扁平化文档树，获取所有文档的有序列表
const flattenDocumentTree = (nodes: TreeNode[]): NavItem[] => {
  const result: NavItem[] = [];

  for (const node of nodes) {
    if (node.type === "folder" && node.children) {
      // 对子文档按 order 排序
      const sortedChildren = [...node.children].sort((a, b) => {
        const orderA = (a as any).order || 999;
        const orderB = (b as any).order || 999;
        return orderA - orderB;
      });

      for (const child of sortedChildren) {
        if (child.type === "file") {
          result.push({
            id: child.id,
            title: child.title,
            category: node.title,
          });
        }
      }
    }
  }

  return result;
};

// 导航链接组件
const NavLink = memo(
  ({
    item,
    direction,
    onClick,
  }: {
    item: NavItem;
    direction: "prev" | "next";
    onClick: () => void;
  }) => {
    const isPrev = direction === "prev";

    return (
      <motion.button
        onClick={onClick}
        className={`group flex items-center text-gray-700 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors group ${
          isPrev ? "" : "flex-row-reverse"
        }`}
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.99 }}
      >
        {isPrev ? (
          <ChevronLeft className="w-4 h-4 mr-2 flex-shrink-0 text-gray-400/80 group-hover:text-gray-600 transition-colors" />
        ) : (
          <ChevronRight className="w-4 h-4 ml-2 flex-shrink-0 text-gray-400/80 group-hover:text-gray-600 transition-colors" />
        )}
        <span className="text-sm font-medium truncate">{item.title}</span>
      </motion.button>
    );
  }
);

NavLink.displayName = "NavLink";

const DocumentPagination = memo(
  ({ currentDocId, onDocSelect }: DocumentPaginationProps) => {
    const { treeData } = useDocumentContext();
    const [prevDoc, setPrevDoc] = useState<NavItem | null>(null);
    const [nextDoc, setNextDoc] = useState<NavItem | null>(null);

    useEffect(() => {
      if (
        !treeData.length ||
        !currentDocId ||
        currentDocId.startsWith("folder-")
      ) {
        setPrevDoc(null);
        setNextDoc(null);
        return;
      }

      // 扁平化文档树
      const flatDocs = flattenDocumentTree(treeData);

      // 找到当前文档的索引
      const currentIndex = flatDocs.findIndex((doc) => doc.id === currentDocId);

      if (currentIndex === -1) {
        setPrevDoc(null);
        setNextDoc(null);
        return;
      }

      // 设置上一篇和下一篇
      setPrevDoc(currentIndex > 0 ? flatDocs[currentIndex - 1] : null);
      setNextDoc(
        currentIndex < flatDocs.length - 1 ? flatDocs[currentIndex + 1] : null
      );
    }, [treeData, currentDocId]);

    // 处理导航点击
    const handleNavClick = (docId: string) => {
      if (onDocSelect) {
        onDocSelect(docId);
      }
    };

    // 如果没有上一篇和下一篇，则不显示
    if (!prevDoc && !nextDoc) {
      return null;
    }

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700"
      >
        <div className="flex justify-between items-center">
          {/* 上一篇 */}
          <div className="flex-1">
            {prevDoc && (
              <NavLink
                item={prevDoc}
                direction="prev"
                onClick={() => handleNavClick(prevDoc.id)}
              />
            )}
          </div>

          {/* 下一篇 */}
          <div className="flex-1 flex justify-end">
            {nextDoc && (
              <NavLink
                item={nextDoc}
                direction="next"
                onClick={() => handleNavClick(nextDoc.id)}
              />
            )}
          </div>
        </div>
      </motion.div>
    );
  }
);

DocumentPagination.displayName = "DocumentPagination";

export default DocumentPagination;
