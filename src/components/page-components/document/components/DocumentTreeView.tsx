"use client";

import * as React from "react";
import { useState, useCallback, useEffect, memo, useRef } from "react";
import { useDocumentContext, TreeNode } from "@/contexts/DocumentContext";

// 缓存键名
const TREE_CACHE_KEY = "document_tree_cache";

// 单个树节点组件
const TreeNodeItem = memo(
  ({
    node,
    activeDocId,
    onNodeClick,
  }: {
    node: TreeNode;
    activeDocId: string;
    onNodeClick: (node: TreeNode) => void;
  }) => {
    const isCategory = node.type === "folder";
    const isActive = node.id === activeDocId;

    return (
      <div className="mb-1">
        <div
          className={`py-2 px-4 rounded-md transition-colors duration-300 ease-in-out truncate ${
            isCategory
              ? "font-semibold text-[15px] text-gray-700 dark:text-gray-300"
              : isActive
              ? "cursor-pointer bg-primary/5 dark:bg-blue-900/20 text-sm text-primary/90 dark:text-blue-400 font-medium"
              : "cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 text-sm text-gray-700 dark:text-gray-300"
          }`}
          onClick={() => onNodeClick(node)}
        >
          {node.title}
        </div>
      </div>
    );
  }
);

TreeNodeItem.displayName = "TreeNodeItem";

// 加载状态组件
const LoadingState = memo(() => {
  return (
    <div className="py-6 px-6 h-full w-full">
      <div className="space-y-6">
        {/* 模拟分类组 */}
        {[1, 2, 3].map((groupIndex) => (
          <div key={groupIndex} className="space-y-1">
            {/* 分类标题 */}
            <div className="py-2 px-4 rounded-md">
              <div className="animate-pulse h-4 bg-gray-200 dark:bg-gray-700 rounded-md w-20"></div>
            </div>
            {/* 文档项 */}
            {[1, 2].map((docIndex) => (
              <div key={docIndex} className="py-2 px-4 rounded-md">
                <div className="animate-pulse h-4 bg-gray-200 dark:bg-gray-700 rounded-md w-3/4"></div>
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
});

LoadingState.displayName = "LoadingState";

// 错误状态组件
const ErrorState = memo(({ error }: { error: string }) => {
  return (
    <div className="py-6 px-2 h-full w-full">
      <h3 className="text-lg font-bold mb-4 px-2 truncate">文档导航</h3>
      <div className="px-2 text-red-500 dark:text-red-400">
        <p>{error}</p>
      </div>
    </div>
  );
});

ErrorState.displayName = "ErrorState";

// 组件属性
interface DocumentTreeViewProps {
  activeDocId: string;
  onDocSelect: (docId: string) => void;
}

const DocumentTreeView = memo(
  ({ activeDocId, onDocSelect }: DocumentTreeViewProps) => {
    // 使用文档上下文
    const { treeData: initialTreeData, error } = useDocumentContext();

    // 本地状态，尝试从缓存初始化数据
    const [localTreeData, setLocalTreeData] = useState<TreeNode[]>(() => {
      try {
        const cachedData = localStorage.getItem(TREE_CACHE_KEY);
        if (cachedData) {
          return JSON.parse(cachedData);
        }
      } catch (err) {
        console.error(
          "Failed to parse cached tree data during initialization:",
          err
        );
        localStorage.removeItem(TREE_CACHE_KEY);
      }
      return [];
    });

    // 检查是否有缓存数据，如果有则不显示 loading
    const [localLoading, setLocalLoading] = useState(() => {
      try {
        const cachedData = localStorage.getItem(TREE_CACHE_KEY);
        return !cachedData; // 如果有缓存数据，则不需要 loading
      } catch {
        return true;
      }
    });

    // 延迟显示骨架屏的状态
    const [showSkeleton, setShowSkeleton] = useState(false);

    // 检查是否是页面刷新
    const [isPageRefresh, setIsPageRefresh] = useState(true);

    // 容器引用，用于保持一致的高度
    const containerRef = useRef<HTMLDivElement>(null);

    // 组件初始化时的页面刷新检测和事件监听
    useEffect(() => {
      // 如果初始化时已经有数据，确保 loading 状态为 false
      if (localTreeData.length > 0) {
        setLocalLoading(false);
        setShowSkeleton(false);
      }

      // 设置一个标记表示已经加载过页面，用于页面刷新检测
      const handleBeforeUnload = () => {
        sessionStorage.setItem("page_reloaded", "true");
      };

      // 检查是否是页面刷新，并清除标记
      const checkIfRefreshed = () => {
        const wasReloaded = sessionStorage.getItem("page_reloaded") === "true";
        setIsPageRefresh(wasReloaded);
        sessionStorage.removeItem("page_reloaded");
      };

      // 页面加载时检查
      checkIfRefreshed();

      // 页面卸载前设置标记
      window.addEventListener("beforeunload", handleBeforeUnload);

      return () => {
        window.removeEventListener("beforeunload", handleBeforeUnload);
      };
    }, []);

    // 延迟显示骨架屏的逻辑
    useEffect(() => {
      let skeletonTimer: NodeJS.Timeout;

      if (localLoading && localTreeData.length === 0) {
        // 500ms 后才显示骨架屏
        skeletonTimer = setTimeout(() => {
          setShowSkeleton(true);
        }, 500);
      } else {
        setShowSkeleton(false);
      }

      return () => {
        if (skeletonTimer) {
          clearTimeout(skeletonTimer);
        }
      };
    }, [localLoading, localTreeData.length]);

    // 当初始树数据更新时，处理缓存逻辑
    useEffect(() => {
      if (initialTreeData.length > 0) {
        // 如果是页面刷新，清除缓存并使用新数据
        if (isPageRefresh) {
          localStorage.removeItem(TREE_CACHE_KEY);
          setLocalTreeData(initialTreeData);
          localStorage.setItem(TREE_CACHE_KEY, JSON.stringify(initialTreeData));
          setLocalLoading(false);
        } else {
          // 非页面刷新时，静默更新缓存，但不立即更新显示的数据
          // 这样可以避免切换文档时的闪烁
          localStorage.setItem(TREE_CACHE_KEY, JSON.stringify(initialTreeData));

          // 如果当前没有本地数据，则使用初始数据
          if (localTreeData.length === 0) {
            setLocalTreeData(initialTreeData);
          }
          setLocalLoading(false);
        }
      }
    }, [initialTreeData, isPageRefresh, localTreeData.length]);

    // 处理点击节点的事件
    const handleNodeClick = useCallback(
      (node: TreeNode) => {
        if (node.type === "file") {
          // 只处理文件类型的点击
          onDocSelect(node.id);
        }
      },
      [onDocSelect]
    );

    // 递归渲染树节点
    const renderTreeNodes = useCallback(
      (nodes: TreeNode[]) => {
        return nodes.map((node) => {
          const isCategory = node.type === "folder";

          if (isCategory) {
            // 如果是分类，将分类标题和其子项作为一个整体
            return (
              <div key={node.id} className="mb-6 last:mb-0">
                <TreeNodeItem
                  node={node}
                  activeDocId={activeDocId}
                  onNodeClick={handleNodeClick}
                />
                {node.children && (
                  <div className="space-y-1">
                    {renderTreeNodes(node.children)}
                  </div>
                )}
              </div>
            );
          } else {
            // 如果是文档项，直接渲染
            return (
              <TreeNodeItem
                key={node.id}
                node={node}
                activeDocId={activeDocId}
                onNodeClick={handleNodeClick}
              />
            );
          }
        });
      },
      [activeDocId, handleNodeClick]
    );

    // 渲染空状态
    const renderEmptyState = () => (
      <div className="py-6 px-4 text-center text-gray-500 dark:text-gray-400 w-full">
        <p>暂无文档</p>
      </div>
    );

    // 渲染内容
    const renderContent = () => {
      // 优先显示本地数据，只有在完全没有数据且正在加载时才显示骨架屏
      if (localTreeData.length > 0) {
        return (
          <div className="overflow-y-auto max-h-full w-full">
            {renderTreeNodes(localTreeData)}
          </div>
        );
      }

      // 只有在延迟时间后才显示骨架屏
      if (showSkeleton) {
        return <LoadingState />;
      }

      if (error) {
        return <ErrorState error={error} />;
      }

      return (
        <div className="overflow-y-auto max-h-[calc(100vh-250px)] w-full">
          {renderEmptyState()}
        </div>
      );
    };

    return (
      <div className="py-6 px-6 h-full w-full" ref={containerRef}>
        <div className="w-full h-[calc(100%-60px)]">{renderContent()}</div>
      </div>
    );
  }
);

DocumentTreeView.displayName = "DocumentTreeView";

export default DocumentTreeView;
