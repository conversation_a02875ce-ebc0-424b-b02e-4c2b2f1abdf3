"use client";

import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";

import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";

import DocumentTreeView from "./DocumentTreeView";
import DocumentSearchInput from "./DocumentSearchInput";

// 扁平化的搜索结果类型
interface SearchResultItem {
  id: string;
  title: string;
  type: "folder" | "file";
  path: string;
}

interface DocumentTopBarProps {
  onDocSelect?: (docId: string) => void;
  activeDocId?: string;
}

export default function DocumentTopBar({
  onDocSelect,
  activeDocId = "",
}: DocumentTopBarProps = {}) {
  const [searchQuery, setSearchQuery] = useState("");
  const [showDropdown, setShowDropdown] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResultItem[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const mobileMenuRef = useRef<HTMLDivElement>(null);

  // 搜索文档树
  const searchDocumentTree = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);

    try {
      // 调用API进行搜索
      const response = await fetch(
        `/api/docs/search?q=${encodeURIComponent(query)}`
      );

      if (!response.ok) {
        throw new Error("搜索请求失败");
      }

      const data = await response.json();

      // 转换API返回的结果为搜索结果格式
      const results: SearchResultItem[] = data.map((item: any) => ({
        id: item.id,
        title: item.title,
        type: item.type || "file",
        path: `/document/${item.id}`,
      }));

      setSearchResults(results);
    } catch (error) {
      console.error("搜索文档失败:", error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleClear = () => {
    setSearchQuery("");
    setSearchResults([]);
    setShowDropdown(false);
  };

  // 防抖定时器引用
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 适配器函数 - 为新搜索组件提供正确的接口
  const handleSearchQueryChange = (query: string) => {
    setSearchQuery(query);

    // 清除之前的定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 当输入长度大于等于1个字符时就开始搜索
    if (query.length >= 1) {
      // 使用防抖处理搜索请求
      searchTimeoutRef.current = setTimeout(() => {
        searchDocumentTree(query);
      }, 300);
    } else {
      // 输入为空时立即清空结果
      setSearchResults([]);
      setShowDropdown(false);
    }
  };

  const handleSearchSubmit = (query: string) => {
    if (query.trim()) {
      searchDocumentTree(query);
    }
  };

  // 切换移动端菜单显示状态
  const toggleMobileMenu = (e?: React.MouseEvent) => {
    // 阻止事件冒泡
    if (e) {
      e.stopPropagation();
    }

    setShowMobileMenu((prev) => !prev);
  };

  // 当搜索结果或搜索查询变化时，决定是否显示下拉菜单
  useEffect(() => {
    setShowDropdown(searchQuery.length > 0 && searchResults.length > 0);
  }, [searchResults, searchQuery]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  const handleItemClick = (_path: string, docId: string) => {
    // 如果提供了文档选择回调函数，则调用它
    if (onDocSelect && docId) {
      onDocSelect(docId);
    }

    setShowDropdown(false);
    setSearchQuery("");
    setSearchResults([]);
    setShowMobileMenu(false); // 在小屏幕上选择文档后隐藏菜单
  };

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 处理搜索下拉菜单
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="w-full h-16 border-b border-gray-100 dark:border-gray-800 bg-transparent dark:bg-gray-900 flex items-center justify-between px-4 lg:px-6 gap-2">
      {/* 左侧Logo */}
      <div className="flex items-center flex-shrink-0 min-w-0">
        <Link href="/" className="flex items-center min-w-0">
          <div className="relative lg:translate-y-[1px] flex-shrink-0">
            <Image
              src="/logo/quot.svg"
              alt="Quote Logo"
              width={16}
              height={20}
              className="mr-2"
            />
          </div>
          <span className="text-lg font-semibold text-gray-900 dark:text-white whitespace-nowrap">
            Quote 文档
          </span>
        </Link>
      </div>

      {/* 桌面端搜索框 - 始终展开可用 */}
      <div className="hidden lg:flex flex-1 justify-center max-w-2xl mx-auto">
        <DocumentSearchInput
          searchQuery={searchQuery}
          onSearchChange={handleSearchQueryChange}
          onSearch={handleSearchSubmit}
          onClear={handleClear}
          onItemClick={handleItemClick}
          searchResults={searchResults}
          isSearching={isSearching}
          showDropdown={showDropdown}
          placeholder="搜索文档..."
          className="w-full max-w-lg"
          isMobile={false}
        />
      </div>

      {/* 移动端操作区域 - 搜索框和菜单按钮 */}
      <div className="lg:hidden flex items-center gap-2 flex-shrink-0">
        {/* 移动端搜索框 - 默认折叠，点击展开 */}
        <div className="h-10 flex-shrink-0">
          <DocumentSearchInput
            searchQuery={searchQuery}
            onSearchChange={handleSearchQueryChange}
            onSearch={handleSearchSubmit}
            onClear={handleClear}
            onItemClick={handleItemClick}
            searchResults={searchResults}
            isSearching={isSearching}
            showDropdown={showDropdown}
            placeholder="搜索文档..."
            className=""
            isMobile={true}
          />
        </div>

        {/* 移动端菜单按钮 - 点击展开文档导航 */}
        <button
          type="button"
          onClick={(e) => toggleMobileMenu(e)}
          className="p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-200 dark:focus:ring-gray-700"
          aria-expanded={showMobileMenu}
          aria-label="展开文档导航菜单"
        >
          <span className="sr-only">Open navigation menu</span>
          {/* 汉堡菜单 / X 图标 */}
          <div className="relative w-5 h-5 flex items-center justify-center">
            <span
              className={`absolute block h-0.5 w-4 bg-current transform transition duration-300 ease-in-out rounded-full ${
                showMobileMenu
                  ? "text-foreground rotate-45"
                  : "text-foreground/60 -translate-y-1"
              }`}
            ></span>
            {showMobileMenu && (
              <span
                className={`absolute block h-0.5 w-4 bg-current transition duration-300 ease-in-out text-foreground opacity-0 rounded-full`}
              ></span>
            )}
            <span
              className={`absolute block h-0.5 w-4 bg-current transform transition duration-300 ease-in-out rounded-full ${
                showMobileMenu
                  ? "text-foreground -rotate-45"
                  : "text-foreground/60 translate-y-1"
              }`}
            ></span>
          </div>
        </button>
      </div>

      {/* 移动端侧边栏 - 从左侧滑出 */}
      <AnimatePresence>
        {showMobileMenu && (
          <>
            {/* 背景遮罩层 - 高斯模糊+半透明深色 */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
              onClick={() => setShowMobileMenu(false)}
            />

            {/* 侧边栏容器 */}
            <motion.div
              ref={mobileMenuRef}
              initial={{ x: "-100%" }}
              animate={{ x: 0 }}
              exit={{ x: "-100%" }}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 30,
                mass: 0.8,
              }}
              className="fixed top-0 left-0 h-full w-80 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm shadow-xl z-50 lg:hidden overflow-y-auto thin-scrollbar"
              onClick={(e) => e.stopPropagation()}
            >
              {/* 侧边栏内容 - 直接从顶部开始 */}
              {onDocSelect && (
                <DocumentTreeView
                  activeDocId={activeDocId}
                  onDocSelect={(docId) => {
                    handleItemClick(`/document/${docId}`, docId);
                  }}
                />
              )}
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
}
