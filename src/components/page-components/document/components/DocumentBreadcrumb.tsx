"use client";

import React from "react";

interface DocumentBreadcrumbProps {
  path?: string;
  title: string;
}

/**
 * 文档面包屑导航组件
 * 显示文档的路径层次结构，但不可点击
 */
const DocumentBreadcrumb: React.FC<DocumentBreadcrumbProps> = ({
  path,
  title,
}) => {
  // 如果没有路径，只显示标题
  if (!path) {
    return (
      <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
        <span>{title}</span>
      </div>
    );
  }

  // 处理路径，分割成数组
  const pathSegments = path.split("/").filter(Boolean);

  // 如果路径为空，只显示标题
  if (pathSegments.length === 0) {
    return (
      <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
        <span>{title}</span>
      </div>
    );
  }

  return (
    <div className="flex flex-wrap items-center text-sm text-gray-600 dark:text-gray-300">
      {pathSegments.map((segment, index) => {
        // 将路径片段转换为更友好的显示形式
        const displayName = segment
          .replace(/-/g, " ")
          .replace(/\b\w/g, (char) => char.toUpperCase());

        return (
          <React.Fragment key={index}>
            <span className="capitalize text-gray-400 dark:text-gray-500">
              {displayName}
            </span>
            <span className="mx-2 text-gray-300 dark:text-gray-600">/</span>
          </React.Fragment>
        );
      })}

      {/* 当前文档 */}
      <span>{title}</span>
    </div>
  );
};

export default DocumentBreadcrumb;
