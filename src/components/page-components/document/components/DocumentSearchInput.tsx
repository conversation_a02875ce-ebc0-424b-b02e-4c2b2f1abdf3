"use client";

import React, { useState, useRef, useEffect } from "react";
import { Search, X, FileText, ArrowRight, Folder } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

// 搜索结果类型
interface SearchResultItem {
  id: string;
  title: string;
  type: "folder" | "file";
  path: string;
}

export interface DocumentSearchInputProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onSearch: (query: string) => void;
  onClear: () => void;
  onItemClick: (path: string, docId: string) => void;
  searchResults: SearchResultItem[];
  isSearching: boolean;
  showDropdown: boolean;
  placeholder?: string;
  className?: string;
  isMobile?: boolean;
}

/**
 * 文档搜索输入组件
 * 桌面端：始终展开可用
 * 移动端：默认折叠，点击图标后动态展开
 */
const DocumentSearchInput: React.FC<DocumentSearchInputProps> = ({
  searchQuery = "",
  onSearchChange,
  onSearch,
  onClear,
  onItemClick,
  searchResults = [],
  isSearching = false,
  showDropdown = false,
  placeholder = "搜索文档...",
  className = "",
  isMobile = false,
}) => {
  const [isActive, setIsActive] = useState(!isMobile); // 桌面端默认激活，移动端默认折叠
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchChange(e.target.value);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      onSearch(searchQuery);
    }
  };

  const handleActivate = () => {
    if (isMobile) {
      setIsActive(true);
      // 使用setTimeout确保在动画开始后再聚焦
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  };

  const handleBlur = () => {
    if (isMobile && searchQuery === "" && !showDropdown) {
      setIsActive(false);
    }
  };

  const handleClear = () => {
    onClear();
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const handleItemSelect = (result: SearchResultItem) => {
    onItemClick(result.path, result.id);
    if (isMobile) {
      setIsActive(false);
    }
  };

  // 点击外部区域时关闭搜索框（仅移动端）
  useEffect(() => {
    if (!isMobile) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        searchQuery === "" &&
        !showDropdown
      ) {
        setIsActive(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [searchQuery, showDropdown, isMobile]);

  // 桌面端样式
  if (!isMobile) {
    return (
      <div className={`relative ${className}`}>
        <form onSubmit={handleSubmit} className="w-full max-w-lg relative">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            </div>
            <input
              ref={inputRef}
              type="text"
              className="block w-full p-2 pl-10 pr-10 text-sm text-gray-900 border border-gray-50 rounded-lg bg-gray-50 focus:bg-gray-100/80 focus:border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 transition-colors duration-200"
              placeholder={placeholder}
              value={searchQuery}
              onChange={handleSearchChange}
              required
            />
            {searchQuery && (
              <button
                type="button"
                className="absolute inset-y-0 right-0 flex items-center pr-3"
                onClick={handleClear}
              >
                <X className="w-4 h-4 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300" />
              </button>
            )}
          </div>

          {/* 桌面端搜索结果下拉菜单 */}
          {showDropdown && (
            <div
              ref={dropdownRef}
              className="absolute z-20 mt-1 w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg overflow-hidden"
            >
              <div className="p-2 text-xs text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
                {isSearching
                  ? "正在搜索..."
                  : `搜索结果 (${searchResults.length})`}
              </div>
              {searchResults.length > 0 ? (
                <ul>
                  {searchResults.map((result) => (
                    <li key={result.id}>
                      <button
                        type="button"
                        className="flex w-full items-center px-4 py-3 text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        onClick={() => handleItemSelect(result)}
                      >
                        {result.type === "folder" ? (
                          <Folder className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2 flex-shrink-0" />
                        ) : (
                          <FileText className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2 flex-shrink-0" />
                        )}
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {result.title}
                          </p>
                        </div>
                        <ArrowRight className="w-4 h-4 text-gray-400 dark:text-gray-500 flex-shrink-0 ml-2" />
                      </button>
                    </li>
                  ))}
                </ul>
              ) : isSearching ? (
                <div className="p-4 text-center">
                  <div className="inline-block animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-gray-600 dark:border-gray-700 dark:border-t-gray-300"></div>
                  <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                    搜索中...
                  </p>
                </div>
              ) : (
                <div className="p-4 text-center text-sm text-gray-500 dark:text-gray-400">
                  未找到相关文档
                </div>
              )}
            </div>
          )}
        </form>
      </div>
    );
  }

  // 移动端样式
  return (
    <div ref={containerRef} className={`relative h-full ${className}`}>
      <motion.div
        className={`h-full rounded-lg border border-transparent ${
          isActive
            ? "bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-600"
            : "hover:bg-gray-100 dark:hover:bg-gray-800"
        } transition-colors duration-300 flex items-center overflow-hidden relative`}
        animate={{
          width: isActive ? "14rem" : "2.5rem",
        }}
        initial={false}
        transition={{
          type: "spring",
          stiffness: 500,
          damping: 40,
        }}
      >
        {/* 搜索图标固定在左侧 */}
        <div
          className="absolute left-0 h-full w-10 flex items-center justify-center text-gray-600 dark:text-gray-400 cursor-pointer transition-opacity duration-300 ease-in-out z-10"
          onClick={handleActivate}
        >
          <Search className="w-5 h-5" />
        </div>

        <AnimatePresence>
          {isActive && (
            <motion.div
              className="w-full h-full flex items-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              <form
                onSubmit={handleSubmit}
                className="w-full h-full flex items-center"
              >
                <input
                  ref={inputRef}
                  type="text"
                  value={searchQuery}
                  onChange={handleSearchChange}
                  onBlur={handleBlur}
                  placeholder={placeholder}
                  className="w-full h-full pl-10 pr-10 bg-transparent text-sm text-gray-900 dark:text-white outline-none border-none placeholder:text-gray-500 dark:placeholder:text-gray-400"
                  autoFocus
                />

                {/* 清空按钮 */}
                <AnimatePresence>
                  {searchQuery && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      transition={{ duration: 0.15 }}
                      className="absolute right-2 h-full flex items-center justify-center"
                    >
                      <button
                        type="button"
                        onClick={handleClear}
                        className="w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                      >
                        <X className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                      </button>
                    </motion.div>
                  )}
                </AnimatePresence>
              </form>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* 移动端搜索结果下拉菜单 */}
      {showDropdown && isActive && (
        <div
          ref={dropdownRef}
          className="absolute z-20 left-0 right-0 mt-1 mx-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg overflow-hidden max-h-[60vh] overflow-y-auto"
        >
          <div className="p-2 text-xs text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
            {isSearching ? "正在搜索..." : `搜索结果 (${searchResults.length})`}
          </div>
          {searchResults.length > 0 ? (
            <ul>
              {searchResults.map((result) => (
                <li key={result.id}>
                  <button
                    type="button"
                    className="flex w-full items-center px-4 py-3 text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    onClick={() => handleItemSelect(result)}
                  >
                    {result.type === "folder" ? (
                      <Folder className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2 flex-shrink-0" />
                    ) : (
                      <FileText className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2 flex-shrink-0" />
                    )}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {result.title}
                      </p>
                    </div>
                    <ArrowRight className="w-4 h-4 text-gray-400 dark:text-gray-500 flex-shrink-0 ml-2" />
                  </button>
                </li>
              ))}
            </ul>
          ) : isSearching ? (
            <div className="p-4 text-center">
              <div className="inline-block animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-gray-600 dark:border-gray-700 dark:border-t-gray-300"></div>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                搜索中...
              </p>
            </div>
          ) : (
            <div className="p-4 text-center text-sm text-gray-500 dark:text-gray-400">
              未找到相关文档
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DocumentSearchInput;
