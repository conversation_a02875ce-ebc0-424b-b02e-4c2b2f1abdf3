/* 防止整个页面滚动 */
html,
body {
  overflow: hidden;
  height: 100%;
  position: fixed;
  width: 100%;
}

/* 自定义滚动条样式 */
.thin-scrollbar::-webkit-scrollbar {
  width: 4px;
}
.thin-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}
.thin-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}
.thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
.dark .thin-scrollbar::-webkit-scrollbar-thumb {
  background: #475569;
}
.dark .thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* 防止移动端橡皮筋效果 */
.document-scroll-container {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}
