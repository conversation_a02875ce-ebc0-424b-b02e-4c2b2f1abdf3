import React, { useState, useRef } from "react";
import { But<PERSON> } from "@heroui/react";
import { Tabs, ITab } from "./ui/Tabs";
import { PhoneInput, PhoneInputRef } from "./ui/PhoneInput";
import * as EmailValidator from "email-validator";
import FormCard from "./ui/FormCard";
import Image from "next/image";
import { AuthClient } from "@quote/auth-client";

// 创建认证客户端实例
const authClient = new AuthClient({
  authServiceApiUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL!,
  authServiceRedirectUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL!,
  timeout: 10000,
});

interface LoginFormProps {
  onSuccess: (data: { type: "email" | "phone"; value: string }) => void;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSuccess }) => {
  const [loginType, setLoginType] = useState<"email" | "phone">("phone");
  const [value, setValue] = useState("");
  const [error, setError] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const phoneInputRef = useRef<PhoneInputRef>(null);

  const validateInput = () => {
    if (!value.trim()) {
      setError(
        `Please enter your ${loginType === "email" ? "email" : "phone number"}`
      );
      return false;
    }

    if (loginType === "email") {
      // 使用email-validator库验证邮箱格式
      if (!EmailValidator.validate(value)) {
        setError("Please enter a valid email address");
        return false;
      }
    } else {
      // 使用PhoneInput的验证功能
      const isPhoneValid = phoneInputRef.current?.validate();
      if (!isPhoneValid) {
        // 设置手机号格式错误提示
        setError("Please enter a valid phone number");
        return false;
      }
    }

    setError("");
    return true;
  };

  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitted(true);

    if (validateInput()) {
      try {
        // 设置加载状态
        setIsLoading(true);
        setError("");

        // 设置请求超时时间（15秒）
        const timeout = 15000;

        // 创建一个可以被超时中断的Promise
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => {
            reject(
              new Error("Request timeout, please check your network connection")
            );
          }, timeout);
        });

        // 使用Promise.race竞争，哪个先完成就返回哪个结果
        await Promise.race([
          authClient.apiSendVerificationCode(value),
          timeoutPromise,
        ]);

        // 发送验证码成功，通知父组件
        onSuccess({ type: loginType, value });
      } catch (error) {
        // 处理API错误
        if (
          error instanceof Error &&
          error.message ===
            "Request timeout, please check your network connection"
        ) {
          setError(error.message);
        } else if (error && typeof error === "object" && "status" in error) {
          // 根据API错误状态码显示固定的错误提示
          const apiError = error as { status?: number };
          switch (apiError.status) {
            case 400:
              setError(
                "Invalid parameters, please check your contact information"
              );
              break;
            case 500:
              setError("System busy, please try again later");
              break;
            default:
              setError("Failed to send code, please try again later");
          }
        } else {
          setError("Network error, please try again later");
        }
      } finally {
        setIsLoading(false);
      }
    }
  };

  // 切换登录类型时重置错误提示和提交状态
  const handleTypeChange = (type: "email" | "phone") => {
    setLoginType(type);
    setValue("");
    setError("");
    setIsSubmitted(false);
  };

  // 处理输入变化，如果已经提交过表单，则实时验证
  const handleInputChange = (newValue: string) => {
    setValue(newValue);

    // 当输入框为空时，清除错误提示
    if (newValue.trim() === "") {
      setError("");
      return;
    }

    // 只有在已提交状态下才进行实时验证
    if (isSubmitted) {
      setTimeout(() => {
        if (loginType === "email" && !EmailValidator.validate(newValue)) {
          setError("请输入有效的邮箱地址");
        } else {
          setError("");
        }
      }, 0);
    }
  };

  // 处理手机号输入变化
  const handlePhoneChange = (newValue: string | undefined) => {
    const phoneValue = newValue?.toString() || "";
    setValue(phoneValue);

    // 当输入框为空时，清除错误提示
    if (phoneValue.trim() === "") {
      setError("");
    }
  };

  // 标签页配置
  const tabs: ITab[] = [
    { title: "Phone", value: "phone" },
    { title: "Email", value: "email" },
  ];

  return (
    <FormCard>
      <div className="mb-6">
        <div className="mb-4 relative h-14 w-14">
          <Image
            src="/logo/logo-icon.png"
            alt="Quote Logo"
            fill
            style={{ objectFit: "contain" }}
            priority
          />
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Welcome to Quote</h2>
      </div>

      <div className="mb-6">
        <Tabs
          selected={loginType}
          setSelected={(value) => handleTypeChange(value as "email" | "phone")}
          tabs={tabs}
          variant="primary"
        />
      </div>

      <form onSubmit={handleSubmit} className="space-y-4" noValidate>
        <div className="flex flex-col">
          {loginType === "email" ? (
            <>
              <div className="relative">
                <input
                  id="email-input"
                  className={`rounded-lg border ${
                    error ? "border-red-500" : "border-gray-300"
                  } px-3 py-3 h-[46px] focus:border-primary focus:outline-none w-full bg-white text-gray-900 pr-10
                  [&:-webkit-autofill]:bg-white [&:-webkit-autofill]:shadow-[0_0_0_30px_white_inset]`}
                  type="text"
                  placeholder="<EMAIL>"
                  value={value}
                  onChange={(e) => handleInputChange(e.target.value)}
                  autoComplete="email"
                />
                {value && (
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-500 focus:outline-none"
                    onClick={() => {
                      handleInputChange("");
                      // 聚焦回输入框
                      document.getElementById("email-input")?.focus();
                    }}
                    aria-label="Clear input"
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </button>
                )}
              </div>
              {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
            </>
          ) : (
            <>
              <style jsx>{`
                /* 移除手机号输入框自动填充背景色 */
                input:-webkit-autofill,
                input:-webkit-autofill:hover,
                input:-webkit-autofill:focus,
                input:-webkit-autofill:active {
                  -webkit-box-shadow: 0 0 0 30px white inset !important;
                  -webkit-text-fill-color: #111827 !important;
                }
              `}</style>
              <div className="w-full">
                <PhoneInput
                  ref={phoneInputRef}
                  international
                  defaultCountry="CN"
                  value={value}
                  onChange={handlePhoneChange}
                />
              </div>
              {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
            </>
          )}
        </div>

        <Button
          color="primary"
          size="lg"
          className="w-full mt-4 text-white bg-[#443AA7] hover:bg-[#443AA7]/90"
          type="submit"
          radius="sm"
          isDisabled={isLoading}
        >
          {isLoading ? "Loading..." : "Next"}
        </Button>
      </form>

      <div className="mt-4 text-center text-sm text-gray-300">
        First-time login will automatically create an account
      </div>
    </FormCard>
  );
};

export default LoginForm;
