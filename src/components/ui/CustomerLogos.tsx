import Container from "./Container";

interface CustomerLogosProps {
  title?: string;
  className?: string;
}

export default function CustomerLogos({
  title = "更多客户选择使用Quote",
  className = "",
}: CustomerLogosProps) {
  return (
    <div className="bg-white dark:bg-gray-900 py-12 sm:py-16">
      <Container>
        {/* 标题部分 */}
        <div className="text-center mb-8">
          <h2 className="text-3xl sm:text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100">
            {title}
          </h2>
        </div>

        {/* Logo墙部分 */}
        <div
          className={`flex flex-wrap justify-center gap-x-8 gap-y-8 md:gap-x-12 md:gap-y-10 ${className}`}
        >
          {/* 使用10个logo占位符 */}
          {Array.from({ length: 10 }).map((_, i) => (
            <div
              key={i}
              className="flex items-center justify-center h-12 w-28 sm:h-16 sm:w-36 bg-gray-100 dark:bg-gray-800 rounded-lg"
            >
              <div className="h-8 w-20 bg-gray-300 dark:bg-gray-700 rounded opacity-70"></div>
            </div>
          ))}
        </div>
      </Container>
    </div>
  );
}
