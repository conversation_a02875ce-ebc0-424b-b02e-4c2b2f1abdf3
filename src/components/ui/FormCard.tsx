import React from "react";

interface FormCardProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * 通用卡片容器组件，用于包裹登录、验证等表单
 */
const FormCard: React.FC<FormCardProps> = ({ children, className = "" }) => {
  return (
    <div className="w-full max-w-md">
      <div className={`p-6 min-h-[380px] flex flex-col ${className}`}>
        {children}
      </div>
    </div>
  );
};

export default FormCard;
