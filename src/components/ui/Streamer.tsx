"use client";

import React, { useEffect, useState } from "react";
import Container from "./Container";
import { ArrowRight } from "lucide-react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";

interface StreamerProps {
  title?: string;
  subtitle?: string;
  buttonText?: string;
  className?: string;
  id?: string;
  onButtonClick?: () => void;
}

export default function Streamer({
  title = "轻松上手 简单好用",
  subtitle = "AI赋能法律，解决复杂繁琐问题，提高工作效率，让法律人的时间更有价值",
  buttonText = "Try Beta",
  className = "",
  id = "streamer",
  onButtonClick,
}: StreamerProps) {
  // 添加路由
  const router = useRouter();

  // 添加小屏幕检测状态
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  // 监听屏幕尺寸变化
  useEffect(() => {
    // 初始化小屏幕状态
    setIsSmallScreen(window.innerWidth < 768);

    const handleResize = () => {
      setIsSmallScreen(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize, { passive: true });

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  // 处理按钮点击
  const handleButtonClick = () => {
    if (onButtonClick) {
      onButtonClick();
    } else {
      // 默认跳转到注册页面
      router.push("/register");
    }
  };

  return (
    <section
      className={`py-12 sm:py-16 md:py-20 ${className}`}
      id={id}
      style={{
        background: "linear-gradient(to bottom, #F6F6FF, #EEEDFF)",
      }}
    >
      <Container>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="flex flex-col items-center text-center"
        >
          {/* 主标题 */}
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 text-gray-800 dark:text-white">
            {title}
          </h2>

          {/* 副标题 */}
          <p className="text-base sm:text-lg md:text-xl mb-6 sm:mb-8 md:mb-10 font-medium text-foreground/70 mx-auto max-w-3xl leading-relaxed">
            {subtitle}
          </p>

          {/* 按钮 - 参考Hero.tsx的样式 */}
          <button
            onClick={handleButtonClick}
            className={`${
              isSmallScreen
                ? "px-6 py-3 text-base gap-2"
                : "px-10 py-4 text-xl gap-3"
            } bg-primary text-white rounded-lg hover:bg-opacity-90 transition font-bold flex items-center relative overflow-hidden before:absolute before:w-[0.4rem] before:h-[20rem] before:top-0 before:translate-x-[-8rem] hover:before:translate-x-[20rem] before:duration-[0.8s] before:-skew-x-[10deg] before:transition-all before:bg-white before:blur-[8px] before:opacity-70 shadow-lg shadow-primary/30 hover:shadow-xl hover:shadow-primary/40`}
          >
            {buttonText}
            <ArrowRight
              className={`${isSmallScreen ? "w-4 h-4" : "w-5 h-5"}`}
            />
          </button>
        </motion.div>
      </Container>
    </section>
  );
}
