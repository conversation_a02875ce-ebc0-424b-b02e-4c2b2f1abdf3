import React from "react";
import Image from "next/image";
import { motion } from "framer-motion";

type TTabVariant = "primary" | "secondary";

export interface ITab {
  title?: string;
  value: string;
  disabled?: boolean;
  icon?: string;
  tooltip?: string;
}

interface TabsProps {
  selected: string;
  setSelected: React.Dispatch<React.SetStateAction<string>>;
  tabs: ITab[];
  disabled?: boolean;
  variant?: TTabVariant;
}

interface TabProps extends ITab {
  selected: string;
  setSelected: React.Dispatch<React.SetStateAction<string>>;
  variant: TTabVariant;
}

const Tab = ({
  selected,
  setSelected,
  title,
  value,
  disabled = false,
  icon,
  variant,
}: TabProps) => {
  if (!title && !icon) {
    return null;
  }

  const isSelected = selected === value;

  return (
    <div
      className={`relative overflow-visible box-border font-sans text-sm flex gap-0.5 duration-100 ${
        disabled ? "cursor-not-allowed" : "cursor-pointer"
      } ${variant === "primary" ? "pb-[5px] hover:text-gray-900" : ""} ${
        variant === "secondary"
          ? "h-6 rounded-md text-[13px] px-1.5 items-center"
          : ""
      } ${
        disabled
          ? isSelected
            ? "text-gray-900"
            : "text-gray-400"
          : variant === "primary"
          ? isSelected
            ? "text-gray-900"
            : "text-gray-500"
          : isSelected
          ? "text-white"
          : "text-gray-900"
      } ${variant === "secondary" && isSelected ? "bg-gray-900" : ""} ${
        variant === "secondary" && !isSelected && disabled ? "bg-gray-200" : ""
      } ${
        variant === "secondary" && !isSelected && !disabled ? "bg-gray-100" : ""
      }`}
      onClick={() => {
        if (!disabled) {
          setSelected(value);
        }
      }}
    >
      {icon && <Image src={icon} alt={title || ""} width={16} height={16} />}
      <div>{title}</div>

      {variant === "primary" && isSelected && (
        <motion.div
          className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#443AA7]"
          layoutId="underline"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{
            type: "spring",
            stiffness: 400,
            damping: 35,
            mass: 1,
          }}
        />
      )}
    </div>
  );
};

export const Tabs = ({
  selected,
  setSelected,
  tabs,
  disabled = false,
  variant = "primary",
}: TabsProps) => {
  return (
    <div
      className={`flex${disabled ? " cursor-not-allowed" : ""} ${
        variant === "primary" ? "gap-6" : "gap-2"
      }`}
    >
      {tabs.map((tab) => (
        <Tab
          key={tab.value}
          selected={selected}
          setSelected={setSelected}
          disabled={disabled || tab.disabled}
          variant={variant}
          {...tab}
        />
      ))}
    </div>
  );
};
