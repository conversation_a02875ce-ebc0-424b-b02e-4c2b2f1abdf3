import React from "react";
import LoadingSpinner from "./LoadingSpinner";

type FullScreenLoadingProps = {
  text?: string;
  spinnerSize?: number;
  className?: string;
  bgClassName?: string;
};

/**
 * 全屏覆盖的加载状态组件
 */
const FullScreenLoading: React.FC<FullScreenLoadingProps> = ({
  text = "",
  spinnerSize = 24,
  className = "",
}) => {
  return (
    <div
      className={`fixed inset-0 z-50 flex flex-col items-center justify-center bg-backgroundDeep/80 backdrop-blur-md ${className}`}
    >
      <LoadingSpinner size={spinnerSize} text={text} />
    </div>
  );
};

export default FullScreenLoading;
