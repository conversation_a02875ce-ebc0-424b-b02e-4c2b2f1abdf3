"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { MessageSquareMore, X } from "lucide-react";

interface FloatingConsultButtonProps {
  className?: string;
}

export default function FloatingConsultButton({
  className = "",
}: FloatingConsultButtonProps) {
  // 添加悬停状态和点击状态
  const [isHovered, setIsHovered] = useState(false);
  const [isFullscreenQROpen, setIsFullscreenQROpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 检测设备类型
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // 初始检查
    checkIsMobile();

    // 监听窗口大小变化
    window.addEventListener("resize", checkIsMobile);

    return () => {
      window.removeEventListener("resize", checkIsMobile);
    };
  }, []);

  // 处理点击事件
  const handleConsultClick = () => {
    if (isMobile) {
      setIsFullscreenQROpen(true);
    } else {
      console.log("购买咨询按钮被点击");
      // 示例：window.open("https://example.com/consult", "_blank");
    }
  };

  // 关闭全屏二维码
  const handleCloseFullscreenQR = () => {
    setIsFullscreenQROpen(false);
  };

  return (
    <>
      {/* 全屏二维码浮层 */}
      <AnimatePresence>
        {isFullscreenQROpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black/70 z-[100] flex items-center justify-center"
            onClick={handleCloseFullscreenQR}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-white dark:bg-gray-800 p-6 rounded-xl max-w-xs w-full mx-4 relative"
              onClick={(e) => e.stopPropagation()} // 防止点击内容区域时关闭
            >
              {/* 关闭按钮 - 移到浮层外部右上角 */}
              <button
                onClick={handleCloseFullscreenQR}
                className="absolute -top-12 right-0 text-white hover:text-gray-200 bg-black/30 hover:bg-black/50 p-2 rounded-full transition-all"
                aria-label="关闭"
              >
                <X className="w-6 h-6" />
              </button>

              {/* 二维码（灰色方块代替） */}
              <div className="w-full aspect-square bg-gray-300 dark:bg-gray-600 rounded-md mb-4"></div>

              {/* 二维码说明文字 */}
              <p className="text-center text-sm text-gray-700 dark:text-gray-300 font-medium">
                扫码咨询
              </p>
              <p className="text-center text-xs text-gray-500 dark:text-gray-400 mt-1">
                微信扫一扫，立即咨询客服
              </p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 悬浮按钮 */}
      <div className="relative">
        <motion.div
          initial={{ opacity: 0, x: 100 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          className={`fixed right-4 top-2/3 -translate-y-1/2 z-50 ${className} flex items-center`}
          onMouseEnter={() => !isMobile && setIsHovered(true)}
          onMouseLeave={() => !isMobile && setIsHovered(false)}
        >
          {/* 桌面端二维码悬浮浮层 */}
          <AnimatePresence>
            {isHovered && !isMobile && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="absolute right-[calc(100%+12px)] bg-white dark:bg-gray-800 p-3 rounded-lg shadow-xl"
              >
                {/* 二维码（灰色方块代替） */}
                <div className="w-32 h-32 bg-gray-300 dark:bg-gray-600 rounded-md"></div>

                {/* 二维码说明文字 */}
                <p className="text-center text-xs mt-2 text-gray-600 dark:text-gray-300 font-medium">
                  扫码咨询
                </p>
              </motion.div>
            )}
          </AnimatePresence>

          <button
            onClick={handleConsultClick}
            className="flex flex-col items-center justify-center bg-gradient-to-b from-consultButton-gradientStart to-consultButton-gradientEnd text-white px-3 py-6 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
            aria-label="购买咨询"
          >
            <MessageSquareMore className="w-5 h-5 mb-0" />
            <span className="writing-vertical-rl text-base font-bold tracking-wider py-1">
              购买咨询
            </span>
          </button>
        </motion.div>
      </div>
    </>
  );
}
