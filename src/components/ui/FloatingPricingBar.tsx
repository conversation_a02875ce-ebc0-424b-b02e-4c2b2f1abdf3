"use client";

import React, { useEffect, useState } from "react";
import { useTheme } from "next-themes";
import QRCodeModal from "./QRCodeModal";

// 从PricingPlans组件中导入价格计划类型和数据
type PlanFeature = {
  text: string;
  included: boolean;
};

type PricingPlan = {
  name: string;
  price: string;
  description: string;
  features: PlanFeature[];
  buttonText: string;
  popular?: boolean;
};

// 使用与PricingPlans相同的价格计划数据
const plans: PricingPlan[] = [
  {
    name: "基础版",
    price: "¥0",
    description: "适合小型团队和个人使用",
    features: [
      { text: "最多 3 个用户", included: true },
      { text: "基础文档管理", included: true },
      { text: "标准客户支持", included: true },
      { text: "每月 100 次 API 调用", included: true },
      { text: "高级分析功能", included: false },
      { text: "自定义集成", included: false },
    ],
    buttonText: "立即免费体验",
  },
  {
    name: "专业版",
    price: "¥499",
    description: "适合中型团队和企业",
    features: [
      { text: "最多 10 个用户", included: true },
      { text: "高级文档管理", included: true },
      { text: "优先客户支持", included: true },
      { text: "每月 1000 次 API 调用", included: true },
      { text: "高级分析功能", included: true },
      { text: "自定义集成", included: false },
    ],
    buttonText: "立即购买",
    popular: true,
  },
  {
    name: "企业版",
    price: "详询报价",
    description: "适合大型企业和组织",
    features: [
      { text: "无限用户", included: true },
      { text: "企业级文档管理", included: true },
      { text: "24/7 专属支持", included: true },
      { text: "无限 API 调用", included: true },
      { text: "高级分析功能", included: true },
      { text: "自定义集成", included: true },
    ],
    buttonText: "联系我们",
  },
];

export default function FloatingPricingBar() {
  const [isVisible, setIsVisible] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [currentTableTitle, setCurrentTableTitle] = useState("功能");
  const { theme } = useTheme();
  // 添加二维码浮层状态
  const [isQRCodeVisible, setIsQRCodeVisible] = useState(false);

  // 处理按钮点击事件
  const handleButtonClick = (planName: string) => {
    if (planName === "企业版") {
      setIsQRCodeVisible(true);
    } else {
      // 其他版本的按钮处理逻辑
      console.log(`${planName} 按钮被点击`);
    }
  };

  // 关闭二维码浮层
  const handleCloseQRCode = () => {
    setIsQRCodeVisible(false);
  };

  useEffect(() => {
    const pricingSection = document.querySelector("[data-pricing-section]");
    // 获取两个表格元素的引用
    const featuresTable = document.querySelector("[data-features-table]");
    const supportTable = document.querySelector("[data-support-table]");

    const handleScroll = () => {
      if (!pricingSection) return;

      const rect = pricingSection.getBoundingClientRect();
      const pricingSectionHeight = pricingSection.clientHeight;
      // 仅当完全滚动过价格模块时显示（当价格模块完全在视口上方时）
      const scrolledPastPricing = rect.bottom <= 0;

      // 添加细微的滚动效果，与Navbar保持一致
      const offset = window.scrollY;
      setScrolled(offset > 10);

      setIsVisible(scrolledPastPricing);

      // 根据滚动位置确定当前表格标题
      if (featuresTable && supportTable) {
        const featuresRect = featuresTable.getBoundingClientRect();
        const supportRect = supportTable.getBoundingClientRect();

        // 检查服务支持表格是否在视口中或接近视口顶部
        // 若服务支持表格的顶部已经进入视口或即将进入视口（距离顶部200px以内）
        if (supportRect.top < 200) {
          setCurrentTableTitle("服务支持");
        } else {
          setCurrentTableTitle("功能");
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <>
      {/* 使用通用二维码浮层组件 */}
      <QRCodeModal isVisible={isQRCodeVisible} onClose={handleCloseQRCode} />

      <div
        className={`fixed top-16 left-0 right-0 z-20 transition-all duration-300 ${
          isVisible ? "opacity-100" : "opacity-0 pointer-events-none"
        } ${scrolled ? "pt-2" : "pt-0"}`}
      >
        <div className="max-w-6xl mx-auto px-2 md:px-4">
          <div
            className={`px-2 sm:px-4 py-2 bg-content1/80 backdrop-blur-xl rounded-2xl shadow-xl shadow-default/5 
            ${scrolled ? "mx-2 sm:mx-4" : ""}`}
          >
            {/* 模拟表格布局，第一列占 1/3 宽度用于功能名称，但在悬浮栏中保留空白 */}
            <div className="flex">
              {/* 功能列 - 现在显示当前表格标题，与表格表头位置对齐 */}
              <div className="w-1/3 hidden sm:flex items-center py-2 sm:py-4 px-2 sm:px-4">
                <h4 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-800 dark:text-gray-200">
                  {currentTableTitle}
                </h4>
              </div>

              {/* 版本列 - 占据全宽(移动端)或2/3宽度(桌面端)并分为3等份 */}
              <div className="w-full sm:w-2/3 flex">
                {plans.map((plan, index) => (
                  <div
                    key={index}
                    className="flex-1 flex flex-col items-center py-2 sm:py-4 px-1 sm:px-2"
                  >
                    <h4 className="text-sm sm:text-base font-bold mb-0.5 sm:mb-1">
                      {plan.name}
                    </h4>
                    <div className="flex items-baseline mb-2 sm:mb-3">
                      <span className="text-sm sm:text-base font-semibold">
                        {plan.price}
                      </span>
                      {plan.name !== "企业版" && (
                        <span className="text-gray-600 dark:text-gray-400 text-xs ml-1">
                          人/月
                        </span>
                      )}
                    </div>
                    <button
                      onClick={() => handleButtonClick(plan.name)}
                      className={`py-1 sm:py-2 px-2 sm:px-4 rounded-lg text-xs sm:text-sm font-medium w-20 sm:w-32 ${
                        plan.name === "企业版"
                          ? "bg-enterprise-button text-enterprise-buttonText hover:bg-enterprise-button/90"
                          : plan.name === "基础版"
                          ? "border border-gray-300 dark:border-gray-700 bg-transparent text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800"
                          : plan.popular
                          ? "bg-primary text-white hover:bg-primary/90"
                          : "bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-700"
                      } transition-colors`}
                    >
                      {plan.buttonText}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
