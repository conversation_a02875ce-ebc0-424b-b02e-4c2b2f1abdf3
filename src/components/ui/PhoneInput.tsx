import * as React from "react";
import { CheckIcon, ChevronsUpDown } from "lucide-react";
import * as RPNInput from "react-phone-number-input";
import { AsYouType, isValidPhoneNumber } from "libphonenumber-js";
import type { CountryCode } from "libphonenumber-js";

// 导入国家名称数据
import en from "react-phone-number-input/locale/en.json";

// 定义PhoneInput组件的ref类型
export interface PhoneInputRef {
  validate: () => boolean | undefined;
}

type PhoneInputProps = Omit<
  React.ComponentProps<"input">,
  "onChange" | "value" | "ref"
> &
  Omit<RPNInput.Props<typeof RPNInput.default>, "onChange"> & {
    onChange?: (value: RPNInput.Value) => void;
    onValidationChange?: (isValid: boolean | undefined) => void;
  };

const PhoneInput = React.forwardRef<PhoneInputRef, PhoneInputProps>(
  ({ className, onChange, value, onValidationChange, ...props }, ref) => {
    // 存储国家和电话号码部分
    const [country, setCountry] = React.useState<RPNInput.Country>(
      (props.defaultCountry as RPNInput.Country) || "CN"
    );
    const [displayValue, setDisplayValue] = React.useState<string>("");
    const [isValid, setIsValid] = React.useState<boolean | undefined>(
      undefined
    );

    // 保存上次的值，用于避免不必要的重渲染
    const lastValueRef = React.useRef<string>("");
    const inputRef = React.useRef<HTMLInputElement | null>(null);

    // 当外部value变化时更新内部状态，避免循环更新
    React.useEffect(() => {
      if (
        value &&
        typeof value === "string" &&
        value !== lastValueRef.current
      ) {
        lastValueRef.current = value;

        // 获取当前国家代码
        const countryCode = RPNInput.getCountryCallingCode(country);
        const prefix = `+${countryCode}`;

        // 提取本地号码部分，并格式化显示
        let localNumber = "";
        if (value.startsWith(prefix)) {
          localNumber = value.substring(prefix.length).trim();
        } else {
          localNumber = value.replace(/^\+\d+\s*/, "");
        }

        // 使用AsYouType格式化本地号码
        const formatter = new AsYouType(country as CountryCode);
        const formattedNumber = formatter.input(localNumber);

        // 只有当显示值确实发生变化时才更新状态
        if (formattedNumber !== displayValue) {
          setDisplayValue(formattedNumber);
        }
      } else if (!value && displayValue) {
        lastValueRef.current = "";
        setDisplayValue("");
      }
    }, [value, country, displayValue]);

    // 当验证状态变化时，通知父组件
    React.useEffect(() => {
      onValidationChange?.(isValid);
    }, [isValid, onValidationChange]);

    // 验证电话号码
    const validatePhoneNumber = React.useCallback((fullNumber: string) => {
      if (!fullNumber) {
        setIsValid(false);
        return false;
      }

      try {
        const valid = isValidPhoneNumber(fullNumber);
        setIsValid(valid);
        return valid;
      } catch {
        setIsValid(false);
        return false;
      }
    }, []);

    // 公开验证方法，允许外部组件手动触发验证
    React.useImperativeHandle(
      ref,
      () => ({
        validate: () => {
          if (!value) {
            setIsValid(false);
            return false;
          }
          return validatePhoneNumber(value as string);
        },
      }),
      [value, validatePhoneNumber]
    );

    // 使用防抖处理onChange，减少不必要的渲染
    const updateValueWithDebounce = React.useCallback(
      (newDisplayValue: string) => {
        // 获取纯数字
        const numericValue = newDisplayValue.replace(/[^\d]/g, "");

        // 使用 AsYouType 格式化输入
        const formatter = new AsYouType(country as CountryCode);
        const formattedValue = formatter.input(numericValue);

        // 更新显示值为格式化后的值
        setDisplayValue(formattedValue);

        // 只有当有意义的数字变化时，才通知父组件
        if (numericValue || (!numericValue && lastValueRef.current)) {
          const countryCode = RPNInput.getCountryCallingCode(country);
          const fullNumber = numericValue
            ? (`+${countryCode}${numericValue}` as RPNInput.Value)
            : ("" as RPNInput.Value);

          // 更新引用值，避免循环更新
          lastValueRef.current = fullNumber as string;

          // 验证并通知父组件
          if (fullNumber) {
            validatePhoneNumber(fullNumber as string);
          } else {
            setIsValid(undefined);
          }

          onChange?.(fullNumber);
        }
      },
      [country, onChange, validatePhoneNumber]
    );

    // 当国家变化时更新国家并重新组合电话号码
    const handleCountryChange = React.useCallback(
      (newCountry: RPNInput.Country) => {
        if (newCountry !== country) {
          setCountry(newCountry);

          // 如果有电话号码，则用新国家代码重新组合
          const numericValue = displayValue.replace(/[^\d]/g, "");
          if (numericValue) {
            const countryCode = RPNInput.getCountryCallingCode(newCountry);
            const fullNumber =
              `+${countryCode}${numericValue}` as RPNInput.Value;

            // 更新引用值，避免循环更新
            lastValueRef.current = fullNumber as string;

            // 验证电话号码
            validatePhoneNumber(fullNumber as string);

            onChange?.(fullNumber);

            // 重新格式化显示值
            const formatter = new AsYouType(newCountry as CountryCode);
            setDisplayValue(formatter.input(numericValue));
          }
        }
      },
      [country, displayValue, onChange, validatePhoneNumber]
    );

    // 获取国家选项列表并自定义特定地区的名称，实现优先排序
    const countryOptions = React.useMemo(() => {
      // 定义优先显示的国家列表（按显示顺序排列）
      const priorityCountries: RPNInput.Country[] = [
        "CN", // 中国大陆
        "HK", // 香港
        "MO", // 澳门
        "TW", // 台湾
        "US", // 美国
        "GB", // 英国
        "JP", // 日本
        "KR", // 韩国
        "SG", // 新加坡
        "AU", // 澳大利亚
        "CA", // 加拿大
      ];

      // 获取所有国家
      const allCountries = RPNInput.getCountries();

      // 创建国家对象数组，包含自定义名称
      const countryEntries = allCountries.map((country) => {
        let label = en[country] || country;

        // 自定义特定地区的名称
        if (country === "CN") {
          label = "China (Mainland)";
        } else if (country === "HK") {
          label = "Hong Kong, China";
        } else if (country === "MO") {
          label = "Macao, China";
        } else if (country === "TW") {
          label = "Taiwan, China";
        }

        return {
          value: country,
          label,
          isPriority: priorityCountries.includes(country),
        };
      });

      // 排序逻辑：优先国家在前，按优先级排序；其他国家按字母顺序排序
      return countryEntries.sort((a, b) => {
        // 如果两个都是优先国家，按优先数组中的顺序排序
        if (a.isPriority && b.isPriority) {
          return (
            priorityCountries.indexOf(a.value as RPNInput.Country) -
            priorityCountries.indexOf(b.value as RPNInput.Country)
          );
        }

        // 优先国家排在前面
        if (a.isPriority) return -1;
        if (b.isPriority) return 1;

        // 其他国家按名称字母顺序排序
        return a.label.localeCompare(b.label);
      });
    }, []);

    // 清除输入框内容
    const handleClearInput = React.useCallback(() => {
      setDisplayValue("");
      lastValueRef.current = "";
      setIsValid(undefined);
      onChange?.("" as RPNInput.Value);

      // 聚焦输入框
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, [onChange]);

    return (
      <div className={`flex flex-col ${className || ""}`}>
        <div className="flex">
          <CustomCountrySelect
            value={country}
            options={countryOptions}
            onChange={handleCountryChange}
            disabled={props.disabled}
          />
          <div className="relative flex-1">
            <input
              className={`rounded-e-lg rounded-s-none border ${
                isValid === false ? "border-red-500" : "border-gray-300"
              } px-3 py-3 h-[46px] focus:border-primary focus:outline-none w-full bg-white text-gray-900 pr-10 
              [&:-webkit-autofill]:bg-white [&:-webkit-autofill]:shadow-[0_0_0_30px_white_inset] [&:-webkit-autofill]:!text-gray-900`}
              type="tel"
              value={displayValue}
              onChange={(e) => updateValueWithDebounce(e.target.value)}
              placeholder={props.placeholder || "Enter phone number"}
              disabled={props.disabled}
              autoComplete="tel"
              name="phone"
              ref={(node) => {
                // 处理多个ref
                if (typeof ref === "function") {
                  ref(node as unknown as PhoneInputRef);
                } else if (ref) {
                  (
                    ref as React.MutableRefObject<HTMLInputElement | null>
                  ).current = node;
                }
                // 更新内部 ref
                inputRef.current = node;
              }}
            />
            {displayValue && !props.disabled && (
              <button
                type="button"
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-500 focus:outline-none"
                onClick={handleClearInput}
                aria-label="Clear input"
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }
);
PhoneInput.displayName = "PhoneInput";

type CountryEntry = {
  label: string;
  value: RPNInput.Country | undefined;
  isPriority?: boolean;
};

type CustomCountrySelectProps = {
  disabled?: boolean;
  value: RPNInput.Country;
  options: CountryEntry[];
  onChange: (country: RPNInput.Country) => void;
};

const CustomCountrySelect = ({
  disabled,
  value: selectedCountry,
  options: countryList,
  onChange,
}: CustomCountrySelectProps) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  // 处理点击外部关闭下拉列表
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSearchQuery("");
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  // 获取当前选中国家的区号
  const selectedCountryCode = selectedCountry
    ? `+${RPNInput.getCountryCallingCode(selectedCountry)}`
    : "";

  // 过滤国家列表
  const filteredCountryList = React.useMemo(() => {
    if (!searchQuery.trim()) return countryList;

    const normalizedQuery = searchQuery.toLowerCase();
    return countryList.filter(({ label, value }) => {
      if (!value) return false;

      // 按国家名称搜索
      const matchesName = label.toLowerCase().includes(normalizedQuery);

      // 按国家代码搜索 (+xx)
      const callingCode = value ? RPNInput.getCountryCallingCode(value) : "";
      const matchesCode =
        callingCode.includes(normalizedQuery) ||
        `+${callingCode}`.includes(normalizedQuery);

      return matchesName || matchesCode;
    });
  }, [countryList, searchQuery]);

  return (
    <div className="relative flex-shrink-0" ref={dropdownRef}>
      <button
        type="button"
        className={`flex items-center gap-1 rounded-s-lg rounded-e-none border border-r-0 border-gray-300 px-3 h-[46px] ${
          disabled
            ? "bg-gray-100 cursor-not-allowed text-gray-500"
            : "bg-white cursor-pointer hover:bg-gray-50 text-gray-900"
        }`}
        disabled={disabled}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-base font-medium">{selectedCountryCode}</span>
        <ChevronsUpDown
          className={`size-4 opacity-50 ${disabled ? "hidden" : "opacity-100"}`}
        />
      </button>
      {isOpen && (
        <div className="absolute top-full left-0 z-50 mt-1 w-[300px] rounded-md border border-gray-200 bg-white shadow-md">
          <div className="p-2">
            <input
              type="text"
              placeholder="Search countries..."
              className="w-full rounded border border-gray-200 px-3 py-1 text-sm bg-white text-gray-900"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              autoFocus
            />
          </div>
          <div className="max-h-72 overflow-y-auto">
            {filteredCountryList.length > 0 ? (
              filteredCountryList.map(({ value, label }) =>
                value ? (
                  <CountrySelectOption
                    key={value}
                    country={value}
                    countryName={label}
                    selectedCountry={selectedCountry}
                    onChange={(country) => {
                      onChange(country);
                      setIsOpen(false);
                      setSearchQuery("");
                    }}
                  />
                ) : null
              )
            ) : (
              <div className="px-3 py-2 text-sm text-gray-500">
                No matching countries found
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

interface CountrySelectOptionProps extends RPNInput.FlagProps {
  selectedCountry: RPNInput.Country;
  onChange: (country: RPNInput.Country) => void;
}

const CountrySelectOption = ({
  country,
  countryName,
  selectedCountry,
  onChange,
}: CountrySelectOptionProps) => {
  return (
    <div
      className={`flex items-center gap-2 px-3 py-2 cursor-pointer hover:bg-gray-100 ${
        country === selectedCountry ? "bg-gray-50" : "bg-white"
      }`}
      onClick={() => onChange(country)}
    >
      <span className="flex-1 text-sm text-gray-900">{countryName}</span>
      <span className="text-sm text-gray-500">{`+${RPNInput.getCountryCallingCode(
        country
      )}`}</span>
      {country === selectedCountry && (
        <CheckIcon className="size-4 text-gray-900" />
      )}
    </div>
  );
};

export { PhoneInput };
