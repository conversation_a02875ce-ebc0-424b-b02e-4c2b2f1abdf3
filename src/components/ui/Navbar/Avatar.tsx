"use client";

import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import { ThemeSwitcher } from "./ThemeSwitcher";
import {
  <PERSON>older<PERSON><PERSON>ban,
  <PERSON>ting<PERSON>,
  Moon,
  HelpCircle,
  LogOut,
  User,
} from "lucide-react";

interface AvatarProps {
  /**
   * 用户头像URL，如果未提供则使用默认头像
   */
  src?: string;
  /**
   * 头像尺寸，默认为40px
   */
  size?: number;
  /**
   * 用户名，用于alt文本
   */
  username?: string;
  /**
   * 用户邮箱
   */
  email?: string;
  /**
   * 点击头像时的回调
   */
  onClick?: () => void;
  /**
   * 退出登录回调
   */
  onSignOut?: () => void;
}

const Avatar: React.FC<AvatarProps> = ({
  src,
  size = 32,
  username = "User",
  email = "<EMAIL>",
  onClick,
  onSignOut,
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [menuOpenTime, setMenuOpenTime] = useState<number>(0);
  const [menuCloseTime, setMenuCloseTime] = useState<number>(0);
  const menuRef = useRef<HTMLDivElement>(null);
  const avatarRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 检查菜单是否可以打开（关闭后至少500ms）
  const canOpenMenu = () => {
    const timeSinceClose = Date.now() - menuCloseTime;
    return !isMenuOpen && (menuCloseTime === 0 || timeSinceClose > 500);
  };

  // 检查菜单是否可以关闭（打开后至少500ms）
  const canCloseMenu = () => {
    const timeSinceOpen = Date.now() - menuOpenTime;
    return isMenuOpen && timeSinceOpen > 500;
  };

  // 处理鼠标进入头像区域
  const handleMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    // 只有当菜单可以打开时才添加延迟显示
    if (canOpenMenu()) {
      // 添加延迟显示菜单
      hoverTimeoutRef.current = setTimeout(() => {
        setIsMenuOpen(true);
        setMenuOpenTime(Date.now()); // 记录菜单打开的时间
      }, 300); // 300ms的延迟显示
    }
  };

  // 处理鼠标离开头像区域
  const handleMouseLeave = () => {
    // 清除延迟显示的定时器
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }

    timeoutRef.current = setTimeout(() => {
      if (isMenuOpen) {
        setIsMenuOpen(false);
        setMenuCloseTime(Date.now()); // 记录菜单关闭的时间
      }
    }, 300); // 添加延迟，防止用户移动到菜单时菜单消失
  };

  // 处理鼠标进入菜单区域
  const handleMenuMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };

  // 处理鼠标离开菜单区域
  const handleMenuMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      if (isMenuOpen) {
        setIsMenuOpen(false);
        setMenuCloseTime(Date.now()); // 记录菜单关闭的时间
      }
    }, 300);
  };

  // 处理点击头像
  const handleAvatarClick = () => {
    // 清除所有定时器
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }

    // 如果菜单未打开且可以打开，则打开菜单
    if (!isMenuOpen && canOpenMenu()) {
      setIsMenuOpen(true);
      setMenuOpenTime(Date.now()); // 记录菜单打开的时间
    }
    // 如果菜单已打开且可以关闭，则关闭菜单
    else if (isMenuOpen && canCloseMenu()) {
      setIsMenuOpen(false);
      setMenuCloseTime(Date.now()); // 记录菜单关闭的时间
    }

    if (onClick) onClick();
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  // 处理点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        avatarRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        !avatarRef.current.contains(event.target as Node)
      ) {
        if (isMenuOpen) {
          setIsMenuOpen(false);
          setMenuCloseTime(Date.now()); // 记录菜单关闭的时间
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMenuOpen]);

  // 处理退出登录
  const handleSignOut = () => {
    setIsMenuOpen(false);
    setMenuCloseTime(Date.now()); // 记录菜单关闭的时间
    if (onSignOut) onSignOut();
  };

  return (
    <div
      className="relative"
      ref={avatarRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <button
        className="relative inline-flex items-center justify-center rounded-full overflow-hidden focus:outline-none"
        style={{ width: size, height: size }}
        onClick={handleAvatarClick}
      >
        {src ? (
          <Image
            src={src}
            alt={`${username}'s avatar`}
            width={size}
            height={size}
            className="object-cover"
          />
        ) : (
          <div className="bg-foreground text-background flex items-center justify-center w-full h-full text-lg font-medium">
            {username.charAt(0).toUpperCase()}
          </div>
        )}
      </button>

      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            ref={menuRef}
            initial={{ opacity: 0, scale: 0.85, y: 0 }}
            animate={{ opacity: 1, scale: 1, y: 8 }}
            exit={{ opacity: 0, scale: 0.85, y: 0 }}
            transition={{ type: "spring", stiffness: 350, damping: 25 }}
            className="absolute -right-4 mt-2 w-56 rounded-xl bg-content1 shadow-lg ring-1 ring-foreground/5 focus:outline-none z-50 overflow-hidden"
            style={{ transformOrigin: "top right" }}
            onMouseEnter={handleMenuMouseEnter}
            onMouseLeave={handleMenuMouseLeave}
          >
            <div className="px-4 py-3">
              {/* 用户信息部分 */}
              <div className="flex items-center">
                <div className="flex-1 min-w-0 pl-1">
                  <p className="text-sm font-bold text-foreground/80 truncate">
                    {username}
                  </p>
                  <p className="text-xs text-foreground/60 truncate">{email}</p>
                </div>
              </div>
            </div>

            {/* 分割线 */}
            <div className="h-px bg-foreground/5 mx-4 my-1"></div>

            {/* 菜单选项 */}
            <div className="py-1">
              <Link
                href="/dashboard"
                className="flex items-center px-4 py-2.5 text-sm font-medium text-foreground/70 hover:text-foreground hover:bg-foreground/5 transition-colors rounded-lg mx-1 group"
                onClick={() => {
                  setIsMenuOpen(false);
                  setMenuCloseTime(Date.now());
                }}
              >
                <FolderKanban className="mr-3 h-4 w-4 text-foreground/50 group-hover:text-foreground" />
                My Projects
              </Link>
              <Link
                href="/settings"
                className="flex items-center px-4 py-2.5 text-sm font-medium text-foreground/70 hover:text-foreground hover:bg-foreground/5 transition-colors rounded-lg mx-1 group"
                onClick={() => {
                  setIsMenuOpen(false);
                  setMenuCloseTime(Date.now());
                }}
              >
                <Settings className="mr-3 h-4 w-4 text-foreground/50 group-hover:text-foreground" />
                Settings
              </Link>
              <div className="flex items-center justify-between px-4 py-2.5 text-sm font-medium text-foreground/70 hover:text-foreground hover:bg-foreground/5 rounded-lg mx-1 group">
                <div className="flex items-center">
                  <Moon className="mr-3 h-4 w-4 text-foreground/50 group-hover:text-foreground" />
                  Theme
                </div>
                <div className="flex-shrink-0">
                  <ThemeSwitcher />
                </div>
              </div>
            </div>

            {/* 分割线 */}
            <div className="h-px bg-foreground/5 mx-4 my-1"></div>

            {/* 底部选项 */}
            <div className="py-1">
              <Link
                href="/help"
                className="flex items-center px-4 py-2.5 text-sm font-medium text-foreground/70 hover:text-foreground hover:bg-foreground/5 transition-colors rounded-lg mx-1 group"
                onClick={() => {
                  setIsMenuOpen(false);
                  setMenuCloseTime(Date.now());
                }}
              >
                <HelpCircle className="mr-3 h-4 w-4 text-foreground/50 group-hover:text-foreground" />
                Help Center
              </Link>
              <button
                onClick={handleSignOut}
                className="text-left w-[calc(100%-0.5rem)] mx-1 flex items-center px-4 py-2.5 text-sm font-medium text-foreground/70 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-400/10 rounded-lg group"
              >
                <LogOut className="mr-3 h-4 w-4 text-foreground/50 group-hover:text-red-500" />
                <span>Sign out</span>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Avatar;
