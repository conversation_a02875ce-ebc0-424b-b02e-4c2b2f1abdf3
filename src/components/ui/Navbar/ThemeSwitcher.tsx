"use client";

import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { Tooltip } from "@heroui/tooltip";
import { Sun, MoonStar, MonitorSmartphone } from "lucide-react";

export function ThemeSwitcher() {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme, resolvedTheme } = useTheme();

  // 确保组件只在客户端渲染
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  // 切换主题函数
  const toggleTheme = () => {
    if (theme === "dark") {
      setTheme("light");
    } else if (theme === "light") {
      setTheme("system");
    } else {
      setTheme("dark");
    }
  };

  // 根据当前主题返回对应的图标
  const renderThemeIcon = () => {
    switch (theme) {
      case "light":
        // 太阳图标 - 当前是亮色模式
        return <Sun className="w-4 h-4" />;
      case "dark":
        // 月亮图标 - 当前是暗色模式
        return <MoonStar className="w-4 h-4" />;
      default:
        // 电脑图标 - 当前是系统模式
        return <MonitorSmartphone className="w-4 h-4" />;
    }
  };

  // 获取当前主题的简短显示名称
  const getShortThemeName = () => {
    switch (theme) {
      case "light":
        return "Light";
      case "dark":
        return "Dark";
      default:
        return "System";
    }
  };

  // 获取当前主题的完整显示名称（用于tooltip）
  const getCurrentThemeName = () => {
    switch (theme) {
      case "light":
        return "Light Theme";
      case "dark":
        return "Dark Theme";
      default:
        return "System Theme";
    }
  };

  // 确定当前实际的主题（用于样式）
  const isDark = resolvedTheme === "dark";

  return (
    <div className="flex items-center gap-2">
      <span className="text-xs px-2 py-0.5 rounded-md bg-foreground/5 text-foreground/80">
        {getShortThemeName()}
      </span>
      <Tooltip
        content={getCurrentThemeName()}
        placement="bottom"
        delay={1000}
        closeDelay={0}
        color="default"
        size="sm"
        radius="md"
        classNames={{
          base: `z-50 ${isDark ? "dark" : ""}`,
          content: `px-2 py-1 text-xs ${
            isDark
              ? "bg-content2 text-foreground border border-divider"
              : "bg-white text-gray-800 border border-gray-200"
          }`,
        }}
      >
        <button
          onClick={toggleTheme}
          className="text-foreground focus:outline-none"
          aria-label="Switch Theme"
        >
          {renderThemeIcon()}
        </button>
      </Tooltip>
    </div>
  );
}
