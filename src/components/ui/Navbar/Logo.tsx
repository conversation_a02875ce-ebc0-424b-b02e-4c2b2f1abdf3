"use client";

import React from "react";
import { useEffect, useState } from "react";
import { useTheme } from "next-themes";

interface LogoProps {
  className?: string;
  width?: number;
  height?: number;
  forceIconColor?: "foreground" | "primary" | "secondary" | string;
}

export default function Logo({
  className = "h-[22px] w-auto",
  width = 100,
  height = 24,
  forceIconColor,
}: LogoProps) {
  // 使用 next-themes 的 useTheme 钩子
  const { theme, resolvedTheme } = useTheme();
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [mounted, setMounted] = useState(false);

  // 确保组件只在客户端渲染后再检测主题
  useEffect(() => {
    setMounted(true);
  }, []);

  // 监听主题变化
  useEffect(() => {
    if (!mounted) return;

    // resolvedTheme 考虑了 system 设置，比 theme 更可靠
    setIsDarkMode(resolvedTheme === "dark");
  }, [resolvedTheme, mounted]);

  // 确定文本颜色
  const getTextColor = () => {
    return isDarkMode ? "#FFFFFF" : "#3D3D3D";
  };

  // 确定图标颜色
  const getIconColor = (defaultColor: string) => {
    if (forceIconColor === "foreground") {
      return isDarkMode ? "#FFFFFF" : "#3D3D3D";
    }
    return defaultColor; // 使用默认颜色
  };

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      fill="none"
      version="1.1"
      width={width}
      height={height}
      viewBox="0 0 205.0068359375 47"
      className={className}
    >
      <defs>
        <clipPath id="master_svg0_265_1620">
          <rect x="0" y="0" width="205.0068359375" height="47" rx="0" />
        </clipPath>
      </defs>
      <g clipPath="url(#master_svg0_265_1620)">
        <g>
          <g>
            <g>
              <g>
                <path
                  d="M87.9192890625,11.7840478515625Q87.6312890625,11.7360478515625,87.0552890625,11.7360478515625Q84.6552890625,11.7360478515625,82.5432890625,13.1520478515625Q80.4312890625,14.5680478515625,79.1592890625,17.136047851562502Q77.8872790625,19.7040478515625,77.8872790625,23.0160478515625Q77.8872790625,26.2800478515625,79.1112790625,28.5840478515625Q80.3352890625,30.8880478515625,82.3032890625,32.0640478515625Q84.2712890625,33.2400478515625,86.5272890625,33.4320478515625L87.1992890625,33.4800478515625Q87.77528906250001,33.4800478515625,88.3992890625,33.3360478515625L86.9592890625,31.0320478515625Q86.1432890625,29.6880478515625,85.1352890625,28.5360478515625Q84.9912890625,28.2960478515625,84.9912890625,28.1040478515625Q84.9912890625,27.6720478515625,85.7592890625,27.4320478515625Q86.62328906249999,27.1440478515625,87.34328906249999,27.1440478515625Q87.77528906250001,27.1440478515625,88.54328906250001,27.3360478515625Q89.4552890625,27.5760478515625,89.9832890625,28.4400478515625Q91.2792890625,30.1680478515625,92.28728906250001,31.3200478515625Q93.9672890625,29.8800478515625,94.9752890625,27.5760478515625Q95.9832890625,25.2720478515625,95.9832890625,22.2480478515625Q95.9832890625,17.6880478515625,93.7032890625,15.0000478515625Q91.4232890625,12.3120478515625,87.9192890625,11.7840478515625ZM71.0712890625,22.2480478515625Q71.0712890625,17.5440478515625,73.1592890625,13.8480478515625Q75.2472790625,10.1520478515625,78.9192890625,8.0880478515625Q82.5912890625,6.0240478515625,87.2472890625,6.0240478515625Q91.8072890625,6.0240478515625,95.3112890625,8.1840478515625Q98.8152890625,10.3440478515625,100.7832890625,14.2560478515625Q102.7512890625,18.1680478515625,102.7512890625,23.2080478515625Q102.7512890625,27.0480478515625,101.28728906250001,30.2400478515625Q99.8232890625,33.4320478515625,97.1352890625,35.6400478515625Q98.0952890625,36.4080478515625,99.1752890625,36.9360478515625Q100.2552890625,37.4640478515625,101.8392890625,37.6080478515625Q101.9352890625,37.6080478515625,101.9352890625,37.7040478515625Q101.8872890625,37.9920478515625,101.1912890625,39.1680478515625Q100.4952890625,40.3440478515625,99.9192890625,40.9200478515625Q99.0072890625,41.8320478515625,98.1672890625,42.3120478515625Q97.3272890625,42.7920478515625,96.2712890625,42.7920478515625Q94.87928906249999,42.7920478515625,91.7592890625,38.5200478515625Q89.4072890625,39.2400478515625,86.62328906249999,39.2400478515625Q82.11128906249999,39.2400478515625,78.5592890625,37.0560478515625Q75.0072890625,34.8720478515625,73.0392790625,31.0080478515625Q71.0712890625,27.1440478515625,71.0712890625,22.2480478515625ZM117.0496890625,39.4922478515625Q113.04188906249999,39.4922478515625,110.7566890625,36.949247851562504Q108.4714890625,34.4062478515625,108.4714890625,29.0391478515625L108.4714890625,18.4688478515625L105.6824890625,17.7656478515625L105.6824890625,13.640667851562501L115.3152890625,13.640667851562501L115.3152890625,29.0859478515625Q115.3152890625,31.8047478515625,116.26448906249999,32.9883478515625Q117.21368906250001,34.1719478515625,119.1355890625,34.1719478515625Q122.1824890625,34.1719478515625,123.7527890625,31.8750478515625L123.7527890625,18.4688478515625L120.72938906249999,17.7656478515625L120.72938906249999,13.640667851562501L130.59648906249998,13.640667851562501L130.59648906249998,34.1953478515625L133.6433890625,34.8984478515625L133.6433890625,39.0000478515625L124.5964890625,39.0000478515625L124.1277890625,35.789147851562504Q122.9324890625,37.5703478515625,121.1277890625,38.5312478515625Q119.3230890625,39.4922478515625,117.0496890625,39.4922478515625ZM148.2683890625,39.4922478515625Q144.5183890625,39.4922478515625,141.8230890625,37.851647851562504Q139.1277890625,36.2109478515625,137.6980890625,33.2930478515625Q136.2683890625,30.3750478515625,136.2683890625,26.5781478515625L136.2683890625,26.0859478515625Q136.2683890625,22.2656478515625,137.6980890625,19.382847851562502Q139.1277890625,16.4531478515625,141.81138906249998,14.8125478515625Q144.4949890625,13.1719278515625,148.22148906249998,13.1719278515625Q152.0183890625,13.1719278515625,154.6668890625,14.8125478515625Q157.3386890625,16.4531478515625,158.7683890625,19.371147851562498Q160.1980890625,22.2891478515625,160.1980890625,26.0859478515625L160.1980890625,26.5781478515625Q160.1980890625,30.3984478515625,158.7683890625,33.2812478515625Q157.3386890625,36.1875478515625,154.6668890625,37.839847851562496Q151.9949890625,39.4922478515625,148.2683890625,39.4922478515625ZM148.2683890625,34.242247851562496Q150.0261890625,34.242247851562496,151.1746890625,33.2812478515625Q152.2996890625,32.3203478515625,152.8386890625,30.5977478515625Q153.3777890625,28.8750478515625,153.3777890625,26.5781478515625L153.3777890625,26.0859478515625Q153.3777890625,23.8594478515625,152.8386890625,22.1016478515625Q152.2996890625,20.3672478515625,151.1629890625,19.406247851562497Q150.0261890625,18.445347851562502,148.22148906249998,18.445347851562502Q146.4871890625,18.445347851562502,145.3152890625,19.406247851562497Q144.1668890625,20.3906478515625,143.6394890625,22.1133478515625Q143.1121890625,23.8359478515625,143.1121890625,26.0859478515625L143.1121890625,26.5781478515625Q143.1121890625,28.8516478515625,143.6511890625,30.6094478515625Q144.1902890625,32.3438478515625,145.3269890625,33.2930478515625Q146.4636890625,34.242247851562496,148.2683890625,34.242247851562496ZM173.2062890625,39.4922478515625Q169.6672890625,39.4922478515625,167.75668906250002,37.5469478515625Q165.84648906249998,35.601647851562504,165.84648906249998,31.3828478515625L165.84648906249998,18.445347851562502L162.3543890625,18.445347851562502L162.3543890625,13.640667851562501L165.84648906249998,13.640667851562501L165.84648906249998,7.4297378515625L172.6672890625,7.4297378515625L172.6672890625,13.640667851562501L177.3312890625,13.640667851562501L177.3312890625,18.445347851562502L172.6672890625,18.445347851562502L172.6672890625,31.3594478515625Q172.6672890625,32.8359478515625,173.28828906249998,33.4688478515625Q173.9092890625,34.101647851562504,174.9872890625,34.101647851562504Q175.5492890625,34.101647851562504,176.2062890625,34.0195478515625Q176.8622890625,33.9375478515625,177.3312890625,33.8438478515625L177.9172890625,38.7891478515625Q176.8622890625,39.0938478515625,175.6312890625,39.2930478515625Q174.40128906249998,39.4922478515625,173.2062890625,39.4922478515625ZM192.6122890625,39.4922478515625Q188.97928906250002,39.4922478515625,186.2602890625,37.8750478515625Q183.5422890625,36.2812478515625,182.0532890625,33.4688478515625Q180.5652890625,30.6562478515625,180.5652890625,27.0469478515625L180.5652890625,26.1094478515625Q180.5652890625,22.3359478515625,181.9712890625,19.4180478515625Q183.37728906249998,16.5000478515625,185.9562890625,14.8125478515625Q188.4402890625,13.1719278515625,191.9092890625,13.1719278515625L192.00228906249998,13.1719278515625Q195.4482890625,13.1719278515625,197.7922890625,14.5312478515625Q202.5732890625,17.2969478515625,202.5732890625,24.6328478515625L202.5732890625,28.3594478515625L187.6672890625,28.3594478515625L187.6202890625,28.5000478515625Q187.7602890625,30.1406478515625,188.4632890625,31.4531478515625Q189.1432890625,32.742247851562496,190.4092890625,33.492247851562496Q191.6742890625,34.242247851562496,193.47928906250002,34.242247851562496Q195.35428906250002,34.242247851562496,196.9832890625,33.761747851562504Q198.6122890625,33.2812478515625,200.1592890625,32.3438478515625L202.0102890625,36.5625478515625Q200.4402890625,37.8281478515625,198.0612890625,38.6602478515625Q195.6822890625,39.4922478515625,192.6122890625,39.4922478515625ZM187.6432890625,23.7188478515625L187.7132890625,23.8359478515625L196.1742890625,23.8359478515625L196.1742890625,23.2266478515625Q196.1742890625,21.7031478515625,195.77628906249998,20.5547478515625Q194.8852890625,18.2344478515625,192.1202890625,18.2344478515625Q190.7842890625,18.2344478515625,189.8232890625,18.9375478515625Q188.8852890625,19.6641478515625,188.3582890625,20.906247851562497Q187.8312890625,22.1484478515625,187.6432890625,23.7188478515625Z"
                  fill={getTextColor()}
                  fillOpacity="1"
                />
              </g>
            </g>
            <g>
              <g>
                <g>
                  <path
                    d="M53.9952325378418,14.517375543212891C48.6464325378418,9.18224554321289,39.959152537841796,9.18224554321289,34.610342537841795,14.517375543212891C34.0190655378418,15.10714554321289,33.500556537841796,15.751355543212892,33.0366325378418,16.42278554321289C37.2210725378418,21.83962554321289,38.2489925378418,28.90772554321289,36.129482537841795,35.141125543212894C37.5576525378418,36.20272554321289,39.158652537841796,36.94672554321289,40.8142425378418,37.38222554321289L40.8142425378418,47.00002554321289L53.9952325378418,33.85272554321289C59.3440325378418,28.51752554321289,59.3440325378418,19.85251554321289,53.9952325378418,14.517375543212891Z"
                    fill={getIconColor("#58BECA")}
                    fillOpacity="1"
                  />
                </g>
              </g>
              <g>
                <g>
                  <path
                    d="M21.6749,12.2672L21.6749,0L5.24649,16.3774L5.25559,16.3774C5.17372,16.4681,5.08275,16.5407,5.00088,16.6224C-1.66694,23.2732,-1.66694,34.0614,5.00088,40.7122C11.6687,47.3629,22.4845,47.3629,29.1523,40.7122C35.8202,34.0614,35.8202,23.2732,29.1523,16.6224C26.9965,14.472,24.4039,13.0293,21.6658,12.2672L21.6749,12.2672Z"
                    fill={getIconColor("#443AA7")}
                    fillOpacity="1"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  );
}
