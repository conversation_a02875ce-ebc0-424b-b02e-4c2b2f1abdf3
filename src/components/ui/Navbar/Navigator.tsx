"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useAuthActions } from "@/hooks/useAuthActions";
import Avatar from "./Avatar";
import { motion } from "framer-motion";
import Logo from "./Logo";
import { ThemeSwitcher } from "./ThemeSwitcher";

// 导航链接类型
interface NavLink {
  name: string;
  href: string;
  current: boolean;
}

// 添加组件属性接口
interface NavigatorProps {
  currentPath?: string;
}

const Navigator: React.FC<NavigatorProps> = ({ currentPath = "/" }) => {
  // 获取路由和用户状态
  const router = useRouter();
  const { isAuthenticated, user, handleLogout } = useAuthActions();

  // 滚动状态
  const [scrolled, setScrolled] = useState(false);

  // 移动端菜单状态
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // 用于测量菜单高度的引用
  const menuRef = useRef<HTMLDivElement>(null);
  const [menuHeight, setMenuHeight] = useState<number>(0);
  const animatingRef = useRef<boolean>(false);

  // 监听滚动事件 - 使用客户端安全的方式
  useEffect(() => {
    // 确保代码只在客户端执行
    if (typeof window === "undefined") return;

    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    // 初始检查
    handleScroll();

    // 添加滚动事件监听
    window.addEventListener("scroll", handleScroll);

    // 组件卸载时移除事件监听
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // 处理菜单展开/折叠过程
  const handleMenuTransition = useCallback(() => {
    if (!menuRef.current) return;

    if (mobileMenuOpen) {
      // 展开菜单
      animatingRef.current = true;

      // 稍微延迟设置高度，确保DOM渲染完成
      requestAnimationFrame(() => {
        if (!menuRef.current) return;
        const height = menuRef.current.scrollHeight;
        setMenuHeight(height);

        // 动画完成后标记
        const timer = setTimeout(() => {
          animatingRef.current = false;
        }, 350);
        return () => clearTimeout(timer);
      });
    } else {
      // 折叠菜单
      animatingRef.current = true;

      if (menuRef.current) {
        const height = menuRef.current.scrollHeight;
        setMenuHeight(height);
      }

      // 在下一帧设置为0，触发平滑折叠
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          setMenuHeight(0);

          // 动画完成后标记
          const timer = setTimeout(() => {
            animatingRef.current = false;
          }, 350);
          return () => clearTimeout(timer);
        });
      });
    }
  }, [mobileMenuOpen]);

  // 监听菜单开关状态
  useEffect(() => {
    handleMenuTransition();
  }, [handleMenuTransition]);

  // 使用ResizeObserver监听内容变化
  useEffect(() => {
    if (!menuRef.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      // 仅在展开状态且不在动画过程中更新高度
      if (!mobileMenuOpen || !menuRef.current || animatingRef.current) return;

      for (const entry of entries) {
        if (entry.target === menuRef.current) {
          setMenuHeight(entry.contentRect.height);
        }
      }
    });

    resizeObserver.observe(menuRef.current);

    // 缓存当前ref值用于清理
    const currentMenuRef = menuRef.current;

    return () => {
      if (currentMenuRef) {
        resizeObserver.unobserve(currentMenuRef);
      }
      resizeObserver.disconnect();
    };
  }, [mobileMenuOpen]);

  // 导航链接
  const [navLinks, setNavLinks] = useState<NavLink[]>([
    { name: "Overview", href: "/", current: currentPath === "/" },
    {
      name: "Use cases",
      href: "/use-cases",
      current: currentPath === "/use-cases",
    },
    { name: "Pricing", href: "/pricing", current: currentPath === "/pricing" },
    // { name: "Blog", href: "/blog", current: false }, // 暂时隐藏Blog选项
    {
      name: "Help center",
      href: "/help-center",
      current: currentPath === "/help-center",
    },
  ]);

  // 初始化时根据当前路径设置活动项
  useEffect(() => {
    setNavLinks((prevLinks) =>
      prevLinks.map((link) => ({
        ...link,
        current: link.href === currentPath,
      }))
    );
  }, [currentPath]);

  // 处理导航项点击
  const handleNavClick = (index: number) => {
    setNavLinks(
      navLinks.map((link, i) => ({
        ...link,
        current: i === index,
      }))
    );
    setMobileMenuOpen(false);
  };

  // 切换移动端菜单
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <div
      className={`fixed top-0 left-0 right-0 z-20 transition-all duration-500 ease-in-out ${
        scrolled ? "pt-2" : "pt-0"
      }`}
    >
      <div className="max-w-6xl mx-auto">
        <nav
          className={`px-4 py-2 transition-all duration-300 ease-in-out ${
            scrolled
              ? mobileMenuOpen
                ? "mx-4 bg-content1/80 rounded-2xl backdrop-blur-xl shadow-xl shadow-default/5 pb-6"
                : "mx-4 bg-content1/80 rounded-2xl backdrop-blur-xl shadow-xl shadow-default/5"
              : mobileMenuOpen
              ? "bg-content1 rounded-b-2xl shadow-xl pb-6"
              : "bg-transparent dark:bg-transparent"
          }`}
        >
          <div className="mx-auto transition-all duration-300 ease-in-out w-full max-w-full">
            <div className="flex items-center justify-between w-full h-10">
              {/* 左侧区域：Logo */}
              <div className="flex items-center">
                {/* Logo */}
                <div className="flex-shrink-0 flex items-center">
                  <Link href="/" className="flex items-center">
                    <Logo />
                  </Link>
                </div>
              </div>

              {/* 导航链接 - 桌面端居中显示 */}
              <div className="hidden sm:block mx-auto">
                <div className="flex space-x-4 lg:space-x-6">
                  {navLinks.map((link, index) => (
                    <Link
                      key={link.name}
                      href={link.href}
                      className={`${
                        link.current
                          ? "text-foreground"
                          : "text-foreground/60 hover:text-foreground"
                      } transition-colors duration-300 px-3 pt-1.5 pb-2 text-sm font-bold whitespace-nowrap hover:bg-foreground/5 rounded-lg inline-block`}
                      onClick={() => handleNavClick(index)}
                      aria-current={link.current ? "page" : undefined}
                    >
                      {link.name}
                    </Link>
                  ))}
                </div>
              </div>

              {/* 用户部分 - 固定宽度确保一致性 */}
              <div className="flex items-center justify-end w-auto sm:w-24 gap-2">
                {/* <ThemeSwitcher /> */}
                {isAuthenticated ? (
                  <div className="relative">
                    <Avatar
                      src={user?.avatar}
                      username={user?.name || "User"}
                      email={user?.email || "<EMAIL>"}
                      onSignOut={() => {
                        // 调用退出登录方法
                        handleLogout();
                        console.log("User signed out");
                        // 退出后跳转到注册页面
                        router.push("/login");
                      }}
                    />
                  </div>
                ) : (
                  <Link
                    href="/login"
                    className="bg-enterprise-button hover:bg-enterprise-button/90 text-enterprise-buttonText px-3 md:px-4 py-1.5 md:py-2 rounded-lg text-sm font-medium transition-all duration-300 whitespace-nowrap"
                  >
                    Sign Up
                  </Link>
                )}

                {/* 汉堡菜单按钮 (仅移动端显示) - 移到登录按钮右侧 */}
                <button
                  type="button"
                  className="sm:hidden inline-flex items-center justify-center p-2 rounded-md focus:outline-none"
                  aria-expanded={mobileMenuOpen}
                  onClick={toggleMobileMenu}
                >
                  <span className="sr-only">Open main menu</span>
                  {/* 汉堡菜单 / X 图标 - 未展开时改为两根横线 */}
                  <div className="relative w-5 h-5 flex items-center justify-center">
                    <span
                      className={`absolute block h-0.5 w-4 bg-current transform transition duration-300 ease-in-out rounded-full ${
                        mobileMenuOpen
                          ? "text-foreground rotate-45"
                          : "text-foreground/30 -translate-y-1"
                      }`}
                    ></span>
                    {mobileMenuOpen && (
                      <span
                        className={`absolute block h-0.5 w-4 bg-current transition duration-300 ease-in-out text-foreground opacity-0 rounded-full`}
                      ></span>
                    )}
                    <span
                      className={`absolute block h-0.5 w-4 bg-current transform transition duration-300 ease-in-out rounded-full ${
                        mobileMenuOpen
                          ? "text-foreground -rotate-45"
                          : "text-foreground/30 translate-y-1"
                      }`}
                    ></span>
                  </div>
                </button>
              </div>
            </div>

            {/* 移动端导航菜单 - 优化过渡动画 */}
            <div
              className="md:hidden overflow-hidden will-change-[height,opacity]"
              style={{
                height: `${menuHeight}px`,
                opacity: menuHeight > 0 ? 1 : 0,
                visibility: menuHeight > 0 ? "visible" : "hidden",
                transition: `
                  height 0.35s cubic-bezier(0.16, 1, 0.3, 1), 
                  opacity 0.25s cubic-bezier(0.33, 1, 0.68, 1),
                  visibility 0s ${menuHeight > 0 ? "0s" : "0.35s"}
                `,
              }}
            >
              <div ref={menuRef} className="mt-4">
                <div className="px-2 pt-2 pb-3 space-y-1">
                  {navLinks.map((link, index) => (
                    <motion.div
                      key={link.name}
                      initial={false}
                      animate={{
                        opacity: mobileMenuOpen ? 1 : 0,
                        y: mobileMenuOpen ? 0 : -8,
                        transition: {
                          duration: 0.2,
                          delay: mobileMenuOpen ? 0.03 * index + 0.1 : 0,
                          ease: [0.33, 1, 0.68, 1],
                        },
                      }}
                      style={{
                        pointerEvents: mobileMenuOpen ? "auto" : "none",
                      }}
                    >
                      {/* 使用a标签替代Link组件，确保正确跳转 */}
                      <a
                        href={link.href}
                        className={`${
                          link.current
                            ? "text-foreground dark:text-foreground"
                            : "text-foreground/60 hover:text-foreground dark:text-foreground/60 dark:hover:text-foreground"
                        } block px-3 py-2 rounded-md font-bold text-base transition-colors duration-200`}
                        onClick={() => {
                          handleNavClick(index);
                        }}
                        aria-current={link.current ? "page" : undefined}
                      >
                        {link.name}
                      </a>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </nav>
      </div>
    </div>
  );
};

export default Navigator;
