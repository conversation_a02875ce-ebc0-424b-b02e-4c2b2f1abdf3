import React from "react";
import Container from "../Container";
import Logo from "../Navbar/Logo";

// 定义链接项接口
interface FooterLinkItem {
  label: string;
  href: string;
}

// 定义链接分组接口
interface FooterLinkGroup {
  title: string;
  links: FooterLinkItem[];
}

// 公司信息接口
interface CompanyInfo {
  name: string;
  copyright: string;
  logoProps: {
    width: number;
    height: number;
    className: string;
  };
}

// 页脚数据接口
interface FooterData {
  companyInfo: CompanyInfo;
  linkGroups: FooterLinkGroup[];
}

// 默认数据
const defaultFooterData: FooterData = {
  companyInfo: {
    name: "FRAMESOUND TECH CO., LTD.",
    copyright: "© 2025",
    logoProps: {
      width: 120,
      height: 28,
      className: "h-[20px] w-auto",
    },
  },
  linkGroups: [
    {
      title: "Resources",
      links: [
        { label: "Documentation", href: "#" },
        { label: "Tutorials", href: "#" },
        { label: "Support", href: "#" },
      ],
    },
    {
      title: "Company",
      links: [
        { label: "About", href: "#" },
        { label: "Careers", href: "#" },
        { label: "Blog", href: "#" },
      ],
    },
  ],
};

interface FooterProps {
  data?: FooterData;
}

export default function Footer({ data = defaultFooterData }: FooterProps) {
  const { companyInfo, linkGroups } = data;

  return (
    <>
      <footer className="bg-background text-foreground pt-16 pb-16">
        <Container>
          {/* 内容 */}
          <div className="flex flex-col md:flex-row justify-between">
            <div className="mb-6 md:mb-0">
              <div className="mb-4">
                <Logo
                  width={companyInfo.logoProps.width}
                  height={companyInfo.logoProps.height}
                  className={companyInfo.logoProps.className}
                  forceIconColor="foreground"
                />
              </div>
              <p className="text-foreground/40 max-w-xs text-[15px]">
                {companyInfo.copyright} {companyInfo.name}
              </p>
            </div>
            <div className="grid grid-cols-2 gap-2 md:gap-10">
              {linkGroups.map((group, groupIndex) => (
                <div key={groupIndex}>
                  <h4 className="text-md font-extrabold mb-4">{group.title}</h4>
                  <ul className="space-y-4 text-sm">
                    {group.links.map((link, linkIndex) => (
                      <li key={linkIndex}>
                        <a
                          href={link.href}
                          className="text-foreground/40 hover:text-foreground transition-colors"
                        >
                          {link.label}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </footer>
    </>
  );
}
