"use client";

import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X } from "lucide-react";

interface QRCodeModalProps {
  isVisible: boolean;
  onClose: () => void;
  title?: string;
  subtitle?: string;
}

export default function QRCodeModal({
  isVisible,
  onClose,
  title = "扫码咨询企业版",
  subtitle = "微信扫一扫，获取企业版专属方案",
}: QRCodeModalProps) {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 bg-black/70 z-[100] flex items-center justify-center"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-white dark:bg-gray-800 p-6 rounded-xl max-w-xs w-full mx-4 relative"
            onClick={(e) => e.stopPropagation()} // 防止点击内容区域时关闭
          >
            {/* 关闭按钮 */}
            <button
              onClick={onClose}
              className="absolute -top-12 right-0 text-white hover:text-gray-200 bg-black/30 hover:bg-black/50 p-2 rounded-full transition-all"
              aria-label="关闭"
            >
              <X className="w-6 h-6" />
            </button>

            {/* 二维码（灰色方块代替） */}
            <div className="w-full aspect-square bg-gray-300 dark:bg-gray-600 rounded-md mb-4"></div>

            {/* 二维码说明文字 */}
            <p className="text-center text-sm text-gray-700 dark:text-gray-300 font-medium">
              {title}
            </p>
            <p className="text-center text-xs text-gray-500 dark:text-gray-400 mt-1">
              {subtitle}
            </p>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
