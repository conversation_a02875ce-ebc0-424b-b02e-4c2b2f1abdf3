"use client";

import { useEffect, useState, useRef } from "react";
import { createPortal } from "react-dom";
import { cn } from "@/lib/utils";
import Container from "../Container";
import AnimatedBackground from "./AnimatedBackground";

export interface NavTabsItem {
  id: string;
  label: string;
  targetId?: string; // 添加目标元素ID属性，用于滚动跳转
}

interface NavTabsProps {
  items: NavTabsItem[];
  defaultTab?: string;
  className?: string;
  onTabChange?: (tabId: string | null) => void;
}

export default function NavTabs({
  items,
  defaultTab,
  className,
  onTabChange,
}: NavTabsProps) {
  // 状态管理
  const [activeTab, setActiveTab] = useState<string | null>(defaultTab || null);
  const [programmaticScrolling, setProgrammaticScrolling] = useState(false);
  const [navbarHeight, setNavbarHeight] = useState(0);
  const [isSticky, setIsSticky] = useState(false);
  const [shouldHide, setShouldHide] = useState(false);
  const scrollEndTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const tabsRef = useRef<HTMLDivElement>(null);

  // 处理标签点击，实现滚动跳转
  const handleTabClick = (tabId: string | null) => {
    setActiveTab(tabId);

    if (onTabChange) {
      onTabChange(tabId);
    }

    // 设置程序化滚动标志，防止滚动监听触发切换
    setProgrammaticScrolling(true);

    // 如果有tabId，查找对应的item
    if (tabId) {
      const selectedItem = items.find((item) => item.id === tabId);
      if (selectedItem && selectedItem.targetId) {
        // 查找目标元素
        const targetElement = document.getElementById(selectedItem.targetId);
        if (targetElement) {
          // 计算滚动位置，考虑导航栏高度和额外偏移
          const offset = navbarHeight + 80; // 点击切换 tab 时的定位偏移量

          // 平滑滚动到目标位置
          window.scrollTo({
            top: targetElement.offsetTop - offset,
            behavior: "smooth",
          });
        }
      }
    }
  };

  // 获取导航栏高度
  const calculateNavbarHeight = () => {
    const navbar = document.querySelector("nav");
    if (navbar) {
      const navbarRect = navbar.getBoundingClientRect();
      const totalHeight = navbarRect.height;
      setNavbarHeight(totalHeight);
      return totalHeight;
    }
    return 60; // 默认高度
  };

  // 监听滚动，实现自动选中标签和吸附效果
  useEffect(() => {
    if (typeof window === "undefined") return;

    // 初始化导航栏高度和标签栏位置
    calculateNavbarHeight();
    let tabsOffsetTop = 0;

    const updateTabsPosition = () => {
      if (tabsRef.current) {
        tabsOffsetTop =
          tabsRef.current.getBoundingClientRect().top + window.scrollY;
      }
    };

    updateTabsPosition();

    // 滚动监听处理
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const currentNavbarHeight = calculateNavbarHeight();

      // 检测是否应该吸附
      const shouldStick = scrollY >= tabsOffsetTop - currentNavbarHeight;
      if (shouldStick !== isSticky) {
        setIsSticky(shouldStick);
      }

      // 检测是否应该隐藏（当滚动超过最后一个内容块时）
      const shouldHideNavTabs = checkShouldHide(scrollY, currentNavbarHeight);
      if (shouldHideNavTabs !== shouldHide) {
        setShouldHide(shouldHideNavTabs);
      }

      // 检测滚动是否停止（用于重置程序化滚动状态）
      if (programmaticScrolling) {
        // 清除之前的定时器
        if (scrollEndTimeoutRef.current) {
          clearTimeout(scrollEndTimeoutRef.current);
        }

        // 设置新的定时器，如果150ms内没有新的滚动事件，认为滚动结束
        scrollEndTimeoutRef.current = setTimeout(() => {
          setProgrammaticScrolling(false);
        }, 150);
      }

      // 如果不是程序化滚动，则检查当前滚动位置并更新活动标签
      if (!programmaticScrolling) {
        updateActiveTabBasedOnScroll(scrollY, currentNavbarHeight);
      }
    };

    // 检测是否应该隐藏 NavTabs（当滚动超过最后一个内容块时）
    const checkShouldHide = (scrollY: number, navHeight: number) => {
      // 获取所有目标元素
      const targetElements = items
        .filter((item) => item.targetId)
        .map((item) => document.getElementById(item.targetId || ""))
        .filter((element): element is HTMLElement => element !== null);

      if (targetElements.length === 0) return false;

      // 找到最后一个元素
      const lastElement = targetElements[targetElements.length - 1];
      if (!lastElement) return false;

      // 计算最后一个元素的底部位置
      const lastElementBottom =
        lastElement.offsetTop + lastElement.offsetHeight;

      // 如果滚动位置超过最后一个元素的底部，则隐藏 NavTabs
      return scrollY > lastElementBottom - navHeight - 300; // 100px 缓冲区
    };

    // 根据滚动位置更新活动标签
    const updateActiveTabBasedOnScroll = (
      scrollY: number,
      navHeight: number
    ) => {
      // 计算偏移量，考虑导航栏高度
      const offset = navHeight + 300; // 手动滚动页面时自动提前切换 tab 的偏移量

      // 获取所有目标元素的位置
      const targetPositions = items
        .filter((item) => item.targetId)
        .map((item) => {
          const element = document.getElementById(item.targetId || "");
          if (element) {
            return {
              id: item.id,
              top: element.offsetTop - offset,
              bottom: element.offsetTop + element.offsetHeight - offset,
            };
          }
          return null;
        })
        .filter(
          (pos): pos is { id: string; top: number; bottom: number } =>
            pos !== null
        );

      // 找到当前视口中的元素
      let currentTabId = null;
      for (const pos of targetPositions) {
        if (pos && scrollY >= pos.top && scrollY < pos.bottom) {
          currentTabId = pos.id;
          break;
        }
      }

      // 如果没有找到匹配的元素，但滚动位置在第一个元素之前，选择第一个标签
      if (!currentTabId && targetPositions.length > 0) {
        const firstTarget = targetPositions[0];
        if (firstTarget && scrollY < firstTarget.top) {
          currentTabId = firstTarget.id;
        }
      }

      // 如果没有找到匹配的元素，但滚动位置在最后一个元素之后，选择最后一个标签
      if (!currentTabId && targetPositions.length > 0) {
        const lastTarget = targetPositions[targetPositions.length - 1];
        if (lastTarget && scrollY >= lastTarget.bottom) {
          currentTabId = lastTarget.id;
        }
      }

      // 更新活动标签
      if (currentTabId && currentTabId !== activeTab) {
        setActiveTab(currentTabId);
        if (onTabChange) {
          onTabChange(currentTabId);
        }
      }
    };

    // 添加监听
    window.addEventListener("scroll", handleScroll);
    window.addEventListener("resize", () => {
      calculateNavbarHeight();
      updateTabsPosition();
    });

    // 清理事件监听
    return () => {
      window.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", () => {
        calculateNavbarHeight();
        updateTabsPosition();
      });
      if (scrollEndTimeoutRef.current) {
        clearTimeout(scrollEndTimeoutRef.current);
      }
    };
  }, [
    items,
    activeTab,
    programmaticScrolling,
    isSticky,
    shouldHide,
    onTabChange,
  ]);

  // 标签内容组件
  const tabContent = (
    <div className={cn("flex justify-center", className)}>
      <div
        className={cn(
          "inline-flex p-1.5 rounded-lg transition-all duration-300",
          isSticky
            ? "bg-gray-200/90 dark:bg-gray-700/95 backdrop-blur-md"
            : "bg-gray-200/60 dark:bg-gray-700/80"
        )}
      >
        <AnimatedBackground
          key="nav-tabs-animated-background"
          className="bg-black dark:bg-gray-900 rounded-lg"
          defaultValue={activeTab || undefined}
          onValueChange={handleTabClick}
        >
          {items.map((item) => (
            <button
              key={item.id}
              data-id={item.id}
              className="px-4 py-2.5 text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors duration-200 hover:text-gray-900 dark:hover:text-white data-[checked=true]:text-enterprise-buttonText"
            >
              {item.label}
            </button>
          ))}
        </AnimatedBackground>
      </div>
    </div>
  );

  return (
    <div
      className={cn(
        "hidden md:block transition-all duration-300",
        shouldHide && "opacity-0 pointer-events-none"
      )}
    >
      {/* 背景容器 */}
      <div
        ref={tabsRef}
        className="bg-white dark:bg-gray-900 py-4 transition-all duration-300"
      >
        <Container>
          {/* 正常状态的标签栏 */}
          {!isSticky && tabContent}
          {/* 吸附状态时的占位符 */}
          {isSticky && <div style={{ height: "56px" }} />}
        </Container>
      </div>

      {/* 吸附状态的标签栏 - 使用 Portal 渲染到 body */}
      {isSticky &&
        typeof window !== "undefined" &&
        createPortal(
          <div
            className="fixed z-50 left-0 right-0 py-4"
            style={{
              top: `${navbarHeight}px`,
              opacity: shouldHide ? 0 : 1,
              pointerEvents: shouldHide ? "none" : "auto",
              transition: "opacity 300ms ease-in-out",
            }}
          >
            <Container>{tabContent}</Container>
          </div>,
          document.body
        )}
    </div>
  );
}
