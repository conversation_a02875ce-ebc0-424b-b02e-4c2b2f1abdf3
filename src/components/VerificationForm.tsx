"use client";

import React, { useState, useEffect, useRef } from "react";
import { ChevronLeft } from "lucide-react";
import FormCard from "./ui/FormCard";
import { AuthClient } from "@quote/auth-client";

// 创建认证客户端实例
const authClient = new AuthClient({
  authServiceApiUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL!,
  authServiceRedirectUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL!,
  timeout: 10000,
});

// 添加自定义的闪烁光标动画
const BlinkingCursorAnimation = () => (
  <style jsx global>{`
    @keyframes cursor-blink {
      0%,
      40% {
        opacity: 1;
      }
      50%,
      90% {
        opacity: 0;
      }
    }
    .cursor-blink {
      animation: cursor-blink 1.2s infinite;
    }
  `}</style>
);

interface VerificationFormProps {
  contact: string; // 只接收邮箱
  onBack: () => void;
  onSuccess?: (redirectUrl?: string) => void;
}

const VerificationForm: React.FC<VerificationFormProps> = ({
  contact,
  onBack,
  onSuccess,
}) => {
  const [code, setCode] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isVerified, setIsVerified] = useState(false); // 新增状态，标记验证是否已成功
  const [countdown, setCountdown] = useState(60); // 默认60秒倒计时
  const [resending, setResending] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const verifyRequestRef = useRef<boolean>(false); // 用于防止重复请求

  // 处理验证码输入变化
  const handleCodeChange = (value: string) => {
    // 如果已经验证成功，不再允许修改验证码
    if (isVerified) return;

    setCode(value);
    setError(null);
  };

  // 处理倒计时
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [countdown]);

  // 重新发送验证码
  const handleResendCode = async () => {
    // 如果已经验证成功，不再允许重发验证码
    if (isVerified || resending || countdown > 0) return;

    try {
      setResending(true);
      setError(null);

      await authClient.apiSendVerificationCode(contact);

      // 重置倒计时
      setCountdown(60);
    } catch (err: any) {
      // 处理API错误响应
      if (err.response && err.response.data) {
        const { detail } = err.response.data;
        setError(
          detail || "Failed to send verification code. Please try again."
        );

        // 如果错误消息包含等待时间，提取并设置倒计时
        const waitTimeMatch = detail && detail.match(/等待(\d+)秒/);
        if (waitTimeMatch && waitTimeMatch[1]) {
          setCountdown(parseInt(waitTimeMatch[1]));
        }
      } else {
        setError("Failed to send verification code. Please try again.");
      }
    } finally {
      setResending(false);
    }
  };

  // 验证验证码
  const handleVerify = async () => {
    // 如果已经验证成功或正在加载，不再重复验证
    if (isVerified || isLoading || verifyRequestRef.current) return;

    if (!code.trim()) {
      setError("Please enter verification code");
      return;
    }

    if (code.length !== 6) {
      setError("Please enter 6-digit verification code");
      return;
    }

    // 设置请求标识，防止重复请求
    verifyRequestRef.current = true;

    // 添加请求标识，用于跟踪
    const requestId = Date.now();
    console.log(
      `[验证码校验] 开始请求 ID: ${requestId}, 邮箱: ${contact}, 验证码长度: ${code.length}`
    );

    try {
      setIsLoading(true);
      setError(null);

      // 调用登录或注册API
      console.log(`[验证码校验] 调用 signInOrUp API, 请求 ID: ${requestId}`);
      const response = await authClient.signInOrUp(contact, code);
      console.log(
        `[验证码校验] API 调用成功, 请求 ID: ${requestId}, 响应:`,
        response
      );

      // 标记验证已成功
      setIsVerified(true);

      // 如果没有抛出异常，说明验证成功 (状态码 200 或 201)
      if (onSuccess) {
        console.log(`[验证码校验] 调用 onSuccess 回调, 请求 ID: ${requestId}`);
        // 使用setTimeout确保状态更新后再调用onSuccess
        setTimeout(() => {
          onSuccess();
        }, 0);
      } else {
        // 默认重定向到根路径
        console.log(`[验证码校验] 默认重定向到首页, 请求 ID: ${requestId}`);
        window.location.href = "/";
      }
    } catch (err: any) {
      // 详细记录错误信息
      console.error(`[验证码校验] 失败, 请求 ID: ${requestId}, 错误:`, {
        status: err?.response?.status,
        statusText: err?.response?.statusText,
        data: err?.response?.data,
        message: err?.message,
        stack: err?.stack,
        name: err?.name,
        fullError: err,
      });

      // 根据 HTTP 状态码处理不同类型的错误
      const status = err?.response?.status;

      if (status === 400) {
        setError("Invalid email format. Please check your email address.");
      } else if (status === 460) {
        setError("Incorrect verification code. Please try again.");
      } else if (status === 461) {
        setError(
          "Verification code expired or doesn't exist. Please request a new one."
        );
      } else if (status === 500) {
        setError("System error. Please try again later.");
      } else if (err?.response?.data?.detail) {
        // 如果有详细错误信息，优先使用
        setError(err.response.data.detail);
      } else {
        // 通用错误处理
        setError(
          err instanceof Error
            ? err.message
            : "Verification failed. Please try again."
        );
      }

      // 验证失败后清空验证码
      setCode("");
    } finally {
      setIsLoading(false);
      // 重置请求标识
      verifyRequestRef.current = false;
      console.log(`[验证码校验] 请求完成, ID: ${requestId}`);
    }
  };

  // 当验证码长度为6位时自动验证
  useEffect(() => {
    // 只有在未验证成功且验证码长度为6位时才自动验证
    if (
      code.length === 6 &&
      !isLoading &&
      !isVerified &&
      !verifyRequestRef.current
    ) {
      handleVerify();
    }
  }, [code, isLoading, isVerified]);

  // 聚焦输入框
  const focusInput = () => {
    // 如果已经验证成功，不再允许聚焦
    if (isVerified) return;
    inputRef.current?.focus();
  };

  return (
    <>
      {/* 引入自定义光标动画 */}
      <BlinkingCursorAnimation />
      <FormCard>
        <button
          type="button"
          onClick={onBack}
          className="mb-4 text-gray-400 hover:text-primary transition-colors flex items-center gap-0.5 text-sm group"
          disabled={isVerified || isLoading}
        >
          <ChevronLeft className="w-4 h-4 group-hover:scale-110 transition-transform" />
          <span>Back</span>
        </button>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">验证邮箱</h2>
        <p className="text-gray-500 mb-8">验证码已发送至 {contact}</p>

        <div className="space-y-6 flex-grow flex flex-col">
          {/* 成功提示 */}
          {isVerified && (
            <div className="p-3 bg-green-50 border border-green-200 rounded-md text-green-600 text-sm">
              验证成功，正在跳转...
            </div>
          )}

          {/* 错误提示 */}
          {error && !isVerified && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md text-red-600 text-sm">
              {error}
            </div>
          )}

          {/* 验证码输入区域 */}
          <div
            onClick={focusInput}
            className={`cursor-text ${
              isVerified ? "opacity-50 pointer-events-none" : ""
            }`}
          >
            {/* 使用标准HTML input元素处理输入 */}
            <input
              ref={inputRef}
              type="text"
              inputMode="numeric"
              autoFocus
              maxLength={6}
              value={code}
              onChange={(e) => handleCodeChange(e.target.value)}
              className="absolute opacity-0 h-0 w-0"
              disabled={isLoading || isVerified}
            />

            {/* 显示用的自定义布局 */}
            <div className="flex items-center justify-center gap-4 w-full">
              {/* 前三个输入框 */}
              <div
                className={`h-12 w-12 border ${
                  code.length === 0 && !isVerified
                    ? "border-primary"
                    : "border-gray-300"
                } rounded-md flex items-center justify-center text-base shadow-none relative`}
              >
                {code[0] || ""}
                {code.length === 0 && !isVerified && (
                  <div className="w-[1px] h-5 bg-gray-500 cursor-blink absolute"></div>
                )}
              </div>
              <div
                className={`h-12 w-12 border ${
                  code.length === 1 && !isVerified
                    ? "border-primary"
                    : "border-gray-300"
                } rounded-md flex items-center justify-center text-base shadow-none relative`}
              >
                {code[1] || ""}
                {code.length === 1 && !isVerified && (
                  <div className="w-[1px] h-5 bg-gray-500 cursor-blink absolute"></div>
                )}
              </div>
              <div
                className={`h-12 w-12 border ${
                  code.length === 2 && !isVerified
                    ? "border-primary"
                    : "border-gray-300"
                } rounded-md flex items-center justify-center text-base shadow-none relative`}
              >
                {code[2] || ""}
                {code.length === 2 && !isVerified && (
                  <div className="w-[1px] h-5 bg-gray-500 cursor-blink absolute"></div>
                )}
              </div>

              {/* 分隔符 */}
              <div className="w-4 h-0.5 bg-gray-300"></div>

              {/* 后三个输入框 */}
              <div
                className={`h-12 w-12 border ${
                  code.length === 3 && !isVerified
                    ? "border-primary"
                    : "border-gray-300"
                } rounded-md flex items-center justify-center text-base shadow-none relative`}
              >
                {code[3] || ""}
                {code.length === 3 && !isVerified && (
                  <div className="w-[1px] h-5 bg-gray-500 cursor-blink absolute"></div>
                )}
              </div>
              <div
                className={`h-12 w-12 border ${
                  code.length === 4 && !isVerified
                    ? "border-primary"
                    : "border-gray-300"
                } rounded-md flex items-center justify-center text-base shadow-none relative`}
              >
                {code[4] || ""}
                {code.length === 4 && !isVerified && (
                  <div className="w-[1px] h-5 bg-gray-500 cursor-blink absolute"></div>
                )}
              </div>
              <div
                className={`h-12 w-12 border ${
                  code.length === 5 && !isVerified
                    ? "border-primary"
                    : "border-gray-300"
                } rounded-md flex items-center justify-center text-base shadow-none relative`}
              >
                {code[5] || ""}
                {code.length === 5 && !isVerified && (
                  <div className="w-[1px] h-5 bg-gray-500 cursor-blink absolute"></div>
                )}
              </div>
            </div>
          </div>

          <div className="mt-auto pt-2">
            <button
              onClick={handleResendCode}
              disabled={countdown > 0 || resending || isVerified}
              className={`text-sm transition-colors px-0 ${
                countdown > 0 || resending || isVerified
                  ? "text-gray-400"
                  : "text-[#443AA7]/70 hover:text-[#443AA7]"
              }`}
            >
              {resending
                ? "发送中..."
                : countdown > 0
                ? `${countdown}秒后重新发送`
                : "获取验证码"}
            </button>
          </div>
        </div>
      </FormCard>
    </>
  );
};

export default VerificationForm;
