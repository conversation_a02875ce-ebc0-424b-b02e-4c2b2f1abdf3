import { useState, useEffect } from "react";
import FingerprintJS from "@fingerprintjs/fingerprintjs";

/**
 * 使用浏览器指纹和设备ID的自定义Hook
 * @returns 浏览器指纹和设备ID信息
 */
export function useFingerprint() {
  const [fingerprint, setFingerprint] = useState<string>("");
  const [deviceId, setDeviceId] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const loadFingerprint = async () => {
      try {
        // 从本地存储获取设备ID
        const storedDeviceId = localStorage.getItem("device_id");
        if (storedDeviceId) {
          setDeviceId(storedDeviceId);
        } else {
          // 生成新的设备ID（UUID）
          const newDeviceId = crypto.randomUUID();
          localStorage.setItem("device_id", newDeviceId);
          setDeviceId(newDeviceId);
        }

        // 获取浏览器指纹
        const fp = await FingerprintJS.load();
        const result = await fp.get();
        setFingerprint(result.visitorId);
        setIsLoading(false);
      } catch (err: any) {
        setError(err);
        setIsLoading(false);
        console.error("获取浏览器指纹失败:", err);
      }
    };

    loadFingerprint();
  }, []);

  return { fingerprint, deviceId, isLoading, error };
}
