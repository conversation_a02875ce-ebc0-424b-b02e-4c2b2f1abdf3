import { useAuth } from "@quote/auth-client/react";
import { useCallback, useEffect } from "react";

/**
 * 统一处理认证相关的操作，包括登录和登出
 * 确保整个应用中认证逻辑保持一致
 */
export function useAuthActions() {
  const authHook = useAuth();
  const {
    isAuthenticated,
    user,
    loading,
    error,
    statusCode,
    login,
    logout,
    refreshUser,
    verifyStatus,
  } = authHook;

  // 检查SSO重定向状态
  const checkSSORedirect = (authHook as any).checkSSORedirect;

  /**
   * 处理用户登录
   * 统一的登录流程：调用认证SDK的login方法，成功后刷新页面
   */
  const handleLogin = useCallback(() => {
    try {
      console.log("开始登录...");
      login();

      // 登录成功后刷新页面以确保状态更新
      setTimeout(() => {
        console.log("登录成功，刷新页面以更新状态");
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error("登录失败:", error);
    }
  }, [login]);

  /**
   * 处理用户登出
   * 统一的登出流程：调用认证SDK的logout方法，会自动清除localStorage中的用户信息
   */
  const handleLogout = useCallback(async () => {
    try {
      console.log("开始退出登录...");
      await logout();
      console.log("已成功退出登录");
    } catch (error) {
      console.error("退出登录失败:", error);
    }
  }, [logout]);

  /**
   * 刷新用户信息
   */
  const refreshUserInfo = useCallback(async () => {
    try {
      await refreshUser();
    } catch (error) {
      console.error("刷新用户信息失败:", error);
    }
  }, [refreshUser]);

  /**
   * 检查认证状态
   */
  const checkAuthStatus = useCallback(async () => {
    try {
      const result = await verifyStatus();
      return result;
    } catch (error) {
      console.error("检查认证状态失败:", error);
      return { success: false, message: "检查认证状态失败", statusCode: 500 };
    }
  }, [verifyStatus]);

  /**
   * 获取认证状态的文本描述
   * 基于状态码返回对应的状态描述
   */
  const getAuthStatusMessage = useCallback(() => {
    if (!statusCode) return "";

    switch (statusCode) {
      case 200:
        return "用户认证验证成功，欢迎使用";
      case 401:
        return "登录已过期，请重新登录";
      case 500:
        return "认证服务异常";
      default:
        return "未知认证状态";
    }
  }, [statusCode]);

  /**
   * 检查登录是否已过期
   * 当状态码为401时表示登录已过期
   */
  const isLoginExpired = useCallback(() => {
    return statusCode === 401;
  }, [statusCode]);

  // 监听认证状态变化
  useEffect(() => {
    if (error) {
      console.error("认证错误:", error);
    }
  }, [error]);

  return {
    isAuthenticated,
    user,
    loading,
    error,
    statusCode,
    login,
    logout,
    handleLogin,
    handleLogout,
    refreshUserInfo,
    checkAuthStatus,
    getAuthStatusMessage,
    isLoginExpired,
    checkSSORedirect,
    verifyStatus,
  };
}

export default useAuthActions;
