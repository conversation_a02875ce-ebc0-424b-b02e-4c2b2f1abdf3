"use client";

import { useEffect, useCallback, useRef, useState } from "react";
import { useDocumentContext, TreeNode } from "@/contexts/DocumentContext";
import logger from "@/utils/logger";

// 预加载配置
interface PreloadConfig {
  maxCacheSize: number; // 最大缓存数量
  maxConcurrentRequests: number; // 最大并发请求数
  preloadDelay: number; // 预加载延迟（ms）
  enableNetworkAwarePreload: boolean; // 是否启用网络感知预加载
}

// 默认配置
const DEFAULT_CONFIG: PreloadConfig = {
  maxCacheSize: 20,
  maxConcurrentRequests: 3,
  preloadDelay: 1000,
  enableNetworkAwarePreload: true,
};

// 扁平化文档树，获取所有文档的有序列表
const flattenDocumentTree = (
  nodes: TreeNode[]
): Array<{ id: string; title: string }> => {
  const result: Array<{ id: string; title: string }> = [];

  for (const node of nodes) {
    if (node.type === "folder" && node.children) {
      // 对子文档按 order 排序
      const sortedChildren = [...node.children].sort((a, b) => {
        const orderA = (a as any).order || 999;
        const orderB = (b as any).order || 999;
        return orderA - orderB;
      });

      for (const child of sortedChildren) {
        if (child.type === "file") {
          result.push({
            id: child.id,
            title: child.title,
          });
        }
      }
    }
  }

  return result;
};

// 网络状况检测
const getNetworkInfo = () => {
  if (typeof navigator !== "undefined" && "connection" in navigator) {
    const connection = (navigator as any).connection;
    return {
      effectiveType: connection?.effectiveType || "4g",
      downlink: connection?.downlink || 10,
      saveData: connection?.saveData || false,
    };
  }
  return { effectiveType: "4g", downlink: 10, saveData: false };
};

// 判断是否应该预加载
const shouldPreload = (networkInfo: ReturnType<typeof getNetworkInfo>) => {
  if (networkInfo.saveData) return false; // 用户启用了数据节省模式
  if (
    networkInfo.effectiveType === "slow-2g" ||
    networkInfo.effectiveType === "2g"
  )
    return false;
  if (networkInfo.downlink < 1.5) return false; // 网络速度太慢
  return true;
};

export function useDocumentPreloader(
  currentDocId: string,
  contentCache: Map<string, any>,
  config: Partial<PreloadConfig> = {}
) {
  const { treeData } = useDocumentContext();
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const preloadQueueRef = useRef<Set<string>>(new Set());
  const activeRequestsRef = useRef<Set<string>>(new Set());
  const preloadTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 添加状态强制更新
  const [, forceUpdate] = useState({});

  // 预加载单个文档
  const preloadDocument = useCallback(
    async (docId: string) => {
      // 检查是否已在缓存中
      if (contentCache.has(docId)) {
        return;
      }

      // 检查是否已在请求中
      if (activeRequestsRef.current.has(docId)) {
        return;
      }

      // 检查并发请求限制
      if (activeRequestsRef.current.size >= finalConfig.maxConcurrentRequests) {
        return;
      }

      try {
        activeRequestsRef.current.add(docId);

        const response = await fetch(`/api/docs/content/${docId}`, {
          priority: "low", // 低优先级请求
        } as any);

        if (response.ok) {
          const data = await response.json();

          // 检查缓存大小限制
          if (contentCache.size >= finalConfig.maxCacheSize) {
            // 删除最旧的缓存项（简单的 LRU 策略）
            const firstKey = contentCache.keys().next().value;
            if (firstKey) {
              contentCache.delete(firstKey);
            }
          }

          contentCache.set(docId, data);
          logger.log(
            `📄 Preloaded document: ${docId} (Cache size: ${contentCache.size})`
          );

          // 触发状态更新以反映缓存变化
          forceUpdate({});
        }
      } catch (error) {
        logger.warn(`Failed to preload document ${docId}:`, error);
      } finally {
        activeRequestsRef.current.delete(docId);
        preloadQueueRef.current.delete(docId);
      }
    },
    [
      contentCache,
      finalConfig.maxConcurrentRequests,
      finalConfig.maxCacheSize,
      forceUpdate,
    ]
  );

  // 处理预加载队列
  const processPreloadQueue = useCallback(() => {
    const networkInfo = getNetworkInfo();
    logger.log("🌐 Network info:", networkInfo);

    if (!shouldPreload(networkInfo)) {
      logger.log(
        "🚫 Preloading disabled due to network conditions",
        networkInfo
      );
      return;
    }

    logger.log("✅ Network conditions OK, proceeding with preload");

    // 使用 requestIdleCallback 在浏览器空闲时预加载
    const schedulePreload = (callback: () => void) => {
      if (typeof window !== "undefined" && "requestIdleCallback" in window) {
        window.requestIdleCallback(callback, { timeout: 5000 });
      } else {
        setTimeout(callback, 0);
      }
    };

    schedulePreload(() => {
      const queueArray = Array.from(preloadQueueRef.current);
      const availableSlots =
        finalConfig.maxConcurrentRequests - activeRequestsRef.current.size;

      for (let i = 0; i < Math.min(availableSlots, queueArray.length); i++) {
        const docId = queueArray[i];
        preloadDocument(docId);
      }
    });
  }, [preloadDocument, finalConfig.maxConcurrentRequests]);

  // 获取相邻文档进行预加载
  const getAdjacentDocuments = useCallback(
    (currentId: string) => {
      if (!treeData.length || currentId.startsWith("folder-")) {
        return [];
      }

      const flatDocs = flattenDocumentTree(treeData);
      const currentIndex = flatDocs.findIndex((doc) => doc.id === currentId);

      if (currentIndex === -1) {
        return [];
      }

      const adjacent: string[] = [];

      // 添加前一篇和后一篇
      if (currentIndex > 0) {
        adjacent.push(flatDocs[currentIndex - 1].id);
      }
      if (currentIndex < flatDocs.length - 1) {
        adjacent.push(flatDocs[currentIndex + 1].id);
      }

      // 添加前后各2篇（更积极的预加载）
      if (currentIndex > 1) {
        adjacent.push(flatDocs[currentIndex - 2].id);
      }
      if (currentIndex < flatDocs.length - 2) {
        adjacent.push(flatDocs[currentIndex + 2].id);
      }

      return adjacent;
    },
    [treeData]
  );

  // 主预加载逻辑
  useEffect(() => {
    if (!currentDocId || !treeData.length) {
      return;
    }

    // 清除之前的定时器
    if (preloadTimeoutRef.current) {
      clearTimeout(preloadTimeoutRef.current);
    }

    // 延迟预加载，避免影响当前页面加载
    preloadTimeoutRef.current = setTimeout(() => {
      const adjacentDocs = getAdjacentDocuments(currentDocId);
      logger.log(
        `🚀 Starting preload for ${currentDocId}, adjacent docs:`,
        adjacentDocs
      );

      // 将相邻文档添加到预加载队列
      adjacentDocs.forEach((docId) => {
        if (!contentCache.has(docId)) {
          preloadQueueRef.current.add(docId);
          logger.log(`➕ Added to preload queue: ${docId}`);
        } else {
          logger.log(`✅ Already cached: ${docId}`);
        }
      });

      logger.log(`📋 Preload queue size: ${preloadQueueRef.current.size}`);

      // 处理预加载队列
      processPreloadQueue();
    }, finalConfig.preloadDelay);

    return () => {
      if (preloadTimeoutRef.current) {
        clearTimeout(preloadTimeoutRef.current);
      }
    };
  }, [
    currentDocId,
    treeData,
    getAdjacentDocuments,
    processPreloadQueue,
    finalConfig.preloadDelay,
    contentCache,
  ]);

  // 清理函数
  useEffect(() => {
    return () => {
      if (preloadTimeoutRef.current) {
        clearTimeout(preloadTimeoutRef.current);
      }
      // 取消所有进行中的请求
      activeRequestsRef.current.clear();
      preloadQueueRef.current.clear();
    };
  }, []);

  // 返回预加载状态信息（用于调试）
  return {
    cacheSize: contentCache.size,
    activeRequests: activeRequestsRef.current.size,
    queueSize: preloadQueueRef.current.size,
    maxCacheSize: finalConfig.maxCacheSize,
  };
}
