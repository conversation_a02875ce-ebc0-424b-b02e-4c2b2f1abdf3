import { useState, useEffect } from "react";

// 存储键名
const USER_STORAGE_KEY = "quote_user";

// 用户信息类型定义
export interface User {
  id: string;
  name?: string;
  email?: string;
  phone?: string;
  avatar?: string;
}

// 用户状态接口
export interface UserState {
  user: User | null;
  isLoggedIn: boolean;
  isLoading: boolean;
}

/**
 * 用户状态管理钩子
 * 处理用户登录状态和信息
 */
export function useUser() {
  // 客户端状态标志
  const [isClient, setIsClient] = useState(false);
  // 用户状态
  const [userState, setUserState] = useState<UserState>({
    user: null,
    isLoggedIn: false,
    isLoading: true,
  });

  // 客户端渲染标志
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 从本地存储恢复用户状态
  useEffect(() => {
    if (!isClient) return;

    try {
      const savedUser = localStorage.getItem(USER_STORAGE_KEY);
      if (savedUser) {
        const parsedUser = JSON.parse(savedUser) as User;
        setUserState({
          user: parsedUser,
          isLoggedIn: true,
          isLoading: false,
        });
      } else {
        setUserState({
          user: null,
          isLoggedIn: false,
          isLoading: false,
        });
      }
    } catch (error) {
      console.error("Failed to restore user state:", error);
      if (typeof localStorage !== "undefined") {
        localStorage.removeItem(USER_STORAGE_KEY);
      }
      setUserState({
        user: null,
        isLoggedIn: false,
        isLoading: false,
      });
    }
  }, [isClient]);

  /**
   * 设置用户信息并更新登录状态
   */
  const setUser = (user: User | null) => {
    if (user) {
      // 登录用户
      setUserState({
        user,
        isLoggedIn: true,
        isLoading: false,
      });

      if (isClient) {
        try {
          localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user));
        } catch (error) {
          console.error("Failed to save user state:", error);
        }
      }
    } else {
      // 注销用户
      setUserState({
        user: null,
        isLoggedIn: false,
        isLoading: false,
      });

      if (isClient) {
        try {
          localStorage.removeItem(USER_STORAGE_KEY);
        } catch (error) {
          console.error("Failed to clear user state:", error);
        }
      }
    }
  };

  /**
   * 注销当前用户
   */
  const logout = () => {
    setUser(null);
  };

  return {
    ...userState,
    setUser,
    logout,
  };
}
