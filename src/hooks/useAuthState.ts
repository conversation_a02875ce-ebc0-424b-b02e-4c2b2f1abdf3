import { useState, useEffect } from "react";

// 存储键名
const STORAGE_KEY = "quote_auth_state";

// 认证状态类型定义
export interface AuthState {
  step: "login" | "verification";
  contactInfo: {
    type: "email" | "phone";
    value: string;
  };
}

// 初始认证状态
const initialAuthState: AuthState = {
  step: "login",
  contactInfo: { type: "email", value: "" },
};

/**
 * 自定义Hook，用于管理认证状态和本地存储
 * 处理页面刷新时的状态恢复
 */
export function useAuthState() {
  // 状态
  const [state, setState] = useState<AuthState>(initialAuthState);
  const [isClient, setIsClient] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 客户端渲染标志
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 从本地存储恢复状态
  useEffect(() => {
    if (!isClient) return;

    try {
      const savedState = localStorage.getItem(STORAGE_KEY);
      if (savedState) {
        const parsedState = JSON.parse(savedState) as AuthState;
        setState(parsedState);
      }
    } catch (error) {
      console.error("Failed to restore auth state:", error);
      if (typeof localStorage !== "undefined") {
        localStorage.removeItem(STORAGE_KEY);
      }
    } finally {
      // 无论是否成功恢复状态，都将加载状态设为false
      setIsLoading(false);
    }
  }, [isClient]);

  // 保存状态到本地存储
  const saveState = (newState: AuthState) => {
    setState(newState);
    setIsLoading(false); // 确保状态更新后，加载状态为false

    if (isClient && newState.contactInfo.value) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(newState));
      } catch (error) {
        console.error("Failed to save auth state:", error);
      }
    }
  };

  // 清除状态
  const clearState = () => {
    setState(initialAuthState);

    if (isClient) {
      try {
        localStorage.removeItem(STORAGE_KEY);
      } catch (error) {
        console.error("Failed to clear auth state:", error);
      }
    }
  };

  // 更新当前步骤
  const setStep = (step: AuthState["step"]) => {
    saveState({ ...state, step });
  };

  // 更新联系信息
  const setContactInfo = (contactInfo: AuthState["contactInfo"]) => {
    saveState({ ...state, contactInfo });
  };

  // 进入验证阶段
  const goToVerification = (contactInfo: AuthState["contactInfo"]) => {
    saveState({
      step: "verification",
      contactInfo,
    });
  };

  return {
    ...state,
    isLoading,
    setStep,
    setContactInfo,
    goToVerification,
    clearState,
  };
}
