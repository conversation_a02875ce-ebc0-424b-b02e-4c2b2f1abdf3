"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";

// 文档树节点类型
export interface TreeNode {
  id: string;
  title: string;
  type: "folder" | "file";
  children?: TreeNode[];
  expanded?: boolean;
}

// 上下文类型
interface DocumentContextType {
  treeData: TreeNode[];
  setTreeData: (data: TreeNode[]) => void;
  loading: boolean;
  error: string | null;
  refreshTree: () => Promise<void>;
}

// 创建上下文
const DocumentContext = createContext<DocumentContextType | undefined>(
  undefined
);

// 上下文提供者组件属性
interface DocumentProviderProps {
  children: ReactNode;
}

export function DocumentProvider({ children }: DocumentProviderProps) {
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);

  // 检测客户端环境
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 加载文档树数据
  const loadTreeData = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);

      // 获取新数据
      const response = await fetch("/api/docs/tree");
      if (!response.ok) {
        throw new Error(`Error loading document tree: ${response.statusText}`);
      }

      const data = await response.json();

      // 更新状态
      setTreeData(data);
    } catch (err: any) {
      console.error("Failed to load document tree:", err);
      setError("无法加载文档目录，请稍后重试。");
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    if (isClient) {
      loadTreeData();
    }
  }, [isClient]);

  // 刷新树数据
  const refreshTree = async () => {
    await loadTreeData(true);
  };

  const value = {
    treeData,
    setTreeData,
    loading,
    error,
    refreshTree,
  };

  return (
    <DocumentContext.Provider value={value}>
      {children}
    </DocumentContext.Provider>
  );
}

// 自定义Hook，用于访问上下文
export function useDocumentContext() {
  const context = useContext(DocumentContext);
  if (context === undefined) {
    throw new Error(
      "useDocumentContext must be used within a DocumentProvider"
    );
  }
  return context;
}
